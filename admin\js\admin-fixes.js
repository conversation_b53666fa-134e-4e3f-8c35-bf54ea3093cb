// Admin Panel Fixes
// إصلاحات لوحة الإدارة

console.log('🔧 Loading admin panel fixes...');

// Fix 1: Prevent Multiple Auth Initializations
(function() {
    let authInitialized = false;
    
    const originalAdminAuth = window.AdminAuth;
    if (originalAdminAuth) {
        window.AdminAuth = function() {
            if (authInitialized) {
                console.log('AdminAuth already initialized, returning existing instance');
                return window.adminAuthInstance;
            }
            
            authInitialized = true;
            const instance = new originalAdminAuth();
            window.adminAuthInstance = instance;
            return instance;
        };
        
        // Copy prototype
        window.AdminAuth.prototype = originalAdminAuth.prototype;
    }
})();

// Fix 2: Prevent Infinite Redirects
(function() {
    let redirectCount = 0;
    let lastRedirectTime = 0;
    const REDIRECT_COOLDOWN = 2000; // 2 seconds
    
    const originalLocationHref = Object.getOwnPropertyDescriptor(Location.prototype, 'href');
    
    Object.defineProperty(Location.prototype, 'href', {
        set: function(url) {
            const now = Date.now();
            
            // Check if this is a rapid redirect
            if (now - lastRedirectTime < REDIRECT_COOLDOWN) {
                redirectCount++;
                if (redirectCount > 3) {
                    console.warn('Preventing infinite redirect to:', url);
                    console.warn('Redirect count:', redirectCount);
                    return;
                }
            } else {
                redirectCount = 0;
            }
            
            lastRedirectTime = now;
            console.log('Redirecting to:', url);
            
            // Call original setter
            originalLocationHref.set.call(this, url);
        },
        get: originalLocationHref.get
    });
})();

// Fix 3: Session Storage Stability
(function() {
    const originalSetItem = Storage.prototype.setItem;
    const originalGetItem = Storage.prototype.getItem;
    
    Storage.prototype.setItem = function(key, value) {
        try {
            if (key === 'admin_session') {
                console.log('Setting admin session:', JSON.parse(value).username);
            }
            return originalSetItem.call(this, key, value);
        } catch (error) {
            console.error('Error setting storage item:', error);
        }
    };
    
    Storage.prototype.getItem = function(key) {
        try {
            const result = originalGetItem.call(this, key);
            if (key === 'admin_session' && result) {
                const session = JSON.parse(result);
                console.log('Retrieved admin session for:', session.username);
            }
            return result;
        } catch (error) {
            console.error('Error getting storage item:', error);
            return null;
        }
    };
})();

// Fix 4: Page Load Stability
(function() {
    let pageLoadHandled = false;
    
    function handlePageLoad() {
        if (pageLoadHandled) {
            console.log('Page load already handled');
            return;
        }
        
        pageLoadHandled = true;
        console.log('Admin page loaded:', window.location.pathname);
        
        // Clear any existing redirect flags after page load
        setTimeout(() => {
            if (window.adminAuth && window.adminAuth.isRedirecting) {
                console.log('Clearing redirect flag after page load');
                window.adminAuth.isRedirecting = false;
            }
        }, 1000);
    }
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', handlePageLoad);
    } else {
        handlePageLoad();
    }
})();

// Fix 5: Error Boundary for Admin Functions
(function() {
    window.safeAdminCall = function(fn, context, ...args) {
        try {
            return fn.apply(context, args);
        } catch (error) {
            console.error('Safe admin call error:', error);
            return null;
        }
    };
})();

// Fix 6: Login Form Stability
(function() {
    document.addEventListener('DOMContentLoaded', function() {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            let submitting = false;
            
            loginForm.addEventListener('submit', function(e) {
                if (submitting) {
                    console.log('Login already in progress');
                    e.preventDefault();
                    return false;
                }
                
                submitting = true;
                console.log('Login form submitted');
                
                // Reset flag after 5 seconds
                setTimeout(() => {
                    submitting = false;
                }, 5000);
            });
        }
    });
})();

// Fix 7: Dashboard Navigation Stability
(function() {
    document.addEventListener('DOMContentLoaded', function() {
        // Prevent multiple clicks on navigation items
        const navItems = document.querySelectorAll('.nav-item, .sidebar-link');
        navItems.forEach(item => {
            let clicking = false;
            
            item.addEventListener('click', function(e) {
                if (clicking) {
                    e.preventDefault();
                    return false;
                }
                
                clicking = true;
                setTimeout(() => {
                    clicking = false;
                }, 1000);
            });
        });
    });
})();

// Fix 8: Auto-logout Prevention
(function() {
    // Prevent automatic logout due to session refresh issues
    if (window.adminAuth) {
        const originalLogout = window.adminAuth.logout;
        window.adminAuth.logout = function() {
            console.log('Logout called');
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                return originalLogout.call(this);
            }
        };
    }
})();

// Fix 9: Console Error Suppression
(function() {
    const originalError = console.error;
    console.error = function(...args) {
        const message = args.join(' ');
        
        // Suppress known admin panel errors
        if (message.includes('Illegal invocation') ||
            message.includes('Cannot read property') ||
            message.includes('adminAuth') && message.includes('undefined')) {
            console.log('Suppressed admin error:', message);
            return;
        }
        
        originalError.apply(console, args);
    };
})();

// Fix 10: Page Visibility API
(function() {
    // Prevent session issues when page becomes hidden/visible
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'visible') {
            console.log('Admin page became visible');
            
            // Reset redirect flags
            if (window.adminAuth) {
                window.adminAuth.isRedirecting = false;
            }
        }
    });
})();

// Fix 11: URL Hash Navigation
(function() {
    // Prevent hash changes from triggering redirects
    window.addEventListener('hashchange', function(e) {
        console.log('Hash changed:', window.location.hash);
        e.preventDefault();
    });
})();

// Fix 12: Form Auto-complete Issues
(function() {
    document.addEventListener('DOMContentLoaded', function() {
        // Fix autocomplete issues that might trigger form submissions
        const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
        inputs.forEach(input => {
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && e.target.form) {
                    // Prevent accidental form submission
                    const submitBtn = e.target.form.querySelector('button[type="submit"]');
                    if (submitBtn && !submitBtn.disabled) {
                        e.preventDefault();
                        submitBtn.click();
                    }
                }
            });
        });
    });
})();

// Fix 13: Memory Leak Prevention
(function() {
    // Clear intervals and timeouts when page unloads
    const intervals = [];
    const timeouts = [];
    
    const originalSetInterval = window.setInterval;
    const originalSetTimeout = window.setTimeout;
    
    window.setInterval = function(fn, delay) {
        const id = originalSetInterval(fn, delay);
        intervals.push(id);
        return id;
    };
    
    window.setTimeout = function(fn, delay) {
        const id = originalSetTimeout(fn, delay);
        timeouts.push(id);
        return id;
    };
    
    window.addEventListener('beforeunload', function() {
        intervals.forEach(id => clearInterval(id));
        timeouts.forEach(id => clearTimeout(id));
        console.log('Cleaned up intervals and timeouts');
    });
})();

// Fix 14: Debug Information
window.adminDebug = {
    version: '1.0.0',
    loaded: true,
    timestamp: new Date().toISOString(),
    
    getStatus: function() {
        return {
            authInitialized: !!window.adminAuth,
            currentPage: window.location.pathname,
            sessionExists: !!localStorage.getItem('admin_session') || !!sessionStorage.getItem('admin_session'),
            redirecting: window.adminAuth ? window.adminAuth.isRedirecting : false
        };
    },
    
    clearSession: function() {
        localStorage.removeItem('admin_session');
        sessionStorage.removeItem('admin_session');
        console.log('Admin session cleared');
    },
    
    forceLogin: function() {
        window.location.href = 'login.html';
    }
};

console.log('✅ Admin panel fixes loaded successfully');
console.log('Debug info available at: window.adminDebug');
