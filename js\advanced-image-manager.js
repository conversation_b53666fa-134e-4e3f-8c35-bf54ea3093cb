// Advanced Image Management System with Firebase Integration
// نظام إدارة الصور المتقدم مع تكامل Firebase

class AdvancedImageManager {
    constructor() {
        this.storage = null;
        this.supabase = null;
        this.maxFileSize = 5 * 1024 * 1024; // 5MB
        this.allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
        this.compressionQuality = 0.8;
        this.maxWidth = 1200;
        this.maxHeight = 1200;
        this.thumbnailSize = 300;
        this.uploadQueue = [];
        this.isUploading = false;
        
        this.init();
    }

    async init() {
        try {
            // Wait for Firebase to be loaded
            await this.waitForFirebase();
            this.setupEventListeners();
            this.createImageComponents();
        } catch (error) {
            console.error('Error initializing image manager:', error);
        }
    }

    async waitForFirebase() {
        return new Promise((resolve) => {
            const checkFirebase = () => {
                if (window.firebase && window.firebaseStorage) {
                    this.storage = window.firebaseStorage;
                    this.supabase = window.supabase;
                    resolve();
                } else {
                    setTimeout(checkFirebase, 100);
                }
            };
            checkFirebase();
        });
    }

    setupEventListeners() {
        // Global drag and drop
        document.addEventListener('dragover', this.handleDragOver.bind(this));
        document.addEventListener('drop', this.handleDrop.bind(this));
        
        // Paste events for images
        document.addEventListener('paste', this.handlePaste.bind(this));
    }

    createImageComponents() {
        // Create image upload modal if it doesn't exist
        if (!document.getElementById('imageUploadModal')) {
            this.createImageUploadModal();
        }
        
        // Create image gallery modal if it doesn't exist
        if (!document.getElementById('imageGalleryModal')) {
            this.createImageGalleryModal();
        }
    }

    createImageUploadModal() {
        const modal = document.createElement('div');
        modal.id = 'imageUploadModal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content" style="max-width: 800px;">
                <div class="modal-header">
                    <h3>رفع الصور</h3>
                    <button class="close-modal" onclick="advancedImageManager.closeUploadModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-zone" onclick="document.getElementById('fileInput').click()">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h4>اسحب الصور هنا أو انقر للاختيار</h4>
                            <p>يمكنك رفع عدة صور في نفس الوقت</p>
                            <p class="upload-limits">الحد الأقصى: 5 ميجابايت لكل صورة</p>
                            <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
                        </div>
                    </div>
                    
                    <div class="upload-queue" id="uploadQueue" style="display: none;">
                        <h4>قائمة الرفع</h4>
                        <div class="queue-items" id="queueItems"></div>
                        <div class="queue-actions">
                            <button class="btn-primary" onclick="advancedImageManager.startUpload()">
                                <i class="fas fa-upload"></i>
                                بدء الرفع
                            </button>
                            <button class="btn-secondary" onclick="advancedImageManager.clearQueue()">
                                <i class="fas fa-trash"></i>
                                مسح القائمة
                            </button>
                        </div>
                    </div>
                    
                    <div class="upload-progress" id="uploadProgress" style="display: none;">
                        <div class="progress-info">
                            <span id="progressText">جاري الرفع...</span>
                            <span id="progressPercent">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Setup file input event
        document.getElementById('fileInput').addEventListener('change', (e) => {
            this.handleFileSelection(e.target.files);
        });
    }

    createImageGalleryModal() {
        const modal = document.createElement('div');
        modal.id = 'imageGalleryModal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content" style="max-width: 1000px;">
                <div class="modal-header">
                    <h3>معرض الصور</h3>
                    <div class="gallery-actions">
                        <button class="btn-primary" onclick="advancedImageManager.openUploadModal()">
                            <i class="fas fa-plus"></i>
                            إضافة صور
                        </button>
                        <button class="close-modal" onclick="advancedImageManager.closeGalleryModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="gallery-toolbar">
                        <div class="gallery-filters">
                            <select id="gallerySort">
                                <option value="newest">الأحدث أولاً</option>
                                <option value="oldest">الأقدم أولاً</option>
                                <option value="name">حسب الاسم</option>
                                <option value="size">حسب الحجم</option>
                            </select>
                            <button class="btn-secondary" onclick="advancedImageManager.toggleSelectMode()">
                                <i class="fas fa-check-square"></i>
                                تحديد متعدد
                            </button>
                        </div>
                        <div class="gallery-info">
                            <span id="galleryCount">0 صورة</span>
                            <span id="gallerySize">0 MB</span>
                        </div>
                    </div>
                    
                    <div class="image-gallery" id="galleryGrid">
                        <!-- Images will be loaded here -->
                    </div>
                    
                    <div class="gallery-pagination" id="galleryPagination" style="display: none;">
                        <button class="btn-secondary" onclick="advancedImageManager.loadPreviousPage()">
                            <i class="fas fa-chevron-right"></i>
                            السابق
                        </button>
                        <span id="pageInfo">صفحة 1 من 1</span>
                        <button class="btn-secondary" onclick="advancedImageManager.loadNextPage()">
                            <i class="fas fa-chevron-left"></i>
                            التالي
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    // File handling methods
    handleFileSelection(files) {
        const validFiles = Array.from(files).filter(file => this.validateFile(file));
        
        if (validFiles.length === 0) {
            this.showMessage('لم يتم اختيار ملفات صالحة', 'error');
            return;
        }

        this.addFilesToQueue(validFiles);
        this.showUploadQueue();
    }

    validateFile(file) {
        if (!this.allowedTypes.includes(file.type)) {
            this.showMessage(`نوع الملف ${file.name} غير مدعوم`, 'error');
            return false;
        }

        if (file.size > this.maxFileSize) {
            const maxSizeMB = (this.maxFileSize / (1024 * 1024)).toFixed(1);
            this.showMessage(`حجم الملف ${file.name} كبير جداً. الحد الأقصى: ${maxSizeMB} ميجابايت`, 'error');
            return false;
        }

        return true;
    }

    addFilesToQueue(files) {
        files.forEach(file => {
            const queueItem = {
                id: Date.now() + Math.random(),
                file: file,
                status: 'pending',
                progress: 0,
                preview: null
            };

            this.uploadQueue.push(queueItem);
            this.generatePreview(queueItem);
        });

        this.updateQueueDisplay();
    }

    generatePreview(queueItem) {
        const reader = new FileReader();
        reader.onload = (e) => {
            queueItem.preview = e.target.result;
            this.updateQueueItemDisplay(queueItem);
        };
        reader.readAsDataURL(queueItem.file);
    }

    showUploadQueue() {
        document.getElementById('uploadQueue').style.display = 'block';
        this.updateQueueDisplay();
    }

    updateQueueDisplay() {
        const container = document.getElementById('queueItems');
        container.innerHTML = '';

        this.uploadQueue.forEach(item => {
            const itemElement = this.createQueueItemElement(item);
            container.appendChild(itemElement);
        });
    }

    createQueueItemElement(item) {
        const element = document.createElement('div');
        element.className = 'queue-item';
        element.dataset.itemId = item.id;
        
        element.innerHTML = `
            <div class="queue-item-preview">
                ${item.preview ? `<img src="${item.preview}" alt="${item.file.name}">` : '<div class="preview-loading"><i class="fas fa-spinner fa-spin"></i></div>'}
            </div>
            <div class="queue-item-info">
                <div class="item-name">${item.file.name}</div>
                <div class="item-size">${this.formatFileSize(item.file.size)}</div>
                <div class="item-status status-${item.status}">${this.getStatusText(item.status)}</div>
            </div>
            <div class="queue-item-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${item.progress}%"></div>
                </div>
                <span class="progress-text">${item.progress}%</span>
            </div>
            <div class="queue-item-actions">
                <button class="btn-remove" onclick="advancedImageManager.removeFromQueue('${item.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        return element;
    }

    updateQueueItemDisplay(item) {
        const element = document.querySelector(`[data-item-id="${item.id}"]`);
        if (element) {
            const preview = element.querySelector('.queue-item-preview');
            if (item.preview && preview) {
                preview.innerHTML = `<img src="${item.preview}" alt="${item.file.name}">`;
            }

            const status = element.querySelector('.item-status');
            if (status) {
                status.className = `item-status status-${item.status}`;
                status.textContent = this.getStatusText(item.status);
            }

            const progressFill = element.querySelector('.progress-fill');
            const progressText = element.querySelector('.progress-text');
            if (progressFill && progressText) {
                progressFill.style.width = `${item.progress}%`;
                progressText.textContent = `${item.progress}%`;
            }
        }
    }

    getStatusText(status) {
        const statusTexts = {
            'pending': 'في الانتظار',
            'uploading': 'جاري الرفع',
            'compressing': 'جاري الضغط',
            'completed': 'مكتمل',
            'error': 'خطأ'
        };
        return statusTexts[status] || status;
    }

    removeFromQueue(itemId) {
        this.uploadQueue = this.uploadQueue.filter(item => item.id !== itemId);
        this.updateQueueDisplay();
        
        if (this.uploadQueue.length === 0) {
            document.getElementById('uploadQueue').style.display = 'none';
        }
    }

    clearQueue() {
        this.uploadQueue = [];
        document.getElementById('uploadQueue').style.display = 'none';
    }

    async startUpload() {
        if (this.isUploading || this.uploadQueue.length === 0) return;

        this.isUploading = true;
        this.showUploadProgress();

        const totalFiles = this.uploadQueue.length;
        let completedFiles = 0;

        for (const item of this.uploadQueue) {
            try {
                item.status = 'compressing';
                this.updateQueueItemDisplay(item);

                // Compress image
                const compressedFile = await this.compressImage(item.file);
                
                item.status = 'uploading';
                this.updateQueueItemDisplay(item);

                // Upload to Firebase Storage
                const result = await this.uploadToFirebase(compressedFile, item);
                
                if (result.success) {
                    // Save to Supabase database
                    await this.saveImageToDatabase(result.data, item);
                    
                    item.status = 'completed';
                    item.progress = 100;
                    completedFiles++;
                } else {
                    item.status = 'error';
                }

                this.updateQueueItemDisplay(item);
                this.updateOverallProgress(completedFiles, totalFiles);

            } catch (error) {
                console.error('Upload error:', error);
                item.status = 'error';
                this.updateQueueItemDisplay(item);
            }
        }

        this.isUploading = false;
        this.hideUploadProgress();
        
        if (completedFiles > 0) {
            this.showMessage(`تم رفع ${completedFiles} من ${totalFiles} صورة بنجاح`, 'success');
            this.clearQueue();
            
            // Refresh gallery if open
            if (document.getElementById('imageGalleryModal').classList.contains('show')) {
                this.loadGalleryImages();
            }
        }
    }

    async compressImage(file) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                const { width, height } = img;
                let newWidth = width;
                let newHeight = height;

                // Calculate new dimensions
                if (width > this.maxWidth || height > this.maxHeight) {
                    const ratio = Math.min(this.maxWidth / width, this.maxHeight / height);
                    newWidth = Math.round(width * ratio);
                    newHeight = Math.round(height * ratio);
                }

                canvas.width = newWidth;
                canvas.height = newHeight;

                // Draw and compress
                ctx.drawImage(img, 0, 0, newWidth, newHeight);
                
                canvas.toBlob((blob) => {
                    const compressedFile = new File([blob], file.name, {
                        type: file.type,
                        lastModified: Date.now()
                    });
                    resolve(compressedFile);
                }, file.type, this.compressionQuality);
            };

            img.src = URL.createObjectURL(file);
        });
    }

    async uploadToFirebase(file, queueItem) {
        try {
            // This would be implemented with actual Firebase Storage
            // For now, simulate upload
            return new Promise((resolve) => {
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 20;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                        
                        // Simulate successful upload
                        resolve({
                            success: true,
                            data: {
                                url: URL.createObjectURL(file),
                                path: `products/${Date.now()}_${file.name}`,
                                size: file.size,
                                type: file.type
                            }
                        });
                    }
                    
                    queueItem.progress = Math.round(progress);
                    this.updateQueueItemDisplay(queueItem);
                }, 100);
            });
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async saveImageToDatabase(imageData, queueItem) {
        try {
            // This would save to Supabase
            // For now, save to localStorage
            const images = JSON.parse(localStorage.getItem('product_images') || '[]');
            
            const imageRecord = {
                id: Date.now().toString(),
                product_id: this.currentProductId || null,
                image_url: imageData.url,
                thumbnail_url: imageData.url, // Would generate actual thumbnail
                file_name: queueItem.file.name,
                file_size: imageData.size,
                file_type: imageData.type,
                is_primary: images.length === 0,
                sort_order: images.length,
                created_at: new Date().toISOString()
            };
            
            images.push(imageRecord);
            localStorage.setItem('product_images', JSON.stringify(images));
            
            return imageRecord;
        } catch (error) {
            console.error('Error saving image to database:', error);
            throw error;
        }
    }

    showUploadProgress() {
        document.getElementById('uploadProgress').style.display = 'block';
    }

    hideUploadProgress() {
        document.getElementById('uploadProgress').style.display = 'none';
    }

    updateOverallProgress(completed, total) {
        const percent = Math.round((completed / total) * 100);
        document.getElementById('progressFill').style.width = `${percent}%`;
        document.getElementById('progressPercent').textContent = `${percent}%`;
        document.getElementById('progressText').textContent = `تم رفع ${completed} من ${total} صورة`;
    }

    // Modal management
    openUploadModal(productId = null) {
        this.currentProductId = productId;
        document.getElementById('imageUploadModal').classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    closeUploadModal() {
        document.getElementById('imageUploadModal').classList.remove('show');
        document.body.style.overflow = '';
        this.clearQueue();
    }

    openGalleryModal(productId = null) {
        this.currentProductId = productId;
        document.getElementById('imageGalleryModal').classList.add('show');
        document.body.style.overflow = 'hidden';
        this.loadGalleryImages();
    }

    closeGalleryModal() {
        document.getElementById('imageGalleryModal').classList.remove('show');
        document.body.style.overflow = '';
    }

    // Gallery management
    async loadGalleryImages() {
        try {
            // Load images from database
            const images = JSON.parse(localStorage.getItem('product_images') || '[]');
            
            let filteredImages = images;
            if (this.currentProductId) {
                filteredImages = images.filter(img => img.product_id === this.currentProductId);
            }

            this.displayGalleryImages(filteredImages);
            this.updateGalleryInfo(filteredImages);
            
        } catch (error) {
            console.error('Error loading gallery images:', error);
            this.showMessage('فشل في تحميل الصور', 'error');
        }
    }

    displayGalleryImages(images) {
        const grid = document.getElementById('galleryGrid');
        
        if (images.length === 0) {
            grid.innerHTML = `
                <div class="no-images">
                    <i class="fas fa-images"></i>
                    <h3>لا توجد صور</h3>
                    <p>ابدأ برفع صور للمنتجات</p>
                    <button class="btn-primary" onclick="advancedImageManager.openUploadModal()">
                        <i class="fas fa-plus"></i>
                        رفع صور
                    </button>
                </div>
            `;
            return;
        }

        grid.innerHTML = images.map(image => this.createGalleryImageElement(image)).join('');
    }

    createGalleryImageElement(image) {
        return `
            <div class="gallery-image-item" data-image-id="${image.id}">
                <div class="image-wrapper">
                    <img src="${image.thumbnail_url || image.image_url}" alt="${image.file_name}" loading="lazy">
                    <div class="image-overlay">
                        <button class="btn-view" onclick="advancedImageManager.viewImage('${image.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-edit" onclick="advancedImageManager.editImage('${image.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-delete" onclick="advancedImageManager.deleteImage('${image.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    ${image.is_primary ? '<div class="primary-badge">رئيسية</div>' : ''}
                </div>
                <div class="image-info">
                    <div class="image-name">${image.file_name}</div>
                    <div class="image-size">${this.formatFileSize(image.file_size)}</div>
                    <div class="image-date">${this.formatDate(image.created_at)}</div>
                </div>
            </div>
        `;
    }

    updateGalleryInfo(images) {
        const totalSize = images.reduce((sum, img) => sum + (img.file_size || 0), 0);
        document.getElementById('galleryCount').textContent = `${images.length} صورة`;
        document.getElementById('gallerySize').textContent = this.formatFileSize(totalSize);
    }

    // Utility methods
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-EG');
    }

    handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'copy';
    }

    handleDrop(e) {
        e.preventDefault();
        const files = Array.from(e.dataTransfer.files).filter(file => 
            file.type.startsWith('image/')
        );
        
        if (files.length > 0) {
            this.handleFileSelection(files);
            if (!document.getElementById('imageUploadModal').classList.contains('show')) {
                this.openUploadModal();
            }
        }
    }

    handlePaste(e) {
        const items = Array.from(e.clipboardData.items);
        const imageItems = items.filter(item => item.type.startsWith('image/'));
        
        if (imageItems.length > 0) {
            const files = imageItems.map(item => item.getAsFile());
            this.handleFileSelection(files);
            if (!document.getElementById('imageUploadModal').classList.contains('show')) {
                this.openUploadModal();
            }
        }
    }

    showMessage(message, type = 'info') {
        // Use existing notification system
        if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            alert(message);
        }
    }

    // Image actions
    viewImage(imageId) {
        const images = JSON.parse(localStorage.getItem('product_images') || '[]');
        const image = images.find(img => img.id === imageId);
        
        if (image) {
            // Open lightbox
            this.openLightbox(image.image_url);
        }
    }

    editImage(imageId) {
        // Open image edit modal
        console.log('Edit image:', imageId);
    }

    async deleteImage(imageId) {
        if (!confirm('هل أنت متأكد من حذف هذه الصورة؟')) return;

        try {
            const images = JSON.parse(localStorage.getItem('product_images') || '[]');
            const updatedImages = images.filter(img => img.id !== imageId);
            localStorage.setItem('product_images', JSON.stringify(updatedImages));
            
            this.loadGalleryImages();
            this.showMessage('تم حذف الصورة بنجاح', 'success');
        } catch (error) {
            console.error('Error deleting image:', error);
            this.showMessage('فشل في حذف الصورة', 'error');
        }
    }

    openLightbox(imageUrl) {
        const lightbox = document.createElement('div');
        lightbox.className = 'image-lightbox';
        lightbox.innerHTML = `
            <div class="lightbox-overlay" onclick="this.parentElement.remove()">
                <div class="lightbox-content" onclick="event.stopPropagation()">
                    <button class="lightbox-close" onclick="this.closest('.image-lightbox').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                    <img src="${imageUrl}" alt="صورة مكبرة">
                </div>
            </div>
        `;
        
        document.body.appendChild(lightbox);
    }
}

// Initialize advanced image manager
const advancedImageManager = new AdvancedImageManager();

// Make available globally
window.advancedImageManager = advancedImageManager;
