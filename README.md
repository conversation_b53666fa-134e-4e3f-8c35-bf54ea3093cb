# نظام الطلبات الإلكتروني - مطعم محمد الاشرافي

نظام طلبات إلكتروني متكامل لمطعم محمد الاشرافي مع لوحة إدارة شاملة وتكامل مع الواتساب.

## 🌟 المميزات الرئيسية

### واجهة العملاء
- **تصميم عصري ومتجاوب**: يعمل بشكل مثالي على جميع الأجهزة
- **قائمة طعام تفاعلية**: تصفح سهل مع البحث والفلترة
- **نظام سلة التسوق**: إضافة وإدارة الطلبات بسهولة
- **تكامل الواتساب**: إرسال الطلبات مباشرة عبر الواتساب
- **تجربة مستخدم محسنة**: رسوم متحركة وتأثيرات بصرية جذابة

### لوحة الإدارة
- **نظام مصادقة آمن**: تسجيل دخول محمي بكلمة مرور
- **لوحة تحكم شاملة**: إحصائيات ومؤشرات الأداء
- **إدارة القائمة**: إضافة وتعديل وحذف عناصر القائمة
- **إدارة الطلبات**: متابعة وتحديث حالة الطلبات
- **التقارير والإحصائيات**: تحليل المبيعات والأداء
- **إعدادات النظام**: تخصيص إعدادات المطعم

## 🚀 التقنيات المستخدمة

- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتنسيق مع Flexbox و Grid
- **JavaScript (ES6+)**: المنطق والتفاعل
- **Font Awesome**: الأيقونات
- **Google Fonts**: الخطوط العربية (Cairo)
- **Local Storage**: حفظ البيانات محلياً

## 📁 هيكل المشروع

```
├── index.html              # الصفحة الرئيسية للعملاء
├── css/
│   └── style.css          # ملف التصميم الرئيسي
├── js/
│   ├── main.js            # الكود الرئيسي للتطبيق
│   ├── cart.js            # إدارة سلة التسوق
│   └── menu-data.js       # بيانات القائمة
├── admin/
│   ├── login.html         # صفحة تسجيل دخول الإدارة
│   ├── dashboard.html     # لوحة التحكم الرئيسية
│   ├── css/
│   │   ├── admin.css      # تصميم لوحة الإدارة
│   │   └── dashboard.css  # تصميم لوحة التحكم
│   └── js/
│       ├── auth.js        # نظام المصادقة
│       ├── dashboard.js   # إدارة لوحة التحكم
│       └── menu-management.js # إدارة القائمة
└── README.md              # ملف التوثيق
```

## 🔧 التثبيت والتشغيل

1. **تحميل الملفات**:
   ```bash
   git clone [repository-url]
   cd restaurant-ordering-system
   ```

2. **فتح النظام**:
   - افتح `index.html` في المتصفح للواجهة الرئيسية
   - افتح `admin/login.html` للوصول للوحة الإدارة

3. **بيانات تسجيل الدخول للإدارة**:
   - المدير العام: `admin` / `admin123`
   - صاحب المطعم: `محمد` / `alashrafi2024`
   - المدير: `manager` / `restaurant123`
   - الموظف: `staff` / `staff2024`

## 📱 كيفية الاستخدام

### للعملاء:
1. تصفح القائمة واختيار الأطباق المرغوبة
2. إضافة العناصر إلى سلة التسوق
3. مراجعة الطلب وإدخال البيانات الشخصية
4. إرسال الطلب عبر الواتساب

### للإدارة:
1. تسجيل الدخول إلى لوحة الإدارة
2. مراجعة الطلبات الواردة وتحديث حالتها
3. إدارة عناصر القائمة (إضافة/تعديل/حذف)
4. مراجعة التقارير والإحصائيات
5. تحديث إعدادات المطعم

## ⚙️ الإعدادات

### تغيير رقم الواتساب:
1. في ملف `js/cart.js` السطر 265: `const whatsappNumber = '201014840269';`
2. في لوحة الإدارة > الإعدادات > معلومات المطعم

### إضافة عناصر جديدة للقائمة:
1. استخدم لوحة الإدارة > إدارة القائمة > إضافة عنصر جديد
2. أو قم بتعديل ملف `js/menu-data.js` مباشرة

### تخصيص التصميم:
- الألوان الرئيسية في ملف `css/style.css`
- تصميم لوحة الإدارة في `admin/css/admin.css`

## 🔒 الأمان

- نظام مصادقة محمي للوحة الإدارة
- تشفير كلمات المرور (يُنصح بتطوير نظام أكثر تقدماً للإنتاج)
- حفظ البيانات محلياً في المتصفح
- تسجيل أنشطة المستخدمين

## 📊 المميزات المتقدمة

- **البحث الذكي**: البحث في أسماء ووصف الأطباق
- **الفلترة بالأقسام**: تصنيف الأطباق حسب النوع
- **التصميم المتجاوب**: يعمل على الهواتف والأجهزة اللوحية
- **الرسوم المتحركة**: تأثيرات بصرية جذابة
- **اختصارات لوحة المفاتيح**: للتنقل السريع
- **الحفظ التلقائي**: حفظ سلة التسوق تلقائياً

## 🛠️ التطوير المستقبلي

- [ ] تكامل مع قاعدة بيانات حقيقية
- [ ] نظام دفع إلكتروني
- [ ] تطبيق جوال
- [ ] نظام إشعارات متقدم
- [ ] تقارير مفصلة ورسوم بيانية
- [ ] نظام تقييم العملاء
- [ ] دعم متعدد اللغات

## 📞 الدعم والتواصل

- **الواتساب**: +201014840269
- **البريد الإلكتروني**: <EMAIL>

## 📄 الترخيص

هذا المشروع مطور خصيصاً لمطعم محمد الاشرافي. جميع الحقوق محفوظة © 2024.

---

**ملاحظة**: هذا النظام مصمم للاستخدام المحلي ويحفظ البيانات في متصفح المستخدم. للاستخدام التجاري الكامل، يُنصح بتطوير نظام خادم وقاعدة بيانات مناسبة.
