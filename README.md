# نظام الطلبات الإلكتروني المتقدم - مطعم محمد الاشرافي

نظام طلبات إلكتروني متكامل ومتقدم لمطعم محمد الاشرافي مع قواعد بيانات حقيقية، إدارة الصور، لوحة إدارة متطورة، وتكامل محسن مع الواتساب.

## 🌟 المميزات الرئيسية

### واجهة العملاء المحسنة
- **تصميم عصري ومتجاوب**: يعمل بشكل مثالي على جميع الأجهزة
- **قائمة طعام تفاعلية**: تصفح سهل مع البحث والفلترة المتقدمة
- **نظام سلة التسوق الذكي**: إضافة وإدارة الطلبات مع حفظ تلقائي
- **تكامل الواتساب المحسن**: فواتير احترافية ورسائل منسقة
- **تجربة مستخدم متطورة**: رسوم متحركة وتأثيرات بصرية جذابة
- **نظام اقتراحات العملاء**: حفظ بيانات العملاء والاقتراحات التلقائية

### لوحة الإدارة المتقدمة
- **نظام مصادقة متعدد المستويات**: أدوار مختلفة (مدير، مالك، موظف)
- **لوحة تحكم تفاعلية**: إحصائيات مباشرة ومؤشرات الأداء
- **إدارة المنتجات المتطورة**: إضافة وتعديل مع إدارة الصور
- **نظام إدارة الصور**: رفع متعدد، معرض صور، ضغط تلقائي
- **إدارة الطلبات المتقدمة**: متابعة مباشرة وتحديث الحالة
- **التقارير والتحليلات**: تحليل مفصل للمبيعات والعملاء
- **إعدادات النظام الشاملة**: تخصيص كامل لإعدادات المطعم

### قواعد البيانات والتخزين
- **Supabase**: قاعدة بيانات PostgreSQL للبيانات الرئيسية
- **Firebase**: المصادقة وتخزين الصور والاستضافة
- **نظام مزامنة**: مزامنة البيانات بين القواعد المختلفة
- **نسخ احتياطية**: نظام نسخ احتياطي تلقائي

### الأمان والأداء
- **نظام إدارة الأخطاء**: تتبع ومعالجة الأخطاء تلقائياً
- **تحسين الأداء**: ضغط الصور، تخزين مؤقت، تحميل تدريجي
- **أمان متقدم**: حماية من XSS، CSRF، وتشفير البيانات
- **مراقبة الأداء**: تتبع مقاييس الأداء والاستخدام

## 🚀 التقنيات المستخدمة

### Frontend
- **HTML5**: هيكل الصفحات مع دعم PWA
- **CSS3**: تصميم متقدم مع Flexbox، Grid، وAnimations
- **JavaScript (ES6+)**: منطق متطور مع Modules وAsync/Await
- **Font Awesome 6**: مكتبة أيقونات شاملة
- **Google Fonts**: خطوط عربية عالية الجودة (Cairo)

### Backend & Database
- **Supabase**: قاعدة بيانات PostgreSQL مع API مباشر
- **Firebase**:
  - Authentication (المصادقة)
  - Cloud Storage (تخزين الصور)
  - Hosting (الاستضافة)
  - Analytics (التحليلات)

### أدوات التطوير والنشر
- **Firebase CLI**: أدوات النشر والإدارة
- **Node.js & NPM**: إدارة الحزم والبناء
- **Webpack/Terser**: ضغط وتحسين الملفات
- **Lighthouse**: قياس الأداء
- **ESLint & Prettier**: جودة الكود

### مكتبات إضافية
- **Intersection Observer**: تحميل تدريجي للصور
- **Performance Observer**: مراقبة الأداء
- **Web APIs**: Geolocation، Notifications، Service Workers

## 📁 هيكل المشروع

```
├── index.html              # الصفحة الرئيسية للعملاء
├── css/
│   └── style.css          # ملف التصميم الرئيسي
├── js/
│   ├── main.js            # الكود الرئيسي للتطبيق
│   ├── cart.js            # إدارة سلة التسوق
│   └── menu-data.js       # بيانات القائمة
├── admin/
│   ├── login.html         # صفحة تسجيل دخول الإدارة
│   ├── dashboard.html     # لوحة التحكم الرئيسية
│   ├── css/
│   │   ├── admin.css      # تصميم لوحة الإدارة
│   │   └── dashboard.css  # تصميم لوحة التحكم
│   └── js/
│       ├── auth.js        # نظام المصادقة
│       ├── dashboard.js   # إدارة لوحة التحكم
│       └── menu-management.js # إدارة القائمة
└── README.md              # ملف التوثيق
```

## 🔧 التثبيت والإعداد

### المتطلبات الأساسية
- Node.js (الإصدار 16 أو أحدث)
- NPM أو Yarn
- حساب Firebase
- حساب Supabase

### 1. تحميل المشروع
```bash
git clone https://github.com/alashrafi/restaurant-ordering-system.git
cd restaurant-ordering-system
npm install
```

### 2. إعداد Firebase
```bash
# تسجيل الدخول إلى Firebase
firebase login

# إنشاء مشروع جديد
firebase init

# اختيار الخدمات: Hosting, Firestore, Storage, Functions
```

### 3. إعداد Supabase
1. إنشاء مشروع جديد في [Supabase](https://supabase.com)
2. تشغيل ملف `database-schema.sql` في SQL Editor
3. نسخ URL المشروع و API Key

### 4. تكوين المتغيرات
```bash
# إنشاء ملف .env
cp .env.example .env

# تحديث المتغيرات:
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_PROJECT_ID=your_project_id
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 5. التشغيل المحلي
```bash
# تشغيل الخادم المحلي
npm run dev

# أو استخدام Firebase Emulator
firebase serve
```

### 6. النشر
```bash
# بناء المشروع للإنتاج
npm run build

# النشر على Firebase
npm run deploy
```

## 🗄️ إدارة قواعد البيانات

### إعداد Supabase
1. **إنشاء الجداول**:
   ```sql
   -- تشغيل ملف database-schema.sql في Supabase SQL Editor
   ```

2. **إعداد Row Level Security (RLS)**:
   - تفعيل RLS على جميع الجداول
   - تطبيق السياسات المحددة في الملف

3. **إنشاء المستخدمين الإداريين**:
   ```sql
   -- إضافة مستخدم إداري
   INSERT INTO auth.users (email, encrypted_password, role)
   VALUES ('<EMAIL>', crypt('admin123', gen_salt('bf')), 'admin');
   ```

### إعداد Firebase
1. **تكوين Firestore**:
   - نشر قواعد الأمان من `firestore.rules`
   - إنشاء الفهارس من `firestore.indexes.json`

2. **تكوين Storage**:
   - نشر قواعد التخزين من `storage.rules`
   - إنشاء مجلدات التخزين

3. **إعداد Authentication**:
   ```javascript
   // تفعيل مقدمي الخدمة المطلوبين
   - Email/Password
   - Phone Authentication
   - Google Sign-in (اختياري)
   ```

## 📱 كيفية الاستخدام

### للعملاء:
1. تصفح القائمة مع الصور عالية الجودة
2. استخدام البحث والفلترة المتقدمة
3. إضافة العناصر إلى سلة التسوق الذكية
4. الاستفادة من الاقتراحات التلقائية للعملاء المتكررين
5. مراجعة الطلب مع معاينة الفاتورة
6. إرسال الطلب عبر الواتساب مع فاتورة احترافية

### للإدارة:
1. **تسجيل الدخول**: استخدام النظام متعدد المستويات
2. **إدارة المنتجات**:
   - إضافة منتجات جديدة مع صور متعددة
   - تعديل الأسعار والأوصاف
   - إدارة التوفر والعروض
3. **إدارة الطلبات**:
   - مراجعة الطلبات الواردة مباشرة
   - تحديث حالة الطلبات
   - إرسال تحديثات للعملاء عبر الواتساب
4. **التقارير والإحصائيات**:
   - تحليل المبيعات اليومية والشهرية
   - إحصائيات العملاء والمنتجات الأكثر طلباً
   - تقارير الأداء والإيرادات
5. **إعدادات النظام**:
   - تخصيص معلومات المطعم
   - إدارة أرقام الواتساب
   - تحديث ساعات العمل والعروض

## ⚙️ الإعدادات

### تغيير رقم الواتساب:
1. في ملف `js/cart.js` السطر 265: `const whatsappNumber = '201014840269';`
2. في لوحة الإدارة > الإعدادات > معلومات المطعم

### إضافة عناصر جديدة للقائمة:
1. استخدم لوحة الإدارة > إدارة القائمة > إضافة عنصر جديد
2. أو قم بتعديل ملف `js/menu-data.js` مباشرة

### تخصيص التصميم:
- الألوان الرئيسية في ملف `css/style.css`
- تصميم لوحة الإدارة في `admin/css/admin.css`

## 🔒 الأمان

- نظام مصادقة محمي للوحة الإدارة
- تشفير كلمات المرور (يُنصح بتطوير نظام أكثر تقدماً للإنتاج)
- حفظ البيانات محلياً في المتصفح
- تسجيل أنشطة المستخدمين

## 📊 المميزات المتقدمة

- **البحث الذكي**: البحث في أسماء ووصف الأطباق
- **الفلترة بالأقسام**: تصنيف الأطباق حسب النوع
- **التصميم المتجاوب**: يعمل على الهواتف والأجهزة اللوحية
- **الرسوم المتحركة**: تأثيرات بصرية جذابة
- **اختصارات لوحة المفاتيح**: للتنقل السريع
- **الحفظ التلقائي**: حفظ سلة التسوق تلقائياً

## 🛠️ التطوير المستقبلي

- [ ] تكامل مع قاعدة بيانات حقيقية
- [ ] نظام دفع إلكتروني
- [ ] تطبيق جوال
- [ ] نظام إشعارات متقدم
- [ ] تقارير مفصلة ورسوم بيانية
- [ ] نظام تقييم العملاء
- [ ] دعم متعدد اللغات

## 📞 الدعم والتواصل

- **الواتساب**: +201014840269
- **البريد الإلكتروني**: <EMAIL>

## 📄 الترخيص

هذا المشروع مطور خصيصاً لمطعم محمد الاشرافي. جميع الحقوق محفوظة © 2024.

---

**ملاحظة**: هذا النظام مصمم للاستخدام المحلي ويحفظ البيانات في متصفح المستخدم. للاستخدام التجاري الكامل، يُنصح بتطوير نظام خادم وقاعدة بيانات مناسبة.
