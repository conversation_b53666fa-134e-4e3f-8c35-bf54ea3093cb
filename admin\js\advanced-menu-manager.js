// Advanced Menu Management System
// نظام إدارة القائمة المتقدم

class AdvancedMenuManager {
    constructor() {
        this.products = [];
        this.categories = [];
        this.filteredProducts = [];
        this.currentProduct = null;
        this.currentProductId = null;
        this.isLoading = false;
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.totalItems = 0;
        this.currentView = 'grid';
        this.filters = {
            search: '',
            category: '',
            status: '',
            sortBy: 'newest'
        };
        
        this.init();
    }

    async init() {
        try {
            this.showLoading(true);
            await this.loadCategories();
            await this.loadProducts();
            this.setupEventListeners();
            this.populateCategoryFilters();
            this.applyFilters();
            this.showLoading(false);
        } catch (error) {
            console.error('Error initializing menu manager:', error);
            this.showMessage('فشل في تحميل البيانات', 'error');
            this.showLoading(false);
        }
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('searchProducts');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce((e) => {
                this.filters.search = e.target.value.toLowerCase();
                this.applyFilters();
            }, 300));
        }

        // Filter controls
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filters.category = e.target.value;
                this.applyFilters();
            });
        }

        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.applyFilters();
            });
        }

        const sortBy = document.getElementById('sortBy');
        if (sortBy) {
            sortBy.addEventListener('change', (e) => {
                this.filters.sortBy = e.target.value;
                this.applyFilters();
            });
        }

        // Product form submission
        const productForm = document.getElementById('productForm');
        if (productForm) {
            productForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveProduct();
            });
        }

        // Category form submission
        const categoryForm = document.getElementById('categoryForm');
        if (categoryForm) {
            categoryForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveCategory();
            });
        }
    }

    async loadCategories() {
        try {
            // Load from localStorage for now, replace with API call later
            const storedCategories = localStorage.getItem('restaurant_categories');
            if (storedCategories) {
                this.categories = JSON.parse(storedCategories);
            } else {
                // Default categories
                this.categories = [
                    { id: '1', name: 'المقبلات', name_en: 'Appetizers', description: 'مقبلات شهية ومتنوعة' },
                    { id: '2', name: 'الأطباق الرئيسية', name_en: 'Main Dishes', description: 'أطباق رئيسية مميزة' },
                    { id: '3', name: 'المشروبات', name_en: 'Beverages', description: 'مشروبات ساخنة وباردة' },
                    { id: '4', name: 'الحلويات', name_en: 'Desserts', description: 'حلويات لذيذة ومتنوعة' }
                ];
                localStorage.setItem('restaurant_categories', JSON.stringify(this.categories));
            }
        } catch (error) {
            console.error('Error loading categories:', error);
            throw error;
        }
    }

    async loadProducts() {
        try {
            // Load from localStorage for now, replace with API call later
            const storedProducts = localStorage.getItem('restaurant_products');
            if (storedProducts) {
                this.products = JSON.parse(storedProducts);
            } else {
                // Sample products
                this.products = [
                    {
                        id: '1',
                        name: 'برجر كلاسيك',
                        name_en: 'Classic Burger',
                        description: 'برجر لذيذ مع اللحم المشوي والخضروات الطازجة',
                        description_en: 'Delicious burger with grilled meat and fresh vegetables',
                        price: 45.00,
                        category_id: '2',
                        is_available: true,
                        is_featured: true,
                        preparation_time: 15,
                        calories: 650,
                        ingredients: ['لحم بقري', 'خبز', 'خس', 'طماطم', 'جبن'],
                        allergens: ['جلوتين', 'ألبان'],
                        tags: ['مشوي', 'لذيذ'],
                        images: [],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    },
                    {
                        id: '2',
                        name: 'سلطة سيزر',
                        name_en: 'Caesar Salad',
                        description: 'سلطة سيزر الكلاسيكية مع الدجاج المشوي',
                        description_en: 'Classic Caesar salad with grilled chicken',
                        price: 35.00,
                        category_id: '1',
                        is_available: true,
                        is_featured: false,
                        preparation_time: 10,
                        calories: 320,
                        ingredients: ['خس', 'دجاج مشوي', 'جبن بارميزان', 'خبز محمص'],
                        allergens: ['جلوتين', 'ألبان'],
                        tags: ['صحي', 'خفيف'],
                        images: [],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    }
                ];
                localStorage.setItem('restaurant_products', JSON.stringify(this.products));
            }
        } catch (error) {
            console.error('Error loading products:', error);
            throw error;
        }
    }

    populateCategoryFilters() {
        const categoryFilter = document.getElementById('categoryFilter');
        const productCategory = document.getElementById('productCategory');
        
        if (categoryFilter) {
            categoryFilter.innerHTML = '<option value="">جميع الأقسام</option>';
            this.categories.forEach(category => {
                const option = new Option(category.name, category.id);
                categoryFilter.appendChild(option);
            });
        }
        
        if (productCategory) {
            productCategory.innerHTML = '<option value="">اختر القسم</option>';
            this.categories.forEach(category => {
                const option = new Option(category.name, category.id);
                productCategory.appendChild(option);
            });
        }
    }

    applyFilters() {
        let filtered = [...this.products];

        // Apply search filter
        if (this.filters.search) {
            filtered = filtered.filter(product =>
                product.name.toLowerCase().includes(this.filters.search) ||
                product.description.toLowerCase().includes(this.filters.search) ||
                (product.name_en && product.name_en.toLowerCase().includes(this.filters.search))
            );
        }

        // Apply category filter
        if (this.filters.category) {
            filtered = filtered.filter(product => product.category_id === this.filters.category);
        }

        // Apply status filter
        if (this.filters.status) {
            const isAvailable = this.filters.status === 'available';
            filtered = filtered.filter(product => product.is_available === isAvailable);
        }

        // Apply sorting
        filtered.sort((a, b) => {
            switch (this.filters.sortBy) {
                case 'newest':
                    return new Date(b.created_at) - new Date(a.created_at);
                case 'oldest':
                    return new Date(a.created_at) - new Date(b.created_at);
                case 'name':
                    return a.name.localeCompare(b.name, 'ar');
                case 'price':
                    return a.price - b.price;
                default:
                    return 0;
            }
        });

        this.filteredProducts = filtered;
        this.totalItems = filtered.length;
        this.currentPage = 1;
        this.renderProducts();
        this.updatePagination();
    }

    renderProducts() {
        const grid = document.getElementById('productsGrid');
        if (!grid) return;

        // Calculate pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageProducts = this.filteredProducts.slice(startIndex, endIndex);

        if (pageProducts.length === 0) {
            grid.innerHTML = this.createNoProductsHTML();
            return;
        }

        // Apply current view
        grid.className = `products-grid ${this.currentView}-view`;
        grid.innerHTML = pageProducts.map(product => this.createProductCard(product)).join('');
    }

    createProductCard(product) {
        const category = this.categories.find(c => c.id === product.category_id);
        const categoryName = category ? category.name : 'غير محدد';
        
        // Get primary image
        const primaryImage = product.images?.find(img => img.is_primary) || product.images?.[0];
        const imageUrl = primaryImage?.thumbnail_url || primaryImage?.image_url || 'https://via.placeholder.com/300x200/f8f9fa/666?text=لا+توجد+صورة';
        
        return `
            <div class="product-card" data-product-id="${product.id}">
                <div class="product-image">
                    <img src="${imageUrl}" alt="${product.name}" loading="lazy">
                    ${product.images?.length > 0 ? `
                        <div class="image-count">
                            <i class="fas fa-images"></i>
                            ${product.images.length}
                        </div>
                    ` : ''}
                    <div class="product-status ${product.is_available ? 'status-available' : 'status-unavailable'}">
                        ${product.is_available ? 'متوفر' : 'غير متوفر'}
                    </div>
                </div>
                
                <div class="product-content">
                    <div class="product-header">
                        <h3 class="product-name">${product.name}</h3>
                        <div class="product-price">${product.price} جنيه</div>
                    </div>
                    
                    <p class="product-description">${product.description}</p>
                    
                    <div class="product-meta">
                        <div class="product-category">
                            <i class="fas fa-tag"></i>
                            ${categoryName}
                        </div>
                        <div class="preparation-time">
                            <i class="fas fa-clock"></i>
                            ${product.preparation_time || 15} دقيقة
                        </div>
                    </div>
                    
                    ${product.is_featured ? '<div class="featured-badge">مميز</div>' : ''}
                    
                    <div class="product-actions">
                        <button class="btn-action btn-edit" onclick="advancedMenuManager.editProduct('${product.id}')">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </button>
                        <button class="btn-action btn-images" onclick="advancedImageManager.openGalleryModal('${product.id}')">
                            <i class="fas fa-images"></i>
                            الصور
                        </button>
                        <button class="btn-action btn-toggle ${product.is_available ? '' : 'unavailable'}" 
                                onclick="advancedMenuManager.toggleAvailability('${product.id}')">
                            <i class="fas fa-${product.is_available ? 'eye-slash' : 'eye'}"></i>
                            ${product.is_available ? 'إخفاء' : 'إظهار'}
                        </button>
                        <button class="btn-action btn-delete" onclick="advancedMenuManager.deleteProduct('${product.id}')">
                            <i class="fas fa-trash"></i>
                            حذف
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    createNoProductsHTML() {
        return `
            <div class="no-products">
                <i class="fas fa-utensils"></i>
                <h3>لا توجد منتجات</h3>
                <p>ابدأ بإضافة منتجات جديدة لقائمة المطعم</p>
                <button class="btn-primary" onclick="openProductModal()">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </button>
            </div>
        `;
    }

    updatePagination() {
        const container = document.getElementById('paginationContainer');
        if (!container) return;

        const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        
        if (totalPages <= 1) {
            container.style.display = 'none';
            return;
        }

        container.style.display = 'flex';
        
        // Update pagination info
        const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
        const paginationInfo = document.getElementById('paginationInfo');
        if (paginationInfo) {
            paginationInfo.textContent = `عرض ${startItem}-${endItem} من ${this.totalItems} منتج`;
        }

        // Update page numbers
        const pageNumbers = document.getElementById('pageNumbers');
        if (pageNumbers) {
            pageNumbers.innerHTML = this.generatePageNumbers(totalPages);
        }

        // Update navigation buttons
        const prevBtn = container.querySelector('.page-btn:first-child');
        const nextBtn = container.querySelector('.page-btn:last-child');
        
        if (prevBtn) prevBtn.disabled = this.currentPage === 1;
        if (nextBtn) nextBtn.disabled = this.currentPage === totalPages;
    }

    generatePageNumbers(totalPages) {
        let html = '';
        const maxVisible = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(totalPages, startPage + maxVisible - 1);

        if (endPage - startPage + 1 < maxVisible) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <button class="page-number ${i === this.currentPage ? 'active' : ''}" 
                        onclick="advancedMenuManager.goToPage(${i})">
                    ${i}
                </button>
            `;
        }

        return html;
    }

    goToPage(page) {
        this.currentPage = page;
        this.renderProducts();
        this.updatePagination();
    }

    loadPreviousPage() {
        if (this.currentPage > 1) {
            this.goToPage(this.currentPage - 1);
        }
    }

    loadNextPage() {
        const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        if (this.currentPage < totalPages) {
            this.goToPage(this.currentPage + 1);
        }
    }

    changeView(view) {
        this.currentView = view;
        
        // Update view buttons
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');
        
        this.renderProducts();
    }

    // Product management methods
    openProductModal(productId = null) {
        this.currentProductId = productId;
        const modal = document.getElementById('productModal');
        const title = document.getElementById('productModalTitle');
        
        if (productId) {
            title.textContent = 'تعديل المنتج';
            this.populateProductForm(productId);
        } else {
            title.textContent = 'إضافة منتج جديد';
            this.resetProductForm();
        }
        
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    closeProductModal() {
        const modal = document.getElementById('productModal');
        modal.classList.remove('show');
        document.body.style.overflow = '';
        this.resetProductForm();
        this.currentProductId = null;
    }

    populateProductForm(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        document.getElementById('productName').value = product.name || '';
        document.getElementById('productNameEn').value = product.name_en || '';
        document.getElementById('productDescription').value = product.description || '';
        document.getElementById('productDescriptionEn').value = product.description_en || '';
        document.getElementById('productPrice').value = product.price || '';
        document.getElementById('productCategory').value = product.category_id || '';
        document.getElementById('preparationTime').value = product.preparation_time || 15;
        document.getElementById('calories').value = product.calories || '';
        document.getElementById('ingredients').value = product.ingredients?.join(', ') || '';
        document.getElementById('allergens').value = product.allergens?.join(', ') || '';
        document.getElementById('tags').value = product.tags?.join(', ') || '';
        document.getElementById('isAvailable').checked = product.is_available !== false;
        document.getElementById('isFeatured').checked = product.is_featured === true;

        // Display current images
        this.displayCurrentImages(product.images || []);
    }

    resetProductForm() {
        document.getElementById('productForm').reset();
        document.getElementById('isAvailable').checked = true;
        document.getElementById('isFeatured').checked = false;
        document.getElementById('currentImages').innerHTML = '';
    }

    displayCurrentImages(images) {
        const container = document.getElementById('currentImages');
        if (!container) return;

        container.innerHTML = images.map(image => `
            <div class="current-image">
                <img src="${image.thumbnail_url || image.image_url}" alt="${image.alt_text || ''}">
                <button class="remove-btn" onclick="advancedMenuManager.removeProductImage('${image.id}')">
                    <i class="fas fa-times"></i>
                </button>
                ${image.is_primary ? '<div class="primary-badge">رئيسية</div>' : ''}
            </div>
        `).join('');
    }

    async saveProduct() {
        try {
            this.showLoading(true);
            
            const formData = this.getProductFormData();
            
            if (!this.validateProductData(formData)) {
                this.showLoading(false);
                return;
            }

            if (this.currentProductId) {
                await this.updateProduct(this.currentProductId, formData);
            } else {
                await this.createProduct(formData);
            }

            this.showLoading(false);
            this.closeProductModal();
            await this.loadProducts();
            this.applyFilters();
            
        } catch (error) {
            console.error('Error saving product:', error);
            this.showMessage('فشل في حفظ المنتج', 'error');
            this.showLoading(false);
        }
    }

    getProductFormData() {
        return {
            name: document.getElementById('productName').value.trim(),
            name_en: document.getElementById('productNameEn').value.trim(),
            description: document.getElementById('productDescription').value.trim(),
            description_en: document.getElementById('productDescriptionEn').value.trim(),
            price: parseFloat(document.getElementById('productPrice').value),
            category_id: document.getElementById('productCategory').value,
            preparation_time: parseInt(document.getElementById('preparationTime').value) || 15,
            calories: parseInt(document.getElementById('calories').value) || null,
            ingredients: document.getElementById('ingredients').value.split(',').map(s => s.trim()).filter(s => s),
            allergens: document.getElementById('allergens').value.split(',').map(s => s.trim()).filter(s => s),
            tags: document.getElementById('tags').value.split(',').map(s => s.trim()).filter(s => s),
            is_available: document.getElementById('isAvailable').checked,
            is_featured: document.getElementById('isFeatured').checked
        };
    }

    validateProductData(data) {
        if (!data.name) {
            this.showMessage('يرجى إدخال اسم المنتج', 'error');
            return false;
        }

        if (!data.description) {
            this.showMessage('يرجى إدخال وصف المنتج', 'error');
            return false;
        }

        if (!data.price || data.price <= 0) {
            this.showMessage('يرجى إدخال سعر صحيح', 'error');
            return false;
        }

        if (!data.category_id) {
            this.showMessage('يرجى اختيار قسم المنتج', 'error');
            return false;
        }

        return true;
    }

    async createProduct(productData) {
        const newProduct = {
            id: Date.now().toString(),
            ...productData,
            images: [],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        this.products.unshift(newProduct);
        localStorage.setItem('restaurant_products', JSON.stringify(this.products));
        this.showMessage('تم إضافة المنتج بنجاح', 'success');
    }

    async updateProduct(productId, productData) {
        const productIndex = this.products.findIndex(p => p.id === productId);
        if (productIndex > -1) {
            this.products[productIndex] = {
                ...this.products[productIndex],
                ...productData,
                updated_at: new Date().toISOString()
            };

            localStorage.setItem('restaurant_products', JSON.stringify(this.products));
            this.showMessage('تم تحديث المنتج بنجاح', 'success');
        }
    }

    editProduct(productId) {
        this.openProductModal(productId);
    }

    async toggleAvailability(productId) {
        try {
            const product = this.products.find(p => p.id === productId);
            if (!product) return;

            product.is_available = !product.is_available;
            product.updated_at = new Date().toISOString();
            
            localStorage.setItem('restaurant_products', JSON.stringify(this.products));
            this.applyFilters();
            
            const status = product.is_available ? 'متوفر' : 'غير متوفر';
            this.showMessage(`تم تغيير حالة المنتج إلى ${status}`, 'success');
            
        } catch (error) {
            console.error('Error toggling availability:', error);
            this.showMessage('فشل في تحديث حالة المنتج', 'error');
        }
    }

    async deleteProduct(productId) {
        if (!confirm('هل أنت متأكد من حذف هذا المنتج؟\nلا يمكن التراجع عن هذا الإجراء.')) {
            return;
        }

        try {
            this.showLoading(true);
            
            this.products = this.products.filter(p => p.id !== productId);
            localStorage.setItem('restaurant_products', JSON.stringify(this.products));
            
            this.applyFilters();
            this.showMessage('تم حذف المنتج بنجاح', 'success');
            this.showLoading(false);
            
        } catch (error) {
            console.error('Error deleting product:', error);
            this.showMessage('فشل في حذف المنتج', 'error');
            this.showLoading(false);
        }
    }

    // Category management methods
    openCategoryModal() {
        const modal = document.getElementById('categoryModal');
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
        this.loadCategoriesDisplay();
    }

    closeCategoryModal() {
        const modal = document.getElementById('categoryModal');
        modal.classList.remove('show');
        document.body.style.overflow = '';
        document.getElementById('categoryForm').reset();
    }

    loadCategoriesDisplay() {
        const grid = document.getElementById('categoriesGrid');
        if (!grid) return;

        grid.innerHTML = this.categories.map(category => `
            <div class="category-item">
                <div class="category-name">${category.name}</div>
                <div class="category-description">${category.description || 'لا يوجد وصف'}</div>
                <div class="category-actions">
                    <button class="btn-edit-category" onclick="advancedMenuManager.editCategory('${category.id}')">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                    <button class="btn-delete-category" onclick="advancedMenuManager.deleteCategory('${category.id}')">
                        <i class="fas fa-trash"></i>
                        حذف
                    </button>
                </div>
            </div>
        `).join('');
    }

    async saveCategory() {
        try {
            const formData = {
                name: document.getElementById('categoryName').value.trim(),
                name_en: document.getElementById('categoryNameEn').value.trim(),
                description: document.getElementById('categoryDescription').value.trim()
            };

            if (!formData.name) {
                this.showMessage('يرجى إدخال اسم القسم', 'error');
                return;
            }

            const newCategory = {
                id: Date.now().toString(),
                ...formData,
                created_at: new Date().toISOString()
            };

            this.categories.push(newCategory);
            localStorage.setItem('restaurant_categories', JSON.stringify(this.categories));
            
            this.populateCategoryFilters();
            this.loadCategoriesDisplay();
            document.getElementById('categoryForm').reset();
            
            this.showMessage('تم إضافة القسم بنجاح', 'success');
            
        } catch (error) {
            console.error('Error saving category:', error);
            this.showMessage('فشل في حفظ القسم', 'error');
        }
    }

    async deleteCategory(categoryId) {
        // Check if category has products
        const hasProducts = this.products.some(p => p.category_id === categoryId);
        
        if (hasProducts) {
            this.showMessage('لا يمكن حذف قسم يحتوي على منتجات', 'error');
            return;
        }

        if (!confirm('هل أنت متأكد من حذف هذا القسم؟')) {
            return;
        }

        try {
            this.categories = this.categories.filter(c => c.id !== categoryId);
            localStorage.setItem('restaurant_categories', JSON.stringify(this.categories));
            
            this.populateCategoryFilters();
            this.loadCategoriesDisplay();
            
            this.showMessage('تم حذف القسم بنجاح', 'success');
            
        } catch (error) {
            console.error('Error deleting category:', error);
            this.showMessage('فشل في حذف القسم', 'error');
        }
    }

    // Utility methods
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            if (show) {
                overlay.classList.add('show');
            } else {
                overlay.classList.remove('show');
            }
        }
    }

    showMessage(message, type = 'info') {
        // Use existing notification system
        if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            alert(message);
        }
    }
}

// Global functions for HTML onclick events
function openProductModal() {
    advancedMenuManager.openProductModal();
}

function closeProductModal() {
    advancedMenuManager.closeProductModal();
}

function openCategoryModal() {
    advancedMenuManager.openCategoryModal();
}

function closeCategoryModal() {
    advancedMenuManager.closeCategoryModal();
}

function changeView(view) {
    advancedMenuManager.changeView(view);
}

function loadPreviousPage() {
    advancedMenuManager.loadPreviousPage();
}

function loadNextPage() {
    advancedMenuManager.loadNextPage();
}

// Initialize advanced menu manager
const advancedMenuManager = new AdvancedMenuManager();

// Make available globally
window.advancedMenuManager = advancedMenuManager;
