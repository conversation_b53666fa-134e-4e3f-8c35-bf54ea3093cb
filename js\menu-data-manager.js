// Advanced Menu Data Management with Real Database Integration
// إدارة بيانات القائمة المتقدمة مع تكامل قاعدة البيانات الحقيقية

class MenuDataManager {
    constructor() {
        this.categories = [];
        this.products = [];
        this.isLoaded = false;
        this.useDatabase = true;
        this.supabase = null;
        this.firebase = null;
        
        this.init();
    }

    async init() {
        try {
            // Wait for database managers to be ready
            await this.waitForDatabases();
            
            if (this.useDatabase && this.supabase) {
                await this.loadFromDatabase();
            } else {
                await this.loadFallbackData();
            }
            
            this.isLoaded = true;
            console.log('Menu data loaded successfully');
            
            // Trigger data loaded event
            window.dispatchEvent(new CustomEvent('menuDataLoaded', {
                detail: { categories: this.categories, products: this.products }
            }));
            
        } catch (error) {
            console.error('Error loading menu data:', error);
            await this.loadFallbackData();
        }
    }

    async waitForDatabases() {
        return new Promise((resolve) => {
            const checkDatabases = () => {
                if (window.supabaseManager && window.firebaseManager) {
                    this.supabase = window.supabaseManager;
                    this.firebase = window.firebaseManager;
                    resolve();
                } else {
                    setTimeout(checkDatabases, 100);
                }
            };
            checkDatabases();
        });
    }

    async loadFromDatabase() {
        try {
            console.log('Loading data from Supabase...');
            
            // Load categories
            const categoriesResult = await this.supabase.getCategories();
            if (categoriesResult.success) {
                this.categories = categoriesResult.data;
                console.log('Categories loaded from database:', this.categories.length);
            } else {
                throw new Error('Failed to load categories');
            }
            
            // Load products
            const productsResult = await this.supabase.getProducts();
            if (productsResult.success) {
                this.products = productsResult.data;
                console.log('Products loaded from database:', this.products.length);
            } else {
                throw new Error('Failed to load products');
            }
            
            // Cache data locally
            localStorage.setItem('restaurant_categories', JSON.stringify(this.categories));
            localStorage.setItem('restaurant_products', JSON.stringify(this.products));
            
        } catch (error) {
            console.error('Database loading failed, using fallback:', error);
            throw error;
        }
    }

    async loadFallbackData() {
        console.log('Loading fallback data...');
        
        // Load from localStorage first
        const storedCategories = localStorage.getItem('restaurant_categories');
        const storedProducts = localStorage.getItem('restaurant_products');
        
        if (storedCategories && storedProducts) {
            this.categories = JSON.parse(storedCategories);
            this.products = JSON.parse(storedProducts);
            console.log('Data loaded from localStorage');
            return;
        }
        
        // Default categories
        this.categories = [
            {
                id: '1',
                name: 'المقبلات',
                name_en: 'Appetizers',
                description: 'مقبلات شهية ومتنوعة',
                sort_order: 1,
                is_active: true
            },
            {
                id: '2',
                name: 'الأطباق الرئيسية',
                name_en: 'Main Dishes',
                description: 'أطباق رئيسية مميزة',
                sort_order: 2,
                is_active: true
            },
            {
                id: '3',
                name: 'المشروبات',
                name_en: 'Beverages',
                description: 'مشروبات ساخنة وباردة',
                sort_order: 3,
                is_active: true
            },
            {
                id: '4',
                name: 'الحلويات',
                name_en: 'Desserts',
                description: 'حلويات لذيذة ومتنوعة',
                sort_order: 4,
                is_active: true
            }
        ];

        // Default products
        this.products = [
            {
                id: '1',
                name: 'حمص بالطحينة',
                name_en: 'Hummus with Tahini',
                description: 'حمص طازج مع الطحينة والزيت والبقدونس',
                description_en: 'Fresh hummus with tahini, oil and parsley',
                price: 25.00,
                category_id: '1',
                is_available: true,
                is_featured: false,
                preparation_time: 5,
                calories: 180,
                ingredients: ['حمص', 'طحينة', 'زيت زيتون', 'ليمون', 'ثوم', 'بقدونس'],
                allergens: ['سمسم'],
                tags: ['نباتي', 'صحي'],
                images: [],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '2',
                name: 'متبل باذنجان',
                name_en: 'Baba Ganoush',
                description: 'باذنجان مشوي مع الطحينة والثوم والليمون',
                description_en: 'Grilled eggplant with tahini, garlic and lemon',
                price: 30.00,
                category_id: '1',
                is_available: true,
                is_featured: false,
                preparation_time: 10,
                calories: 150,
                ingredients: ['باذنجان', 'طحينة', 'ثوم', 'ليمون', 'زيت زيتون'],
                allergens: ['سمسم'],
                tags: ['نباتي', 'مشوي'],
                images: [],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '3',
                name: 'كباب لحم',
                name_en: 'Meat Kebab',
                description: 'كباب لحم مشوي مع الأرز والسلطة',
                description_en: 'Grilled meat kebab with rice and salad',
                price: 85.00,
                category_id: '2',
                is_available: true,
                is_featured: true,
                preparation_time: 20,
                calories: 450,
                ingredients: ['لحم بقري', 'أرز', 'خضروات', 'بصل', 'بهارات'],
                allergens: [],
                tags: ['مشوي', 'بروتين'],
                images: [],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '4',
                name: 'فراخ مشوية',
                name_en: 'Grilled Chicken',
                description: 'فراخ مشوية مع البطاطس والخضروات',
                description_en: 'Grilled chicken with potatoes and vegetables',
                price: 75.00,
                category_id: '2',
                is_available: true,
                is_featured: true,
                preparation_time: 25,
                calories: 380,
                ingredients: ['دجاج', 'بطاطس', 'جزر', 'فلفل', 'بهارات'],
                allergens: [],
                tags: ['مشوي', 'صحي'],
                images: [],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '5',
                name: 'عصير برتقال طازج',
                name_en: 'Fresh Orange Juice',
                description: 'عصير برتقال طبيعي 100%',
                description_en: '100% natural orange juice',
                price: 20.00,
                category_id: '3',
                is_available: true,
                is_featured: false,
                preparation_time: 3,
                calories: 110,
                ingredients: ['برتقال طازج'],
                allergens: [],
                tags: ['طبيعي', 'فيتامين سي'],
                images: [],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '6',
                name: 'شاي بالنعناع',
                name_en: 'Mint Tea',
                description: 'شاي أحمر بالنعناع الطازج',
                description_en: 'Black tea with fresh mint',
                price: 15.00,
                category_id: '3',
                is_available: true,
                is_featured: false,
                preparation_time: 5,
                calories: 5,
                ingredients: ['شاي أحمر', 'نعناع طازج', 'سكر'],
                allergens: [],
                tags: ['ساخن', 'منعش'],
                images: [],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '7',
                name: 'كنافة بالجبن',
                name_en: 'Cheese Kunafa',
                description: 'كنافة طازجة بالجبن والقطر',
                description_en: 'Fresh kunafa with cheese and syrup',
                price: 40.00,
                category_id: '4',
                is_available: true,
                is_featured: true,
                preparation_time: 15,
                calories: 320,
                ingredients: ['كنافة', 'جبن', 'قطر', 'فستق'],
                allergens: ['جلوتين', 'ألبان'],
                tags: ['حلو', 'تقليدي'],
                images: [],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '8',
                name: 'مهلبية',
                name_en: 'Muhallabia',
                description: 'مهلبية بالحليب والفستق',
                description_en: 'Milk pudding with pistachios',
                price: 25.00,
                category_id: '4',
                is_available: true,
                is_featured: false,
                preparation_time: 10,
                calories: 180,
                ingredients: ['حليب', 'نشا', 'سكر', 'فستق', 'ماء ورد'],
                allergens: ['ألبان', 'مكسرات'],
                tags: ['كريمي', 'بارد'],
                images: [],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            }
        ];

        // Save to localStorage
        localStorage.setItem('restaurant_categories', JSON.stringify(this.categories));
        localStorage.setItem('restaurant_products', JSON.stringify(this.products));
        
        console.log('Fallback data loaded and cached');
    }

    // Get methods
    getCategories() {
        return this.categories.filter(cat => cat.is_active !== false);
    }

    getProducts(categoryId = null) {
        let products = this.products.filter(product => product.is_available !== false);
        
        if (categoryId) {
            products = products.filter(product => product.category_id === categoryId);
        }
        
        return products;
    }

    getFeaturedProducts() {
        return this.products.filter(product => 
            product.is_featured === true && product.is_available !== false
        );
    }

    getProductById(id) {
        return this.products.find(product => product.id === id);
    }

    getCategoryById(id) {
        return this.categories.find(category => category.id === id);
    }

    searchProducts(query) {
        const searchTerm = query.toLowerCase();
        return this.products.filter(product => 
            product.is_available !== false && (
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm) ||
                (product.name_en && product.name_en.toLowerCase().includes(searchTerm)) ||
                (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
            )
        );
    }

    // Refresh data from database
    async refreshData() {
        if (this.useDatabase && this.supabase) {
            try {
                await this.loadFromDatabase();
                window.dispatchEvent(new CustomEvent('menuDataRefreshed', {
                    detail: { categories: this.categories, products: this.products }
                }));
                return true;
            } catch (error) {
                console.error('Failed to refresh data:', error);
                return false;
            }
        }
        return false;
    }

    // Check if data is loaded
    isDataLoaded() {
        return this.isLoaded;
    }

    // Wait for data to be loaded
    async waitForData() {
        return new Promise((resolve) => {
            if (this.isLoaded) {
                resolve();
            } else {
                const checkLoaded = () => {
                    if (this.isLoaded) {
                        resolve();
                    } else {
                        setTimeout(checkLoaded, 100);
                    }
                };
                checkLoaded();
            }
        });
    }
}

// Initialize menu data manager
const menuDataManager = new MenuDataManager();

// Make available globally
window.menuDataManager = menuDataManager;

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MenuDataManager;
}
