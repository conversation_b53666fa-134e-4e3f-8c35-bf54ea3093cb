// Complete Product Manager with Image Support
// مدير المنتجات الكامل مع دعم الصور

class CompleteProductManager {
    constructor() {
        this.products = [];
        this.categories = [
            { id: '1', name: 'المقبلات', name_en: 'Appetizers' },
            { id: '2', name: 'الأطباق الرئيسية', name_en: 'Main Dishes' },
            { id: '3', name: 'المشروبات', name_en: 'Beverages' },
            { id: '4', name: 'الحلويات', name_en: 'Desserts' }
        ];
        this.currentProduct = null;
        this.storageManager = null;
        
        console.log('🚀 CompleteProductManager initializing...');
        this.init();
    }

    async init() {
        try {
            // Wait for storage manager
            await this.waitForStorageManager();
            
            // Load existing products
            this.loadProducts();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Render initial display
            this.renderProducts();
            
            console.log('✅ CompleteProductManager initialized');
            
        } catch (error) {
            console.error('❌ Error initializing CompleteProductManager:', error);
        }
    }

    async waitForStorageManager() {
        return new Promise((resolve) => {
            const checkStorage = () => {
                if (window.supabaseStorageManager) {
                    this.storageManager = window.supabaseStorageManager;
                    resolve();
                } else {
                    setTimeout(checkStorage, 100);
                }
            };
            checkStorage();
        });
    }

    loadProducts() {
        try {
            const savedProducts = localStorage.getItem('restaurant_products');
            if (savedProducts && savedProducts !== 'undefined') {
                this.products = JSON.parse(savedProducts);
            } else {
                this.products = this.getDefaultProducts();
                this.saveProducts();
            }
            console.log(`📦 Loaded ${this.products.length} products`);
        } catch (error) {
            console.error('Error loading products:', error);
            this.products = this.getDefaultProducts();
        }
    }

    getDefaultProducts() {
        return [
            {
                id: 'p1',
                name: 'حمص بالطحينة',
                description: 'حمص طازج مع الطحينة والزيت والبقدونس',
                price: 25,
                category_id: '1',
                category: 'المقبلات',
                icon: 'fas fa-seedling',
                is_available: true,
                is_featured: false,
                images: [],
                image_url: null,
                created_at: new Date().toISOString()
            },
            {
                id: 'p2',
                name: 'كباب مشوي',
                description: 'كباب لحم مشوي مع الخضار والأرز',
                price: 85,
                category_id: '2',
                category: 'الأطباق الرئيسية',
                icon: 'fas fa-drumstick-bite',
                is_available: true,
                is_featured: true,
                images: [],
                image_url: null,
                created_at: new Date().toISOString()
            },
            {
                id: 'p3',
                name: 'شاي أحمر',
                description: 'شاي أحمر طازج مع النعناع',
                price: 15,
                category_id: '3',
                category: 'المشروبات',
                icon: 'fas fa-coffee',
                is_available: true,
                is_featured: false,
                images: [],
                image_url: null,
                created_at: new Date().toISOString()
            }
        ];
    }

    saveProducts() {
        try {
            localStorage.setItem('restaurant_products', JSON.stringify(this.products));
            // Trigger update event for main site
            window.dispatchEvent(new CustomEvent('menuDataRefreshed', {
                detail: { products: this.products }
            }));
        } catch (error) {
            console.error('Error saving products:', error);
        }
    }

    setupEventListeners() {
        // Add product button
        const addBtn = document.getElementById('addProductBtn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.openProductModal());
        }

        // Product form submission
        const productForm = document.getElementById('productForm');
        if (productForm) {
            productForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleProductSubmit(e.target);
            });
        }

        // Modal close buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('close-modal') || e.target.classList.contains('modal-overlay')) {
                this.closeProductModal();
            }
        });

        // Product action buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('edit-product-btn')) {
                const productId = e.target.dataset.productId;
                this.editProduct(productId);
            } else if (e.target.classList.contains('delete-product-btn')) {
                const productId = e.target.dataset.productId;
                this.deleteProduct(productId);
            } else if (e.target.classList.contains('toggle-availability-btn')) {
                const productId = e.target.dataset.productId;
                this.toggleAvailability(productId);
            }
        });

        // Image upload
        const imageInput = document.getElementById('productImages');
        if (imageInput) {
            imageInput.addEventListener('change', (e) => {
                this.handleImageSelection(e.target.files);
            });
        }
    }

    renderProducts() {
        const container = document.getElementById('productsContainer');
        if (!container) {
            console.warn('Products container not found');
            return;
        }

        if (this.products.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-utensils"></i>
                    <h3>لا توجد منتجات</h3>
                    <p>ابدأ بإضافة منتج جديد</p>
                    <button class="btn btn-primary" onclick="completeProductManager.openProductModal()">
                        <i class="fas fa-plus"></i> إضافة منتج
                    </button>
                </div>
            `;
            return;
        }

        const productsHtml = this.products.map(product => this.renderProductCard(product)).join('');
        container.innerHTML = productsHtml;
    }

    renderProductCard(product) {
        const imageHtml = product.image_url ? 
            `<img src="${product.image_url}" alt="${product.name}" class="product-image">` :
            `<div class="product-icon"><i class="${product.icon || 'fas fa-utensils'}"></i></div>`;

        const availabilityClass = product.is_available ? 'available' : 'unavailable';
        const featuredBadge = product.is_featured ? '<span class="featured-badge">مميز</span>' : '';

        return `
            <div class="product-card ${availabilityClass}" data-product-id="${product.id}">
                <div class="product-image-container">
                    ${imageHtml}
                    ${featuredBadge}
                </div>
                <div class="product-info">
                    <h3>${product.name}</h3>
                    <p class="product-description">${product.description}</p>
                    <div class="product-meta">
                        <span class="product-price">${product.price} جنيه</span>
                        <span class="product-category">${product.category || this.getCategoryName(product.category_id)}</span>
                    </div>
                </div>
                <div class="product-actions">
                    <button class="btn btn-sm btn-primary edit-product-btn" data-product-id="${product.id}">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm ${product.is_available ? 'btn-warning' : 'btn-success'} toggle-availability-btn" data-product-id="${product.id}">
                        <i class="fas ${product.is_available ? 'fa-eye-slash' : 'fa-eye'}"></i>
                        ${product.is_available ? 'إخفاء' : 'إظهار'}
                    </button>
                    <button class="btn btn-sm btn-danger delete-product-btn" data-product-id="${product.id}">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
    }

    getCategoryName(categoryId) {
        const category = this.categories.find(cat => cat.id === categoryId);
        return category ? category.name : 'عام';
    }

    openProductModal(productId = null) {
        this.currentProduct = productId;
        const modal = document.getElementById('productModal');
        const form = document.getElementById('productForm');
        
        if (!modal || !form) {
            console.error('Product modal or form not found');
            return;
        }

        // Reset form
        form.reset();
        
        if (productId) {
            // Edit mode
            const product = this.products.find(p => p.id === productId);
            if (product) {
                this.populateForm(product);
            }
        }

        modal.style.display = 'flex';
    }

    populateForm(product) {
        const form = document.getElementById('productForm');
        if (!form) return;

        form.querySelector('#productName').value = product.name || '';
        form.querySelector('#productDescription').value = product.description || '';
        form.querySelector('#productPrice').value = product.price || '';
        form.querySelector('#productCategory').value = product.category_id || '';
        form.querySelector('#productIcon').value = product.icon || '';
        
        const availableCheckbox = form.querySelector('#productAvailable');
        if (availableCheckbox) {
            availableCheckbox.checked = product.is_available !== false;
        }
        
        const featuredCheckbox = form.querySelector('#productFeatured');
        if (featuredCheckbox) {
            featuredCheckbox.checked = product.is_featured || false;
        }
    }

    closeProductModal() {
        const modal = document.getElementById('productModal');
        if (modal) {
            modal.style.display = 'none';
        }
        this.currentProduct = null;
    }

    async handleProductSubmit(form) {
        try {
            const formData = new FormData(form);
            const productData = this.extractProductData(formData);
            
            // Handle image upload
            const imageFiles = form.querySelector('#productImages').files;
            if (imageFiles.length > 0) {
                await this.uploadProductImages(productData, imageFiles);
            }
            
            if (this.currentProduct) {
                // Update existing product
                this.updateProduct(this.currentProduct, productData);
            } else {
                // Create new product
                this.createProduct(productData);
            }
            
            this.closeProductModal();
            this.renderProducts();
            this.saveProducts();
            
        } catch (error) {
            console.error('Error saving product:', error);
            this.showMessage('خطأ في حفظ المنتج', 'error');
        }
    }

    extractProductData(formData) {
        return {
            name: formData.get('name'),
            description: formData.get('description'),
            price: parseFloat(formData.get('price')) || 0,
            category_id: formData.get('category_id'),
            category: this.getCategoryName(formData.get('category_id')),
            icon: formData.get('icon') || 'fas fa-utensils',
            is_available: formData.get('is_available') === 'on',
            is_featured: formData.get('is_featured') === 'on'
        };
    }

    async uploadProductImages(productData, imageFiles) {
        if (!this.storageManager || !this.storageManager.isInitialized) {
            console.warn('Storage manager not available');
            return;
        }

        try {
            const uploadResults = await this.storageManager.uploadMultipleImages(Array.from(imageFiles));
            if (uploadResults.success && uploadResults.data.length > 0) {
                productData.images = uploadResults.data.map((img, index) => ({
                    url: img.url,
                    path: img.path,
                    name: img.name,
                    isPrimary: index === 0
                }));
                productData.image_url = uploadResults.data[0].url;
                this.showMessage('تم رفع الصور بنجاح', 'success');
            }
        } catch (error) {
            console.error('Error uploading images:', error);
            this.showMessage('خطأ في رفع الصور', 'error');
        }
    }

    createProduct(productData) {
        const newProduct = {
            id: 'p_' + Date.now(),
            ...productData,
            images: productData.images || [],
            image_url: productData.image_url || null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        this.products.unshift(newProduct);
        this.showMessage('تم إضافة المنتج بنجاح', 'success');
        
        // Notify main site
        window.dispatchEvent(new CustomEvent('productAdded', { detail: newProduct }));
    }

    updateProduct(productId, productData) {
        const index = this.products.findIndex(p => p.id === productId);
        if (index !== -1) {
            this.products[index] = {
                ...this.products[index],
                ...productData,
                updated_at: new Date().toISOString()
            };
            this.showMessage('تم تحديث المنتج بنجاح', 'success');
            
            // Notify main site
            window.dispatchEvent(new CustomEvent('productUpdated', { detail: this.products[index] }));
        }
    }

    deleteProduct(productId) {
        if (!confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            return;
        }

        const index = this.products.findIndex(p => p.id === productId);
        if (index !== -1) {
            const deletedProduct = this.products[index];
            this.products.splice(index, 1);
            this.renderProducts();
            this.saveProducts();
            this.showMessage('تم حذف المنتج بنجاح', 'success');
            
            // Notify main site
            window.dispatchEvent(new CustomEvent('productDeleted', { detail: { id: productId } }));
        }
    }

    toggleAvailability(productId) {
        const product = this.products.find(p => p.id === productId);
        if (product) {
            product.is_available = !product.is_available;
            product.updated_at = new Date().toISOString();
            this.renderProducts();
            this.saveProducts();
            
            const status = product.is_available ? 'متوفر' : 'غير متوفر';
            this.showMessage(`تم تغيير حالة ${product.name} إلى ${status}`, 'success');
            
            // Notify main site
            window.dispatchEvent(new CustomEvent('productUpdated', { detail: product }));
        }
    }

    editProduct(productId) {
        this.openProductModal(productId);
    }

    handleImageSelection(files) {
        const preview = document.getElementById('imagePreview');
        if (!preview) return;

        preview.innerHTML = '';
        
        Array.from(files).forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.style.cssText = 'width: 80px; height: 80px; object-fit: cover; border-radius: 8px; margin: 5px;';
                    preview.appendChild(img);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    showMessage(message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type}`;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10000;
            background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#fff3cd'};
            color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#856404'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#ffeaa7'};
        `;
        messageDiv.textContent = message;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }

    // Public API
    getProducts() {
        return this.products;
    }

    getCategories() {
        return this.categories;
    }

    getProductById(id) {
        return this.products.find(p => p.id === id);
    }
}

// Initialize and make available globally
const completeProductManager = new CompleteProductManager();
window.completeProductManager = completeProductManager;

console.log('✅ CompleteProductManager loaded');
