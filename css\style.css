/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: #ffd700;
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.logo p {
    font-size: 0.9rem;
    opacity: 0.9;
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.cart-btn {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    color: white;
    padding: 12px 20px;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
}

.cart-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.cart-count {
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

.admin-link {
    color: white;
    text-decoration: none;
    padding: 10px 15px;
    border-radius: 25px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.admin-link:hover {
    background: rgba(255,255,255,0.2);
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(44, 90, 160, 0.8), rgba(30, 58, 138, 0.8)), 
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect fill="%23f0f0f0" width="1200" height="600"/><circle fill="%23e0e0e0" cx="200" cy="150" r="80"/><circle fill="%23d0d0d0" cx="800" cy="300" r="120"/><circle fill="%23e0e0e0" cx="1000" cy="100" r="60"/></svg>');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 100px 0;
    text-align: center;
}

.hero-content h2 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-content p {
    font-size: 1.3rem;
    margin-bottom: 30px;
    opacity: 0.95;
}

.cta-btn {
    background: #ffd700;
    color: #1e3a8a;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

/* Search Section */
.search-section {
    padding: 40px 0;
    background: white;
}

.search-box {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.search-box i {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #2c5aa0;
    font-size: 1.2rem;
}

.search-box input {
    width: 100%;
    padding: 15px 60px 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 50px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.search-box input:focus {
    outline: none;
    border-color: #2c5aa0;
    background: white;
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

/* Categories */
.categories {
    padding: 40px 0;
    background: white;
    border-bottom: 1px solid #e1e5e9;
}

.categories h3 {
    text-align: center;
    font-size: 2rem;
    color: #2c5aa0;
    margin-bottom: 30px;
}

.category-tabs {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.category-tab {
    background: #f8f9fa;
    border: 2px solid #e1e5e9;
    color: #666;
    padding: 15px 25px;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    font-weight: 500;
}

.category-tab:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.category-tab.active {
    background: #2c5aa0;
    color: white;
    border-color: #2c5aa0;
}

.category-tab i {
    font-size: 1.2rem;
}

/* Menu Section */
.menu {
    padding: 60px 0;
    background: #f8f9fa;
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.menu-item {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.menu-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.menu-item-image {
    height: 200px;
    background: linear-gradient(45deg, #2c5aa0, #1e3a8a);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    position: relative;
    overflow: hidden;
}

.menu-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.menu-item-image:hover img {
    transform: scale(1.05);
}

.menu-item-icon-fallback {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #2c5aa0, #1e3a8a);
    color: white;
    font-size: 3rem;
}

/* Featured items */
.menu-item.featured {
    border: 3px solid #ffd700;
    position: relative;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
}

.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    z-index: 2;
    box-shadow: 0 3px 10px rgba(255, 215, 0, 0.4);
}

/* Unavailable items */
.menu-item.unavailable {
    opacity: 0.7;
    filter: grayscale(70%);
}

.menu-item.unavailable .menu-item-image {
    background: linear-gradient(45deg, #95a5a6, #7f8c8d);
}

.menu-item.unavailable .menu-item-icon-fallback {
    background: linear-gradient(45deg, #95a5a6, #7f8c8d);
}

.unavailable-label {
    background: #e74c3c;
    color: white;
    padding: 10px 15px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: bold;
    border: none;
    cursor: not-allowed;
}

.menu-item-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle fill="rgba(255,255,255,0.1)" cx="20" cy="20" r="15"/><circle fill="rgba(255,255,255,0.05)" cx="80" cy="60" r="20"/><circle fill="rgba(255,255,255,0.08)" cx="60" cy="80" r="10"/></svg>');
}

.menu-item-content {
    padding: 25px;
}

.menu-item h4 {
    font-size: 1.4rem;
    color: #2c5aa0;
    margin-bottom: 10px;
    font-weight: 600;
}

.menu-item p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
}

.menu-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c5aa0;
}

.add-to-cart {
    background: #2c5aa0;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.add-to-cart:hover {
    background: #1e3a8a;
    transform: scale(1.05);
}

/* Cart Sidebar */
.cart-sidebar {
    position: fixed;
    top: 0;
    left: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: 2px 0 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    z-index: 2000;
    display: flex;
    flex-direction: column;
}

.cart-sidebar.open {
    left: 0;
}

.cart-header {
    padding: 20px;
    background: #2c5aa0;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
}

.close-cart {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-cart:hover {
    background: rgba(255,255,255,0.2);
}

.cart-items {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e1e5e9;
}

.cart-item-info h5 {
    color: #2c5aa0;
    margin-bottom: 5px;
}

.cart-item-info p {
    color: #666;
    font-size: 0.9rem;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.quantity-btn {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.quantity-btn:hover {
    background: #2c5aa0;
    color: white;
}

.quantity {
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.cart-footer {
    padding: 20px;
    border-top: 1px solid #e1e5e9;
    background: #f8f9fa;
}

.cart-total {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c5aa0;
    margin-bottom: 15px;
    text-align: center;
}

.checkout-btn {
    width: 100%;
    background: #25d366;
    color: white;
    border: none;
    padding: 15px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.checkout-btn:hover {
    background: #128c7e;
    transform: translateY(-2px);
}

.checkout-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 20px;
    background: #2c5aa0;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20px 20px 0 0;
}

.modal-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
}

.close-modal {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background: rgba(255,255,255,0.2);
}

.modal-body {
    padding: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c5aa0;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2c5aa0;
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.form-hint {
    display: block;
    margin-top: 5px;
    font-size: 0.85rem;
    color: #666;
    font-style: italic;
}

.form-group input[type="tel"] {
    direction: ltr;
    text-align: left;
}

/* Enhanced form validation */
.form-group input:invalid {
    border-color: #dc3545;
}

.form-group input:valid {
    border-color: #28a745;
}

.form-group input:invalid:focus {
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-group input:valid:focus {
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.order-summary {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
}

.order-summary h4 {
    color: #2c5aa0;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e1e5e9;
}

.summary-item:last-child {
    border-bottom: none;
}

.total-amount {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 2px solid #2c5aa0;
    text-align: center;
    font-size: 1.2rem;
    color: #2c5aa0;
}

.submit-order-btn {
    width: 100%;
    background: #25d366;
    color: white;
    border: none;
    padding: 15px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.submit-order-btn:hover {
    background: #128c7e;
    transform: translateY(-2px);
}

/* Overlay */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    z-index: 1500;
}

.overlay.show {
    display: block;
}

/* Footer */
.footer {
    background: #2c5aa0;
    color: white;
    padding: 40px 0 20px;
    margin-top: 60px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h4 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #ffd700;
}

.footer-section p {
    margin-bottom: 10px;
    opacity: 0.9;
}

.footer-section i {
    margin-left: 8px;
    color: #ffd700;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255,255,255,0.2);
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .header-actions {
        order: -1;
        width: 100%;
        justify-content: center;
    }

    .hero-content h2 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .category-tabs {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 10px;
        scrollbar-width: thin;
        scrollbar-color: #2c5aa0 #f1f1f1;
    }

    .category-tabs::-webkit-scrollbar {
        height: 6px;
    }

    .category-tabs::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .category-tabs::-webkit-scrollbar-thumb {
        background: #2c5aa0;
        border-radius: 3px;
    }

    .category-tab {
        flex-shrink: 0;
        font-size: 0.9rem;
        padding: 12px 20px;
    }

    .menu-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .menu-item {
        margin: 0 10px;
    }

    .cart-sidebar {
        width: 100%;
        left: -100%;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
        max-height: 85vh;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .scroll-to-top {
        bottom: 20px;
        left: 20px;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 60px 0;
    }

    .hero-content h2 {
        font-size: 1.8rem;
        line-height: 1.3;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .cta-btn {
        padding: 12px 25px;
        font-size: 1rem;
    }

    .search-box input {
        padding: 12px 50px 12px 15px;
        font-size: 0.95rem;
    }

    .categories h3 {
        font-size: 1.7rem;
    }

    .category-tab {
        padding: 10px 16px;
        font-size: 0.85rem;
    }

    .menu-item-content {
        padding: 20px;
    }

    .menu-item h4 {
        font-size: 1.2rem;
    }

    .menu-item p {
        font-size: 0.9rem;
    }

    .price {
        font-size: 1.1rem;
    }

    .add-to-cart {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .cart-header {
        padding: 15px;
    }

    .cart-header h3 {
        font-size: 1.1rem;
    }

    .cart-items {
        padding: 15px;
    }

    .cart-item {
        padding: 12px 0;
    }

    .cart-footer {
        padding: 15px;
    }

    .checkout-btn {
        padding: 12px;
        font-size: 1rem;
    }

    .modal-content {
        width: 98%;
        margin: 10px;
        max-height: 90vh;
    }

    .modal-header {
        padding: 15px;
    }

    .modal-header h3 {
        font-size: 1.1rem;
    }

    .modal-body {
        padding: 20px;
    }

    .form-group input,
    .form-group textarea {
        padding: 10px 12px;
        font-size: 0.95rem;
    }

    .submit-order-btn {
        padding: 12px;
        font-size: 1rem;
    }

    .footer {
        padding: 30px 0 15px;
    }

    .footer-section h4 {
        font-size: 1.1rem;
    }

    .footer-section p {
        font-size: 0.9rem;
    }

    .scroll-to-top {
        bottom: 15px;
        left: 15px;
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .notification {
        right: 10px;
        left: 10px;
        width: auto;
        transform: translateY(-100px);
        font-size: 0.9rem;
    }

    .notification.show {
        transform: translateY(0);
    }
}

/* Extra small devices */
@media (max-width: 360px) {
    .hero-content h2 {
        font-size: 1.6rem;
    }

    .hero-content p {
        font-size: 0.95rem;
    }

    .menu-item {
        margin: 0 5px;
    }

    .menu-item-content {
        padding: 15px;
    }

    .modal-body {
        padding: 15px;
    }

    .cart-items,
    .cart-footer {
        padding: 12px;
    }
}

/* Landscape orientation for mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        padding: 40px 0;
    }

    .hero-content h2 {
        font-size: 1.8rem;
    }

    .categories {
        padding: 30px 0;
    }

    .menu {
        padding: 40px 0;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .menu-item-image {
        background-size: cover;
    }

    .hero {
        background-attachment: scroll; /* Better performance on mobile */
    }
}

/* Print styles */
@media print {
    .header,
    .cart-sidebar,
    .modal,
    .overlay,
    .scroll-to-top,
    .notification {
        display: none !important;
    }

    .menu-item {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    .footer {
        margin-top: 20px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success Message */
.success-message {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 10px;
    margin: 20px 0;
    border: 1px solid #c3e6cb;
    display: none;
}

.success-message.show {
    display: block;
}

/* Error Message */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 10px;
    margin: 20px 0;
    border: 1px solid #f5c6cb;
    display: none;
}

.error-message.show {
    display: block;
}

/* Empty Cart Message */
.empty-cart {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-cart i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 15px;
}

.empty-cart p {
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.empty-cart button {
    background: #2c5aa0;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.empty-cart button:hover {
    background: #1e3a8a;
}

/* Enhanced Menu Item Styles */
.menu-item {
    position: relative;
    overflow: hidden;
}

.menu-item::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.menu-item:hover::before {
    animation: shimmer 0.6s ease-in-out;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
        opacity: 0;
    }
}

/* Price Badge Enhancement */
.price {
    position: relative;
    display: inline-block;
}

.price::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #2c5aa0, #ffd700);
    border-radius: 1px;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.menu-item:hover .price::after {
    transform: scaleX(1);
}

/* Add to Cart Button Enhancement */
.add-to-cart {
    position: relative;
    overflow: hidden;
}

.add-to-cart::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.add-to-cart:hover::before {
    width: 300px;
    height: 300px;
}

/* Category Tab Enhancement */
.category-tab {
    position: relative;
    overflow: hidden;
}

.category-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(44, 90, 160, 0.2), transparent);
    transition: left 0.5s ease;
}

.category-tab:hover::before {
    left: 100%;
}

/* Search Box Enhancement */
.search-box {
    position: relative;
}

.search-box::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #2c5aa0, #ffd700);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.search-box input:focus + i + ::after,
.search-box:focus-within::after {
    width: 100%;
}

/* Hero Section Enhancement */
.hero {
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><defs><pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Cart Count Animation */
.cart-count {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Loading States */
.menu-item.loading {
    opacity: 0.7;
    pointer-events: none;
}

.menu-item.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2c5aa0;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    padding: 15px 20px;
    z-index: 10000;
    transform: translateX(400px);
    transition: all 0.3s ease;
    border-left: 4px solid #2c5aa0;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: #28a745;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification.warning {
    border-left-color: #ffc107;
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: #2c5aa0;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 5px 15px rgba(44, 90, 160, 0.3);
}

.scroll-to-top:hover {
    background: #1e3a8a;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(44, 90, 160, 0.4);
}

.scroll-to-top.show {
    display: flex;
}
