// Comprehensive System Check for Mohamed Al-<PERSON>
// فحص شامل لنظام مطعم محمد الاشرافي

class SystemChecker {
    constructor() {
        this.tests = [];
        this.results = {
            passed: 0,
            failed: 0,
            warnings: 0,
            total: 0
        };
        this.currentTest = 0;
        
        this.init();
    }

    init() {
        console.log('🔍 System Checker initialized');
        this.setupTests();
        this.updateSummary();
    }

    setupTests() {
        // Frontend Tests
        this.addTest('frontend', 'HTML Structure', 'فحص بنية HTML الأساسية', () => this.checkHTMLStructure());
        this.addTest('frontend', 'CSS Loading', 'فحص تحميل ملفات CSS', () => this.checkCSSLoading());
        this.addTest('frontend', 'JavaScript Loading', 'فحص تحميل ملفات JavaScript', () => this.checkJSLoading());
        this.addTest('frontend', 'Menu Display', 'فحص عرض القائمة', () => this.checkMenuDisplay());
        this.addTest('frontend', 'Cart Functionality', 'فحص وظائف السلة', () => this.checkCartFunctionality());
        this.addTest('frontend', 'Search Feature', 'فحص ميزة البحث', () => this.checkSearchFeature());
        this.addTest('frontend', 'Category Filter', 'فحص فلترة الأقسام', () => this.checkCategoryFilter());
        this.addTest('frontend', 'Responsive Design', 'فحص التصميم المتجاوب', () => this.checkResponsiveDesign());

        // Database Tests
        this.addTest('database', 'Firebase Connection', 'فحص اتصال Firebase', () => this.checkFirebaseConnection());
        this.addTest('database', 'Supabase Connection', 'فحص اتصال Supabase', () => this.checkSupabaseConnection());
        this.addTest('database', 'Data Loading', 'فحص تحميل البيانات', () => this.checkDataLoading());
        this.addTest('database', 'Storage Access', 'فحص الوصول للتخزين', () => this.checkStorageAccess());
        this.addTest('database', 'Data Sync', 'فحص مزامنة البيانات', () => this.checkDataSync());

        // Admin Panel Tests
        this.addTest('admin', 'Authentication System', 'فحص نظام المصادقة', () => this.checkAuthSystem());
        this.addTest('admin', 'Admin Dashboard', 'فحص لوحة الإدارة', () => this.checkAdminDashboard());
        this.addTest('admin', 'Product Management', 'فحص إدارة المنتجات', () => this.checkProductManagement());
        this.addTest('admin', 'Image Upload', 'فحص رفع الصور', () => this.checkImageUpload());
        this.addTest('admin', 'Order Management', 'فحص إدارة الطلبات', () => this.checkOrderManagement());

        // WhatsApp Tests
        this.addTest('whatsapp', 'WhatsApp Integration', 'فحص تكامل الواتساب', () => this.checkWhatsAppIntegration());
        this.addTest('whatsapp', 'Message Formatting', 'فحص تنسيق الرسائل', () => this.checkMessageFormatting());
        this.addTest('whatsapp', 'Order Sending', 'فحص إرسال الطلبات', () => this.checkOrderSending());

        // Performance Tests
        this.addTest('performance', 'Page Load Speed', 'فحص سرعة تحميل الصفحة', () => this.checkPageLoadSpeed());
        this.addTest('performance', 'Image Optimization', 'فحص تحسين الصور', () => this.checkImageOptimization());
        this.addTest('performance', 'Script Performance', 'فحص أداء السكريپتات', () => this.checkScriptPerformance());
        this.addTest('performance', 'Memory Usage', 'فحص استخدام الذاكرة', () => this.checkMemoryUsage());

        // Security Tests
        this.addTest('security', 'XSS Protection', 'فحص الحماية من XSS', () => this.checkXSSProtection());
        this.addTest('security', 'Data Validation', 'فحص التحقق من البيانات', () => this.checkDataValidation());
        this.addTest('security', 'Session Security', 'فحص أمان الجلسات', () => this.checkSessionSecurity());
        this.addTest('security', 'HTTPS Usage', 'فحص استخدام HTTPS', () => this.checkHTTPSUsage());

        this.results.total = this.tests.length;
        this.updateSummary();
    }

    addTest(category, name, description, testFunction) {
        this.tests.push({
            category,
            name,
            description,
            testFunction,
            status: 'pending',
            result: null,
            error: null
        });
    }

    async runAllTests() {
        console.log('🚀 Starting comprehensive system check...');
        this.updateProgress(0, 'بدء الفحص الشامل...');
        
        this.results = { passed: 0, failed: 0, warnings: 0, total: this.tests.length };
        
        for (let i = 0; i < this.tests.length; i++) {
            const test = this.tests[i];
            this.currentTest = i;
            
            this.updateProgress((i / this.tests.length) * 100, `جاري فحص: ${test.description}`);
            
            try {
                const result = await test.testFunction();
                test.status = result.status;
                test.result = result.message;
                test.error = result.error;
                
                if (result.status === 'success') {
                    this.results.passed++;
                } else if (result.status === 'warning') {
                    this.results.warnings++;
                } else {
                    this.results.failed++;
                }
                
            } catch (error) {
                test.status = 'error';
                test.result = 'فشل في تشغيل الاختبار';
                test.error = error.message;
                this.results.failed++;
            }
            
            this.updateTestDisplay(test);
            this.updateSummary();
            
            // Small delay between tests
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        this.updateProgress(100, 'اكتمل الفحص الشامل!');
        this.generateReport();
    }

    async runQuickTest() {
        console.log('⚡ Running quick system check...');
        
        const quickTests = [
            'HTML Structure',
            'JavaScript Loading',
            'Firebase Connection',
            'Supabase Connection',
            'Menu Display',
            'Cart Functionality',
            'WhatsApp Integration'
        ];
        
        const testsToRun = this.tests.filter(test => quickTests.includes(test.name));
        
        for (const test of testsToRun) {
            try {
                const result = await test.testFunction();
                test.status = result.status;
                test.result = result.message;
                this.updateTestDisplay(test);
            } catch (error) {
                test.status = 'error';
                test.result = 'فشل في الاختبار';
                this.updateTestDisplay(test);
            }
        }
        
        this.showMessage('تم الانتهاء من الفحص السريع', 'success');
    }

    // Test Functions
    async checkHTMLStructure() {
        const requiredElements = ['header', 'main', 'footer', '#menuContainer', '#cartContainer'];
        const missing = [];
        
        for (const selector of requiredElements) {
            if (!document.querySelector(selector)) {
                missing.push(selector);
            }
        }
        
        if (missing.length === 0) {
            return { status: 'success', message: 'جميع العناصر الأساسية موجودة' };
        } else {
            return { status: 'error', message: `عناصر مفقودة: ${missing.join(', ')}` };
        }
    }

    async checkCSSLoading() {
        const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
        let loadedCount = 0;
        
        for (const stylesheet of stylesheets) {
            if (stylesheet.sheet) {
                loadedCount++;
            }
        }
        
        if (loadedCount === stylesheets.length) {
            return { status: 'success', message: `تم تحميل ${loadedCount} ملف CSS` };
        } else {
            return { status: 'warning', message: `تم تحميل ${loadedCount} من ${stylesheets.length} ملف CSS` };
        }
    }

    async checkJSLoading() {
        const requiredScripts = [
            'window.menuDataManager',
            'window.cart',
            'window.whatsappManager',
            'window.supabaseManager'
        ];
        
        const loaded = [];
        const missing = [];
        
        for (const script of requiredScripts) {
            if (eval(`typeof ${script} !== 'undefined'`)) {
                loaded.push(script);
            } else {
                missing.push(script);
            }
        }
        
        if (missing.length === 0) {
            return { status: 'success', message: 'جميع السكريپتات محملة' };
        } else {
            return { status: 'warning', message: `سكريپتات مفقودة: ${missing.join(', ')}` };
        }
    }

    async checkMenuDisplay() {
        const menuContainer = document.getElementById('menuContainer');
        if (!menuContainer) {
            return { status: 'error', message: 'حاوي القائمة غير موجود' };
        }
        
        const menuItems = menuContainer.querySelectorAll('.menu-item');
        if (menuItems.length > 0) {
            return { status: 'success', message: `تم عرض ${menuItems.length} عنصر في القائمة` };
        } else {
            return { status: 'warning', message: 'لا توجد عناصر في القائمة' };
        }
    }

    async checkCartFunctionality() {
        if (typeof window.cart === 'undefined') {
            return { status: 'error', message: 'نظام السلة غير محمل' };
        }
        
        try {
            const initialCount = window.cart.getItemCount();
            // Test adding item (if possible)
            return { status: 'success', message: `السلة تعمل بشكل صحيح (${initialCount} عنصر)` };
        } catch (error) {
            return { status: 'error', message: 'خطأ في وظائف السلة' };
        }
    }

    async checkFirebaseConnection() {
        if (typeof window.firebaseManager === 'undefined') {
            return { status: 'error', message: 'Firebase Manager غير محمل' };
        }
        
        if (window.firebaseManager.isInitialized) {
            return { status: 'success', message: 'Firebase متصل بنجاح' };
        } else {
            return { status: 'error', message: 'فشل في الاتصال بـ Firebase' };
        }
    }

    async checkSupabaseConnection() {
        if (typeof window.supabaseManager === 'undefined') {
            return { status: 'error', message: 'Supabase Manager غير محمل' };
        }
        
        if (window.supabaseManager.isInitialized) {
            return { status: 'success', message: 'Supabase متصل بنجاح' };
        } else {
            return { status: 'error', message: 'فشل في الاتصال بـ Supabase' };
        }
    }

    async checkWhatsAppIntegration() {
        if (typeof window.whatsappManager === 'undefined') {
            return { status: 'error', message: 'WhatsApp Manager غير محمل' };
        }
        
        try {
            const testMessage = window.whatsappManager.generateInvoiceMessage(
                [{ name: 'اختبار', price: 10, quantity: 1 }],
                { name: 'اختبار', phone: '01234567890' }
            );
            
            if (testMessage && testMessage.length > 0) {
                return { status: 'success', message: 'تكامل الواتساب يعمل بشكل صحيح' };
            } else {
                return { status: 'error', message: 'فشل في إنشاء رسالة الواتساب' };
            }
        } catch (error) {
            return { status: 'error', message: 'خطأ في تكامل الواتساب' };
        }
    }

    async checkDataLoading() {
        if (window.menuDataManager && window.menuDataManager.isDataLoaded()) {
            const categories = window.menuDataManager.getCategories();
            const products = window.menuDataManager.getProducts();
            return { status: 'success', message: `تم تحميل ${categories.length} فئة و ${products.length} منتج` };
        } else {
            return { status: 'error', message: 'فشل في تحميل البيانات' };
        }
    }

    async checkStorageAccess() {
        if (window.supabaseStorageManager && window.supabaseStorageManager.isInitialized) {
            return { status: 'success', message: 'الوصول للتخزين متاح' };
        } else {
            return { status: 'warning', message: 'التخزين غير متاح' };
        }
    }

    async checkDataSync() {
        try {
            const localData = localStorage.getItem('restaurant_products');
            if (localData) {
                return { status: 'success', message: 'مزامنة البيانات تعمل' };
            } else {
                return { status: 'warning', message: 'لا توجد بيانات محلية' };
            }
        } catch (error) {
            return { status: 'error', message: 'خطأ في مزامنة البيانات' };
        }
    }

    async checkAuthSystem() {
        if (window.stableAuth || window.adminAuth) {
            return { status: 'success', message: 'نظام المصادقة متاح' };
        } else {
            return { status: 'error', message: 'نظام المصادقة غير محمل' };
        }
    }

    async checkAdminDashboard() {
        // This would need to be run from admin page
        return { status: 'warning', message: 'يجب فحص لوحة الإدارة من صفحة الإدارة' };
    }

    async checkProductManagement() {
        return { status: 'warning', message: 'يجب فحص إدارة المنتجات من لوحة الإدارة' };
    }

    async checkImageUpload() {
        if (window.supabaseStorageManager) {
            return { status: 'success', message: 'نظام رفع الصور متاح' };
        } else {
            return { status: 'warning', message: 'نظام رفع الصور غير متاح' };
        }
    }

    async checkOrderManagement() {
        return { status: 'warning', message: 'يجب فحص إدارة الطلبات من لوحة الإدارة' };
    }

    async checkMessageFormatting() {
        if (window.whatsappManager) {
            try {
                const testOrder = [{ name: 'منتج تجريبي', price: 50, quantity: 2 }];
                const testCustomer = { name: 'عميل تجريبي', phone: '01234567890', address: 'عنوان تجريبي' };
                const message = window.whatsappManager.generateInvoiceMessage(testOrder, testCustomer);

                if (message.includes('منتج تجريبي') && message.includes('100')) {
                    return { status: 'success', message: 'تنسيق الرسائل يعمل بشكل صحيح' };
                } else {
                    return { status: 'error', message: 'خطأ في تنسيق الرسائل' };
                }
            } catch (error) {
                return { status: 'error', message: 'فشل في اختبار تنسيق الرسائل' };
            }
        } else {
            return { status: 'error', message: 'مدير الواتساب غير متاح' };
        }
    }

    async checkOrderSending() {
        // This is a simulation since we can't actually send
        if (window.whatsappManager && window.whatsappManager.createWhatsAppUrl) {
            return { status: 'success', message: 'آلية إرسال الطلبات متاحة' };
        } else {
            return { status: 'error', message: 'آلية إرسال الطلبات غير متاحة' };
        }
    }

    async checkPageLoadSpeed() {
        const loadTime = performance.now();
        if (loadTime < 3000) {
            return { status: 'success', message: `سرعة التحميل ممتازة (${loadTime.toFixed(0)}ms)` };
        } else if (loadTime < 5000) {
            return { status: 'warning', message: `سرعة التحميل مقبولة (${loadTime.toFixed(0)}ms)` };
        } else {
            return { status: 'error', message: `سرعة التحميل بطيئة (${loadTime.toFixed(0)}ms)` };
        }
    }

    async checkImageOptimization() {
        const images = document.querySelectorAll('img');
        let optimizedCount = 0;

        images.forEach(img => {
            if (img.loading === 'lazy' || img.getAttribute('loading') === 'lazy') {
                optimizedCount++;
            }
        });

        const percentage = images.length > 0 ? (optimizedCount / images.length) * 100 : 0;

        if (percentage > 80) {
            return { status: 'success', message: `${percentage.toFixed(0)}% من الصور محسنة` };
        } else if (percentage > 50) {
            return { status: 'warning', message: `${percentage.toFixed(0)}% من الصور محسنة` };
        } else {
            return { status: 'error', message: `${percentage.toFixed(0)}% من الصور محسنة` };
        }
    }

    async checkScriptPerformance() {
        const scripts = document.querySelectorAll('script[src]');
        const asyncCount = Array.from(scripts).filter(script => script.async || script.defer).length;
        const percentage = scripts.length > 0 ? (asyncCount / scripts.length) * 100 : 0;

        if (percentage > 70) {
            return { status: 'success', message: `${percentage.toFixed(0)}% من السكريپتات محسنة` };
        } else {
            return { status: 'warning', message: `${percentage.toFixed(0)}% من السكريپتات محسنة` };
        }
    }

    async checkMemoryUsage() {
        if (performance.memory) {
            const used = performance.memory.usedJSHeapSize / 1024 / 1024;
            if (used < 50) {
                return { status: 'success', message: `استخدام الذاكرة منخفض (${used.toFixed(1)}MB)` };
            } else if (used < 100) {
                return { status: 'warning', message: `استخدام الذاكرة متوسط (${used.toFixed(1)}MB)` };
            } else {
                return { status: 'error', message: `استخدام الذاكرة عالي (${used.toFixed(1)}MB)` };
            }
        } else {
            return { status: 'warning', message: 'معلومات الذاكرة غير متاحة' };
        }
    }

    async checkXSSProtection() {
        const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
        if (cspMeta) {
            return { status: 'success', message: 'الحماية من XSS مفعلة' };
        } else {
            return { status: 'warning', message: 'الحماية من XSS غير مكتملة' };
        }
    }

    async checkDataValidation() {
        // Check if form validation is implemented
        const forms = document.querySelectorAll('form');
        let validatedForms = 0;

        forms.forEach(form => {
            const inputs = form.querySelectorAll('input[required], input[pattern]');
            if (inputs.length > 0) {
                validatedForms++;
            }
        });

        if (validatedForms === forms.length && forms.length > 0) {
            return { status: 'success', message: 'جميع النماذج تحتوي على تحقق' };
        } else {
            return { status: 'warning', message: 'بعض النماذج تحتاج تحقق إضافي' };
        }
    }

    async checkSessionSecurity() {
        const hasSecureSession = localStorage.getItem('admin_session_v2') || sessionStorage.getItem('admin_session_v2');
        if (hasSecureSession) {
            return { status: 'success', message: 'نظام الجلسات آمن' };
        } else {
            return { status: 'warning', message: 'لا توجد جلسة نشطة' };
        }
    }

    async checkHTTPSUsage() {
        if (location.protocol === 'https:') {
            return { status: 'success', message: 'الموقع يستخدم HTTPS' };
        } else {
            return { status: 'warning', message: 'الموقع لا يستخدم HTTPS' };
        }
    }

    async checkSearchFeature() {
        const searchInput = document.querySelector('input[type="search"], #searchInput');
        if (searchInput) {
            return { status: 'success', message: 'ميزة البحث متوفرة' };
        } else {
            return { status: 'warning', message: 'ميزة البحث غير موجودة' };
        }
    }

    async checkCategoryFilter() {
        const categoryButtons = document.querySelectorAll('.category-tab, .category-btn');
        if (categoryButtons.length > 0) {
            return { status: 'success', message: `${categoryButtons.length} فئة متوفرة للفلترة` };
        } else {
            return { status: 'warning', message: 'فلترة الفئات غير متوفرة' };
        }
    }

    async checkResponsiveDesign() {
        const viewport = document.querySelector('meta[name="viewport"]');
        if (viewport && viewport.content.includes('width=device-width')) {
            return { status: 'success', message: 'التصميم المتجاوب مُعد بشكل صحيح' };
        } else {
            return { status: 'warning', message: 'إعدادات التصميم المتجاوب قد تحتاج تحسين' };
        }
    }

    // Utility Methods
    updateProgress(percentage, text) {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        if (progressFill) {
            progressFill.style.width = percentage + '%';
        }
        
        if (progressText) {
            progressText.textContent = text;
        }
    }

    updateSummary() {
        document.getElementById('passedCount').textContent = this.results.passed;
        document.getElementById('failedCount').textContent = this.results.failed;
        document.getElementById('warningCount').textContent = this.results.warnings;
        document.getElementById('totalCount').textContent = this.results.total;
    }

    updateTestDisplay(test) {
        const container = document.getElementById(`${test.category}Tests`);
        if (!container) return;
        
        const existingItem = container.querySelector(`[data-test="${test.name}"]`);
        if (existingItem) {
            existingItem.remove();
        }
        
        const testItem = document.createElement('div');
        testItem.className = 'check-item';
        testItem.setAttribute('data-test', test.name);
        
        const iconClass = test.status === 'success' ? 'fas fa-check-circle success' :
                         test.status === 'warning' ? 'fas fa-exclamation-triangle warning' :
                         test.status === 'error' ? 'fas fa-times-circle error' :
                         'fas fa-clock pending';
        
        testItem.innerHTML = `
            <div class="check-icon ${test.status}">
                <i class="${iconClass}"></i>
            </div>
            <div class="check-details">
                <div class="check-title">${test.name}</div>
                <div class="check-description">${test.result || test.description}</div>
            </div>
        `;
        
        container.appendChild(testItem);
        
        // Update section status
        const section = document.getElementById(`${test.category}Section`);
        const sectionTests = this.tests.filter(t => t.category === test.category);
        const completedTests = sectionTests.filter(t => t.status !== 'pending');
        
        if (completedTests.length === sectionTests.length) {
            const hasErrors = sectionTests.some(t => t.status === 'error');
            const hasWarnings = sectionTests.some(t => t.status === 'warning');
            
            if (hasErrors) {
                section.className = 'check-section error';
            } else if (hasWarnings) {
                section.className = 'check-section warning';
            } else {
                section.className = 'check-section success';
            }
        }
    }

    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: this.results,
            tests: this.tests,
            systemInfo: {
                userAgent: navigator.userAgent,
                url: window.location.href,
                viewport: `${window.innerWidth}x${window.innerHeight}`
            }
        };
        
        console.log('📊 System Check Report:', report);
        
        // Save report to localStorage
        localStorage.setItem('system_check_report', JSON.stringify(report));
        
        this.showMessage('تم إنشاء التقرير وحفظه', 'success');
    }

    exportReport() {
        const report = localStorage.getItem('system_check_report');
        if (report) {
            const blob = new Blob([report], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system-check-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        } else {
            this.showMessage('لا يوجد تقرير للتصدير', 'warning');
        }
    }

    async fixIssues() {
        this.showMessage('بدء الإصلاح التلقائي...', 'info');

        let fixedCount = 0;
        const failedTests = this.tests.filter(test => test.status === 'error' || test.status === 'warning');

        for (const test of failedTests) {
            try {
                const fixed = await this.attemptFix(test);
                if (fixed) {
                    fixedCount++;
                    // Re-run the test
                    const result = await test.testFunction();
                    test.status = result.status;
                    test.result = result.message;
                    this.updateTestDisplay(test);
                }
            } catch (error) {
                console.error(`Failed to fix ${test.name}:`, error);
            }
        }

        this.showMessage(`تم إصلاح ${fixedCount} مشكلة`, fixedCount > 0 ? 'success' : 'warning');
        this.updateSummary();
    }

    async attemptFix(test) {
        switch (test.name) {
            case 'CSS Loading':
                return this.fixCSSLoading();
            case 'JavaScript Loading':
                return this.fixJSLoading();
            case 'Menu Display':
                return this.fixMenuDisplay();
            case 'Cart Functionality':
                return this.fixCartFunctionality();
            case 'Search Feature':
                return this.fixSearchFeature();
            case 'Image Optimization':
                return this.fixImageOptimization();
            case 'XSS Protection':
                return this.fixXSSProtection();
            default:
                return false;
        }
    }

    fixCSSLoading() {
        // Check for missing critical CSS
        const criticalCSS = [
            'css/style.css',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
        ];

        let fixed = false;
        criticalCSS.forEach(href => {
            const existing = document.querySelector(`link[href="${href}"]`);
            if (!existing) {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = href;
                document.head.appendChild(link);
                fixed = true;
            }
        });

        return fixed;
    }

    fixJSLoading() {
        // Attempt to reload critical scripts
        const criticalScripts = [
            'js/menu-data-manager.js',
            'js/cart.js',
            'js/whatsapp-enhanced.js'
        ];

        let fixed = false;
        criticalScripts.forEach(src => {
            if (!window[src.split('/').pop().split('.')[0]]) {
                const script = document.createElement('script');
                script.src = src;
                document.head.appendChild(script);
                fixed = true;
            }
        });

        return fixed;
    }

    fixMenuDisplay() {
        // Try to initialize menu if not displayed
        if (window.menuDataManager && !window.menuDataManager.isDataLoaded()) {
            window.menuDataManager.loadFallbackData();
            return true;
        }
        return false;
    }

    fixCartFunctionality() {
        // Initialize cart if not available
        if (typeof window.cart === 'undefined') {
            try {
                window.cart = new ShoppingCart();
                return true;
            } catch (error) {
                console.error('Failed to initialize cart:', error);
            }
        }
        return false;
    }

    fixSearchFeature() {
        // Add search input if missing
        const header = document.querySelector('header, .header');
        if (header && !document.querySelector('#searchInput')) {
            const searchContainer = document.createElement('div');
            searchContainer.className = 'search-container';
            searchContainer.innerHTML = `
                <input type="search" id="searchInput" placeholder="ابحث في القائمة..."
                       style="padding: 10px; border: 1px solid #ddd; border-radius: 5px; width: 200px;">
            `;
            header.appendChild(searchContainer);
            return true;
        }
        return false;
    }

    fixImageOptimization() {
        // Add lazy loading to images
        const images = document.querySelectorAll('img:not([loading])');
        images.forEach(img => {
            img.loading = 'lazy';
        });
        return images.length > 0;
    }

    fixXSSProtection() {
        // Add CSP meta tag if missing
        if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
            const cspMeta = document.createElement('meta');
            cspMeta.setAttribute('http-equiv', 'Content-Security-Policy');
            cspMeta.setAttribute('content', "default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data:;");
            document.head.appendChild(cspMeta);
            return true;
        }
        return false;
    }

    showMessage(message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type}`;
        messageDiv.style.cssText = `
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            text-align: center;
            background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : type === 'warning' ? '#fff3cd' : '#d1ecf1'};
            color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : type === 'warning' ? '#856404' : '#0c5460'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : type === 'warning' ? '#ffeaa7' : '#bee5eb'};
        `;
        messageDiv.textContent = message;
        
        document.getElementById('testResults').appendChild(messageDiv);
        
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 5000);
    }
}

// Initialize system checker
const systemChecker = new SystemChecker();

// Make available globally
window.systemChecker = systemChecker;

// Global functions for buttons
function runAllTests() {
    systemChecker.runAllTests();
}

function runQuickTest() {
    systemChecker.runQuickTest();
}

function exportReport() {
    systemChecker.exportReport();
}

function fixIssues() {
    systemChecker.fixIssues();
}

console.log('✅ System Checker loaded successfully');
