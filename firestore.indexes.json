{"indexes": [{"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category_id", "order": "ASCENDING"}, {"fieldPath": "is_available", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "is_featured", "order": "ASCENDING"}, {"fieldPath": "is_available", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "customer_phone", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "categories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "is_active", "order": "ASCENDING"}, {"fieldPath": "sort_order", "order": "ASCENDING"}]}], "fieldOverrides": []}