// Main Application Logic
class RestaurantApp {
    constructor() {
        this.currentCategory = 'all';
        this.currentSearchQuery = '';
        this.menuDataManager = null;
        this.init();
    }

    // Initialize the application
    async init() {
        console.log('🚀 Initializing Restaurant App...');

        // Wait for real-time menu manager
        await this.waitForMenuManager();

        this.bindEvents();
        this.renderMenu();
        this.renderCategories();
        this.setupSearch();

        // Listen for real-time data updates
        window.addEventListener('menuDataLoaded', () => {
            console.log('📥 Menu data loaded, updating display...');
            this.renderMenu();
            this.renderCategories();
        });

        window.addEventListener('menuDataRefreshed', () => {
            console.log('🔄 Menu data refreshed, updating display...');
            this.renderMenu();
            this.renderCategories();
        });

        console.log('✅ Restaurant App initialized successfully');
    }

    async waitForMenuManager() {
        return new Promise((resolve) => {
            const checkManager = () => {
                if (window.realTimeMenuManager && window.realTimeMenuManager.isDataLoaded()) {
                    this.menuDataManager = window.realTimeMenuManager;
                    console.log('✅ Real-time menu manager connected');
                    resolve();
                } else if (window.menuDataManager) {
                    this.menuDataManager = window.menuDataManager;
                    console.log('✅ Fallback menu manager connected');
                    resolve();
                } else {
                    setTimeout(checkManager, 100);
                }
            };
            checkManager();
        });
    }

    // Bind event listeners
    bindEvents() {
        // Category tab clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.category-tab')) {
                const categoryTab = e.target.closest('.category-tab');
                const category = categoryTab.dataset.category;
                this.setActiveCategory(category);
            }

            // Add to cart button clicks
            if (e.target.closest('.add-to-cart')) {
                const button = e.target.closest('.add-to-cart');
                const itemId = button.dataset.itemId;
                this.addToCart(itemId);
            }
        });

        // Search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
        }

        // Smooth scrolling for CTA button
        window.scrollToMenu = () => {
            const menuSection = document.getElementById('menuSection');
            if (menuSection) {
                menuSection.scrollIntoView({ behavior: 'smooth' });
            }
        };
    }

    // Render category tabs
    renderCategories() {
        const categoryTabsContainer = document.getElementById('categoryTabs');
        if (!categoryTabsContainer) return;

        let categoriesData;

        if (this.menuDataManager) {
            categoriesData = this.menuDataManager.getCategories();

            // Add "all" category
            const allCategories = [
                { id: 'all', name: 'الكل', icon: 'fas fa-th-large' },
                ...categoriesData.map(cat => ({
                    id: cat.id,
                    name: cat.name,
                    icon: 'fas fa-utensils'
                }))
            ];

            const categoryHTML = allCategories.map(category => `
                <button class="category-tab ${category.id === this.currentCategory ? 'active' : ''}" data-category="${category.id}">
                    <i class="${category.icon}"></i>
                    ${category.name}
                </button>
            `).join('');

            categoryTabsContainer.innerHTML = categoryHTML;
        } else {
            // Fallback to old categories
            const categoryHTML = Object.entries(categories).map(([key, category]) => `
                <button class="category-tab ${key === this.currentCategory ? 'active' : ''}" data-category="${key}">
                    <i class="${category.icon}"></i>
                    ${category.name}
                </button>
            `).join('');

            categoryTabsContainer.innerHTML = categoryHTML;
        }
    }

    // Set active category
    setActiveCategory(category) {
        this.currentCategory = category;
        
        // Update active tab
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        
        const activeTab = document.querySelector(`[data-category="${category}"]`);
        if (activeTab) {
            activeTab.classList.add('active');
        }

        // Clear search when changing category
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '';
            this.currentSearchQuery = '';
        }

        // Render menu for selected category
        this.renderMenu();
    }

    // Handle search
    handleSearch(query) {
        this.currentSearchQuery = query.trim();
        this.renderMenu();
    }

    // Get filtered menu items
    getFilteredItems() {
        let items;

        // If there's a search query, search all items
        if (this.currentSearchQuery) {
            if (this.menuDataManager) {
                items = this.menuDataManager.searchProducts(this.currentSearchQuery);
            } else {
                items = searchItems(this.currentSearchQuery);
            }
        } else {
            // Otherwise, get items by category
            if (this.menuDataManager) {
                items = this.currentCategory === 'all' ?
                    this.menuDataManager.getProducts() :
                    this.menuDataManager.getProducts(this.currentCategory);
            } else {
                items = getItemsByCategory(this.currentCategory);
            }
        }

        // Only return available items
        if (this.menuDataManager) {
            return items; // Already filtered by menuDataManager
        } else {
            return getAvailableItems(items);
        }
    }

    // Render menu items
    renderMenu() {
        const menuGrid = document.getElementById('menuGrid');
        if (!menuGrid) return;

        const items = this.getFilteredItems();

        if (items.length === 0) {
            menuGrid.innerHTML = this.renderEmptyState();
            return;
        }

        const menuHTML = items.map(item => this.renderMenuItem(item)).join('');
        menuGrid.innerHTML = menuHTML;

        // Add animation to menu items
        this.animateMenuItems();
    }

    // Render individual menu item with real images
    renderMenuItem(item) {
        const imageHtml = this.renderItemImage(item);
        const availabilityClass = item.is_available ? '' : 'unavailable';
        const featuredClass = item.is_featured ? 'featured' : '';

        return `
            <div class="menu-item ${availabilityClass} ${featuredClass}" data-id="${item.id}">
                ${imageHtml}
                <div class="menu-item-content">
                    <h4>${item.name}</h4>
                    <p>${item.description}</p>
                    <div class="menu-item-footer">
                        <span class="price">${item.price} جنيه</span>
                        <button class="add-to-cart" data-item-id="${item.id}" ${!item.is_available ? 'disabled' : ''}>
                            <i class="fas fa-plus"></i>
                            ${item.is_available ? 'إضافة' : 'غير متوفر'}
                        </button>
                    </div>
                </div>
                ${item.is_featured ? '<div class="featured-badge">مميز</div>' : ''}
            </div>
        `;
    }

    // Render item image (real image or fallback icon)
    renderItemImage(item) {
        if (item.image_url) {
            return `
                <div class="menu-item-image">
                    <img src="${item.image_url}" alt="${item.name}" loading="lazy"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="menu-item-icon-fallback" style="display: none;">
                        <i class="${item.icon || 'fas fa-utensils'}"></i>
                    </div>
                </div>
            `;
        } else {
            return `
                <div class="menu-item-image">
                    <div class="menu-item-icon-fallback">
                        <i class="${item.icon || 'fas fa-utensils'}"></i>
                    </div>
                </div>
            `;
        }
    }

    // Render empty state
    renderEmptyState() {
        const message = this.currentSearchQuery 
            ? `لم يتم العثور على نتائج للبحث "${this.currentSearchQuery}"`
            : 'لا توجد عناصر في هذا القسم حالياً';

        return `
            <div class="empty-state" style="grid-column: 1 / -1; text-align: center; padding: 60px 20px; color: #666;">
                <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 20px; color: #ccc;"></i>
                <h3 style="margin-bottom: 15px; color: #2c5aa0;">${message}</h3>
                <p style="margin-bottom: 25px;">جرب البحث عن شيء آخر أو تصفح الأقسام المختلفة</p>
                <button onclick="app.clearSearch()" style="background: #2c5aa0; color: white; border: none; padding: 12px 25px; border-radius: 25px; cursor: pointer;">
                    <i class="fas fa-arrow-right"></i>
                    تصفح جميع المنتجات
                </button>
            </div>
        `;
    }

    // Clear search
    clearSearch() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '';
        }
        this.currentSearchQuery = '';
        this.setActiveCategory('all');
    }

    // Add item to cart
    addToCart(itemId) {
        const item = getItemById(itemId);
        if (!item) {
            console.error('Item not found:', itemId);
            return;
        }

        // Add visual feedback
        const button = document.querySelector(`[data-item-id="${itemId}"]`);
        if (button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> تم الإضافة';
            button.style.background = '#28a745';
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.style.background = '';
            }, 1000);
        }

        // Add to cart
        cart.addItem(itemId);
    }

    // Animate menu items on load
    animateMenuItems() {
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                item.style.transition = 'all 0.5s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // Setup search functionality
    setupSearch() {
        const searchInput = document.getElementById('searchInput');
        if (!searchInput) return;

        // Add search suggestions (optional enhancement)
        searchInput.addEventListener('focus', () => {
            searchInput.style.transform = 'scale(1.02)';
        });

        searchInput.addEventListener('blur', () => {
            searchInput.style.transform = 'scale(1)';
        });

        // Add search icon animation
        const searchIcon = searchInput.parentElement.querySelector('i');
        if (searchIcon) {
            searchInput.addEventListener('input', () => {
                if (searchInput.value.trim()) {
                    searchIcon.style.color = '#2c5aa0';
                    searchIcon.style.transform = 'scale(1.1)';
                } else {
                    searchIcon.style.color = '';
                    searchIcon.style.transform = 'scale(1)';
                }
            });
        }
    }

    // Utility method to show loading state
    showLoading(container) {
        if (container) {
            container.innerHTML = `
                <div style="text-align: center; padding: 40px; grid-column: 1 / -1;">
                    <div class="loading"></div>
                    <p style="margin-top: 15px; color: #666;">جاري التحميل...</p>
                </div>
            `;
        }
    }

    // Utility method to format price
    formatPrice(price) {
        return `${price} جنيه`;
    }

    // Get popular items (most ordered - placeholder for future enhancement)
    getPopularItems() {
        // This could be enhanced to track actual order data
        const allItems = getAllMenuItems();
        return allItems.slice(0, 6); // Return first 6 items as "popular"
    }

    // Get featured items
    getFeaturedItems() {
        // Return some featured items (could be based on admin settings)
        return [
            getItemById('main1'), // Shawarma
            getItemById('bev3'),  // Orange juice
            getItemById('des1'),  // Knafeh
            getItemById('app1')   // Hummus
        ].filter(Boolean);
    }
}

// Utility functions for smooth scrolling and animations
function smoothScrollTo(element) {
    if (element) {
        element.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Add scroll-based animations
function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements that should animate on scroll
    document.querySelectorAll('.menu-item, .category-tab').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'all 0.6s ease';
        observer.observe(el);
    });
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize main app
    window.app = new RestaurantApp();

    // Setup scroll animations
    setupScrollAnimations();

    // Add some interactive enhancements
    setupInteractiveEnhancements();

    // Setup scroll to top button
    setupScrollToTop();

    // Setup loading states
    setupLoadingStates();

    // Setup keyboard shortcuts
    setupKeyboardShortcuts();
});

// Setup interactive enhancements
function setupInteractiveEnhancements() {
    // Add hover effects to buttons
    document.addEventListener('mouseover', (e) => {
        if (e.target.matches('.add-to-cart, .cta-btn, .checkout-btn')) {
            e.target.style.transform = 'translateY(-2px)';
        }
    });

    document.addEventListener('mouseout', (e) => {
        if (e.target.matches('.add-to-cart, .cta-btn, .checkout-btn')) {
            e.target.style.transform = '';
        }
    });

    // Add ripple effect to buttons (optional)
    document.addEventListener('click', (e) => {
        if (e.target.matches('.add-to-cart, .category-tab, .cta-btn')) {
            createRippleEffect(e);
        }
    });
}

// Create ripple effect
function createRippleEffect(e) {
    const button = e.target;
    const ripple = document.createElement('span');
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;
    
    ripple.style.cssText = `
        position: absolute;
        border-radius: 50%;
        background: rgba(255,255,255,0.6);
        transform: scale(0);
        animation: ripple 0.6s linear;
        left: ${x}px;
        top: ${y}px;
        width: ${size}px;
        height: ${size}px;
        pointer-events: none;
    `;
    
    button.style.position = 'relative';
    button.style.overflow = 'hidden';
    button.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Add CSS for ripple animation
const rippleCSS = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;

const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);

// Setup scroll to top functionality
function setupScrollToTop() {
    const scrollToTopBtn = document.getElementById('scrollToTop');
    if (!scrollToTopBtn) return;

    // Show/hide button based on scroll position
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            scrollToTopBtn.classList.add('show');
        } else {
            scrollToTopBtn.classList.remove('show');
        }
    });

    // Scroll to top when clicked
    scrollToTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Setup loading states for better UX
function setupLoadingStates() {
    // Add loading state to menu items when adding to cart
    document.addEventListener('click', (e) => {
        if (e.target.closest('.add-to-cart')) {
            const menuItem = e.target.closest('.menu-item');
            if (menuItem) {
                menuItem.classList.add('loading');
                setTimeout(() => {
                    menuItem.classList.remove('loading');
                }, 1000);
            }
        }
    });
}

// Setup keyboard shortcuts
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // Escape to close modals and sidebars
        if (e.key === 'Escape') {
            // Close cart sidebar
            const cartSidebar = document.getElementById('cartSidebar');
            if (cartSidebar && cartSidebar.classList.contains('open')) {
                cart.closeSidebar();
            }

            // Close order modal
            const orderModal = document.getElementById('orderModal');
            if (orderModal && orderModal.classList.contains('show')) {
                cart.closeOrderModal();
            }

            // Clear search
            const searchInput = document.getElementById('searchInput');
            if (searchInput && searchInput.value) {
                searchInput.value = '';
                window.app.handleSearch('');
            }
        }

        // Arrow keys for category navigation
        if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
            const categoryTabs = document.querySelectorAll('.category-tab');
            const activeTab = document.querySelector('.category-tab.active');

            if (activeTab && categoryTabs.length > 1) {
                const currentIndex = Array.from(categoryTabs).indexOf(activeTab);
                let newIndex;

                if (e.key === 'ArrowLeft') {
                    newIndex = currentIndex > 0 ? currentIndex - 1 : categoryTabs.length - 1;
                } else {
                    newIndex = currentIndex < categoryTabs.length - 1 ? currentIndex + 1 : 0;
                }

                const newTab = categoryTabs[newIndex];
                if (newTab) {
                    newTab.click();
                }
            }
        }
    });
}

// Enhanced notification system
function showNotification(message, type = 'success', duration = 3000) {
    const container = document.getElementById('notificationContainer') || document.body;

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;

    container.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    // Hide and remove notification
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, duration);
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// Performance optimization: Lazy loading for menu items
function setupLazyLoading() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const menuItem = entry.target;
                menuItem.style.opacity = '1';
                menuItem.style.transform = 'translateY(0)';
                observer.unobserve(menuItem);
            }
        });
    }, observerOptions);

    // Observe all menu items
    document.querySelectorAll('.menu-item').forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'all 0.6s ease';
        observer.observe(item);
    });
}

// Auto-save cart data
function setupAutoSave() {
    // Save cart data every 30 seconds
    setInterval(() => {
        if (window.cart && !window.cart.isEmpty()) {
            window.cart.saveToStorage();
        }
    }, 30000);
}

// Initialize additional features
document.addEventListener('DOMContentLoaded', () => {
    setupAutoSave();

    // Setup lazy loading after initial render
    setTimeout(setupLazyLoading, 1000);
});

// Export functions for global use
window.showNotification = showNotification;
