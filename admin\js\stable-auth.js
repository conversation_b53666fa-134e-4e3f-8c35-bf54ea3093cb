// Stable Authentication System - No Refresh Issues
// نظام مصادقة مستقر - بدون مشاكل ريفريش

class StableAuth {
    constructor() {
        this.sessionKey = 'admin_session_v2';
        this.users = [
            { username: 'admin', password: 'admin123', role: 'admin' },
            { username: 'محمد', password: 'alashrafi2024', role: 'owner' }
        ];
        
        // Prevent multiple initializations
        if (window.stableAuthInstance) {
            return window.stableAuthInstance;
        }
        
        window.stableAuthInstance = this;
        this.initialized = false;
        this.redirecting = false;
        
        console.log('StableAuth: Starting initialization...');
        this.safeInit();
    }

    safeInit() {
        // Wait for page to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => this.performAuth(), 1000);
            });
        } else {
            setTimeout(() => this.performAuth(), 1000);
        }
    }

    performAuth() {
        if (this.initialized || this.redirecting) {
            console.log('StableAuth: Already initialized or redirecting');
            return;
        }

        this.initialized = true;
        const currentPath = window.location.pathname;
        
        console.log('StableAuth: Performing auth check for:', currentPath);

        try {
            if (currentPath.includes('login.html')) {
                this.handleLoginPage();
            } else {
                this.handleProtectedPage();
            }
        } catch (error) {
            console.error('StableAuth: Error in performAuth:', error);
        }
    }

    handleLoginPage() {
        console.log('StableAuth: On login page');
        
        if (this.isValidSession()) {
            console.log('StableAuth: Valid session found, redirecting to dashboard');
            this.safeRedirect('dashboard.html');
        } else {
            console.log('StableAuth: No valid session, setting up login form');
            this.setupLoginForm();
        }
    }

    handleProtectedPage() {
        console.log('StableAuth: On protected page');
        
        if (!this.isValidSession()) {
            console.log('StableAuth: No valid session, redirecting to login');
            this.safeRedirect('login.html');
        } else {
            console.log('StableAuth: Valid session, updating UI');
            this.updateUserInterface();
        }
    }

    isValidSession() {
        try {
            const sessionData = localStorage.getItem(this.sessionKey);
            if (!sessionData) {
                console.log('StableAuth: No session data found');
                return false;
            }

            const session = JSON.parse(sessionData);
            if (!session.username || !session.loginTime) {
                console.log('StableAuth: Invalid session structure');
                return false;
            }

            // Check expiration (24 hours)
            const loginTime = new Date(session.loginTime);
            const now = new Date();
            const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

            if (hoursDiff > 24) {
                console.log('StableAuth: Session expired');
                this.clearSession();
                return false;
            }

            console.log('StableAuth: Valid session for user:', session.username);
            return true;

        } catch (error) {
            console.error('StableAuth: Error checking session:', error);
            return false;
        }
    }

    safeRedirect(page) {
        if (this.redirecting) {
            console.log('StableAuth: Redirect already in progress');
            return;
        }

        this.redirecting = true;
        console.log('StableAuth: Redirecting to:', page);
        
        // Use location.replace to avoid back button issues
        window.location.replace(page);
    }

    setupLoginForm() {
        const form = document.getElementById('loginForm');
        if (!form) {
            console.log('StableAuth: Login form not found');
            return;
        }

        console.log('StableAuth: Setting up login form');

        // Remove existing listeners
        const newForm = form.cloneNode(true);
        form.parentNode.replaceChild(newForm, form);

        newForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin(e);
        });
    }

    handleLogin(e) {
        const formData = new FormData(e.target);
        const username = formData.get('username') || document.getElementById('username')?.value;
        const password = formData.get('password') || document.getElementById('password')?.value;

        console.log('StableAuth: Login attempt for:', username);

        if (!username || !password) {
            this.showMessage('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
            return;
        }

        const user = this.users.find(u => u.username === username && u.password === password);
        
        if (!user) {
            this.showMessage('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            return;
        }

        // Create session
        const sessionData = {
            username: user.username,
            role: user.role,
            loginTime: new Date().toISOString()
        };

        try {
            localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
            this.showMessage('تم تسجيل الدخول بنجاح', 'success');
            
            setTimeout(() => {
                this.safeRedirect('dashboard.html');
            }, 1500);

        } catch (error) {
            console.error('StableAuth: Error saving session:', error);
            this.showMessage('خطأ في تسجيل الدخول', 'error');
        }
    }

    updateUserInterface() {
        try {
            const sessionData = localStorage.getItem(this.sessionKey);
            if (!sessionData) return;

            const session = JSON.parse(sessionData);
            
            // Update user name
            const userNameElement = document.getElementById('userName');
            if (userNameElement) {
                userNameElement.textContent = session.username;
            }

            // Update user role
            const userRoleElement = document.getElementById('userRole');
            if (userRoleElement) {
                userRoleElement.textContent = session.role === 'admin' ? 'مدير' : 'مالك';
            }

            // Setup logout button
            this.setupLogoutButton();

            console.log('StableAuth: UI updated successfully');

        } catch (error) {
            console.error('StableAuth: Error updating UI:', error);
        }
    }

    setupLogoutButton() {
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            // Remove existing listeners
            const newLogoutBtn = logoutBtn.cloneNode(true);
            logoutBtn.parentNode.replaceChild(newLogoutBtn, logoutBtn);

            newLogoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        }
    }

    logout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            console.log('StableAuth: Logging out');
            this.clearSession();
            this.safeRedirect('login.html');
        }
    }

    clearSession() {
        localStorage.removeItem(this.sessionKey);
        console.log('StableAuth: Session cleared');
    }

    showMessage(message, type) {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.auth-message');
        existingMessages.forEach(msg => msg.remove());

        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = 'auth-message';
        messageDiv.style.cssText = `
            padding: 12px;
            margin: 15px 0;
            border-radius: 6px;
            text-align: center;
            font-weight: bold;
            background: ${type === 'success' ? '#d4edda' : '#f8d7da'};
            color: ${type === 'success' ? '#155724' : '#721c24'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : '#f5c6cb'};
        `;
        messageDiv.textContent = message;

        // Insert message
        const container = document.querySelector('.login-container') || 
                         document.querySelector('.container') || 
                         document.body;
        
        if (container.firstChild) {
            container.insertBefore(messageDiv, container.firstChild);
        } else {
            container.appendChild(messageDiv);
        }

        // Auto remove
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 4000);
    }

    getCurrentUser() {
        try {
            const sessionData = localStorage.getItem(this.sessionKey);
            if (!sessionData) return null;

            const session = JSON.parse(sessionData);
            return {
                username: session.username,
                role: session.role
            };
        } catch (error) {
            console.error('StableAuth: Error getting current user:', error);
            return null;
        }
    }
}

// Prevent multiple instances
if (!window.stableAuth) {
    console.log('Creating new StableAuth instance');
    window.stableAuth = new StableAuth();
} else {
    console.log('Using existing StableAuth instance');
}

// Prevent page refresh loops
let refreshCount = parseInt(sessionStorage.getItem('refreshCount') || '0');
if (refreshCount > 3) {
    console.warn('Too many refreshes detected, clearing session');
    localStorage.removeItem('admin_session_v2');
    sessionStorage.removeItem('refreshCount');
} else {
    sessionStorage.setItem('refreshCount', (refreshCount + 1).toString());
    setTimeout(() => {
        sessionStorage.removeItem('refreshCount');
    }, 5000);
}

console.log('StableAuth loaded successfully');
