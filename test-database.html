<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قواعد البيانات - مطعم محمد الاشرافي</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .test-container {
            max-width: 1000px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        
        .test-section.success {
            border-left-color: #27ae60;
            background: #d5f4e6;
        }
        
        .test-section.error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        
        .test-results {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn.success {
            background: #27ae60;
        }
        
        .btn.danger {
            background: #e74c3c;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-success {
            background: #27ae60;
        }
        
        .status-error {
            background: #e74c3c;
        }
        
        .status-pending {
            background: #f39c12;
        }
        
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .data-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
        
        .data-card h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-database"></i> اختبار قواعد البيانات</h1>
            <p>اختبار الاتصال والبيانات لـ Firebase و Supabase</p>
        </div>

        <!-- Firebase Test -->
        <div class="test-section" id="firebaseSection">
            <h2>
                <i class="fab fa-google"></i> 
                اختبار Firebase
                <span class="status-indicator status-pending" id="firebaseStatus"></span>
            </h2>
            <button class="btn" onclick="testFirebase()">اختبار Firebase</button>
            <button class="btn" onclick="testFirestore()">اختبار Firestore</button>
            <button class="btn" onclick="addTestData()">إضافة بيانات تجريبية</button>
            <div class="test-results" id="firebaseResults">جاري الانتظار...</div>
        </div>

        <!-- Supabase Test -->
        <div class="test-section" id="supabaseSection">
            <h2>
                <i class="fas fa-database"></i> 
                اختبار Supabase
                <span class="status-indicator status-pending" id="supabaseStatus"></span>
            </h2>
            <button class="btn" onclick="testSupabase()">اختبار Supabase</button>
            <button class="btn" onclick="loadCategories()">تحميل الأقسام</button>
            <button class="btn" onclick="loadProducts()">تحميل المنتجات</button>
            <div class="test-results" id="supabaseResults">جاري الانتظار...</div>
        </div>

        <!-- Data Display -->
        <div class="test-section">
            <h2><i class="fas fa-table"></i> البيانات المحملة</h2>
            <div class="data-grid" id="dataGrid">
                <!-- Data will be displayed here -->
            </div>
        </div>

        <!-- System Status -->
        <div class="test-section">
            <h2><i class="fas fa-info-circle"></i> حالة النظام</h2>
            <div id="systemStatus">
                <p><strong>Firebase:</strong> <span id="firebaseStatusText">غير محدد</span></p>
                <p><strong>Supabase:</strong> <span id="supabaseStatusText">غير محدد</span></p>
                <p><strong>Menu Data Manager:</strong> <span id="menuManagerStatus">غير محدد</span></p>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-storage-compat.js"></script>
    
    <!-- Supabase SDK -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Configuration -->
    <script src="js/firebase-config.js"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/menu-data-manager.js"></script>
    
    <script>
        let firebaseManager, supabaseManager, menuDataManager;
        
        // Initialize managers
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                firebaseManager = window.firebaseManager;
                supabaseManager = window.supabaseManager;
                menuDataManager = window.menuDataManager;
                
                updateSystemStatus();
                
                // Auto-test connections
                testFirebase();
                testSupabase();
            }, 2000);
        });
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            element.innerHTML += `[${timestamp}] ${icon} ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }
        
        function updateStatus(sectionId, statusId, success) {
            const section = document.getElementById(sectionId);
            const status = document.getElementById(statusId);
            
            if (success) {
                section.classList.add('success');
                status.className = 'status-indicator status-success';
            } else {
                section.classList.add('error');
                status.className = 'status-indicator status-error';
            }
        }
        
        async function testFirebase() {
            log('firebaseResults', 'بدء اختبار Firebase...', 'info');
            
            try {
                if (!firebaseManager) {
                    throw new Error('Firebase Manager غير متوفر');
                }
                
                if (!firebaseManager.isInitialized) {
                    throw new Error('Firebase غير مهيأ');
                }
                
                log('firebaseResults', 'Firebase متصل بنجاح!', 'success');
                updateStatus('firebaseSection', 'firebaseStatus', true);
                document.getElementById('firebaseStatusText').textContent = 'متصل ✅';
                
            } catch (error) {
                log('firebaseResults', 'خطأ في Firebase: ' + error.message, 'error');
                updateStatus('firebaseSection', 'firebaseStatus', false);
                document.getElementById('firebaseStatusText').textContent = 'خطأ ❌';
            }
        }
        
        async function testFirestore() {
            log('firebaseResults', 'اختبار Firestore...', 'info');
            
            try {
                if (!firebaseManager || !firebaseManager.firestore) {
                    throw new Error('Firestore غير متوفر');
                }
                
                // Test write
                await firebaseManager.firestore.collection('test').doc('test').set({
                    test: true,
                    timestamp: firebase.firestore.FieldValue.serverTimestamp()
                });
                
                // Test read
                const doc = await firebaseManager.firestore.collection('test').doc('test').get();
                if (doc.exists) {
                    log('firebaseResults', 'Firestore يعمل بشكل صحيح!', 'success');
                    
                    // Clean up
                    await firebaseManager.firestore.collection('test').doc('test').delete();
                } else {
                    throw new Error('فشل في قراءة البيانات');
                }
                
            } catch (error) {
                log('firebaseResults', 'خطأ في Firestore: ' + error.message, 'error');
            }
        }
        
        async function testSupabase() {
            log('supabaseResults', 'بدء اختبار Supabase...', 'info');
            
            try {
                if (!supabaseManager) {
                    throw new Error('Supabase Manager غير متوفر');
                }
                
                if (!supabaseManager.isInitialized) {
                    throw new Error('Supabase غير مهيأ');
                }
                
                // Test connection
                const { data, error } = await supabaseManager.client.from('categories').select('count');
                
                if (error) {
                    throw error;
                }
                
                log('supabaseResults', 'Supabase متصل بنجاح!', 'success');
                updateStatus('supabaseSection', 'supabaseStatus', true);
                document.getElementById('supabaseStatusText').textContent = 'متصل ✅';
                
            } catch (error) {
                log('supabaseResults', 'خطأ في Supabase: ' + error.message, 'error');
                updateStatus('supabaseSection', 'supabaseStatus', false);
                document.getElementById('supabaseStatusText').textContent = 'خطأ ❌';
            }
        }
        
        async function loadCategories() {
            log('supabaseResults', 'تحميل الأقسام...', 'info');
            
            try {
                const result = await supabaseManager.getCategories();
                
                if (result.success) {
                    log('supabaseResults', `تم تحميل ${result.data.length} قسم`, 'success');
                    displayData('categories', result.data);
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                log('supabaseResults', 'خطأ في تحميل الأقسام: ' + error.message, 'error');
            }
        }
        
        async function loadProducts() {
            log('supabaseResults', 'تحميل المنتجات...', 'info');
            
            try {
                const result = await supabaseManager.getProducts();
                
                if (result.success) {
                    log('supabaseResults', `تم تحميل ${result.data.length} منتج`, 'success');
                    displayData('products', result.data);
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                log('supabaseResults', 'خطأ في تحميل المنتجات: ' + error.message, 'error');
            }
        }
        
        function displayData(type, data) {
            const grid = document.getElementById('dataGrid');
            
            const card = document.createElement('div');
            card.className = 'data-card';
            card.innerHTML = `
                <h4>${type === 'categories' ? 'الأقسام' : 'المنتجات'} (${data.length})</h4>
                <div style="max-height: 200px; overflow-y: auto;">
                    ${data.map(item => `
                        <div style="padding: 5px; border-bottom: 1px solid #eee;">
                            <strong>${item.name}</strong>
                            ${item.price ? ` - ${item.price} جنيه` : ''}
                        </div>
                    `).join('')}
                </div>
            `;
            
            grid.appendChild(card);
        }
        
        async function addTestData() {
            log('firebaseResults', 'إضافة بيانات تجريبية...', 'info');
            
            try {
                const db = firebaseManager.firestore;
                
                // Add test category
                await db.collection('categories').add({
                    name: 'قسم تجريبي',
                    description: 'قسم للاختبار',
                    created_at: firebase.firestore.FieldValue.serverTimestamp()
                });
                
                log('firebaseResults', 'تم إضافة بيانات تجريبية بنجاح!', 'success');
                
            } catch (error) {
                log('firebaseResults', 'خطأ في إضافة البيانات: ' + error.message, 'error');
            }
        }
        
        function updateSystemStatus() {
            if (menuDataManager) {
                document.getElementById('menuManagerStatus').textContent = 
                    menuDataManager.isDataLoaded() ? 'جاهز ✅' : 'جاري التحميل ⏳';
            }
        }
        
        // Update status every 5 seconds
        setInterval(updateSystemStatus, 5000);
    </script>
</body>
</html>
