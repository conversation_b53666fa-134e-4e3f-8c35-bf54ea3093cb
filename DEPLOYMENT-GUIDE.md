# دليل النشر والاستخدام - نظام مطعم محمد الاشرافي

## 🚀 النشر السريع

### المتطلبات الأساسية
```bash
# تثبيت Node.js (الإصدار 16 أو أحدث)
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول إلى Firebase
firebase login
```

### خطوات النشر

#### 1. إعداد المشروع
```bash
# استنساخ المشروع
git clone [repository-url]
cd restaurant-ordering-system

# تثبيت التبعيات
npm install

# إنشاء ملف البيئة
cp .env.example .env
# قم بتحديث المتغيرات في ملف .env
```

#### 2. تكوين Firebase
```bash
# تهيئة Firebase
firebase init

# اختر الخدمات التالية:
# ✅ Hosting
# ✅ Firestore Database
# ✅ Storage
# ✅ Functions (اختياري)

# استخدم الإعدادات التالية:
# Public directory: . (النقطة)
# Single-page app: No
# Automatic builds: No
```

#### 3. إعداد قواعد البيانات
```bash
# نشر قواعد Firestore
firebase deploy --only firestore:rules

# نشر قواعد Storage
firebase deploy --only storage:rules
```

#### 4. تشغيل الاختبارات
```bash
# اختبار النظام
npm run test
# أو
node scripts/test-system.js
```

#### 5. النشر النهائي
```bash
# نشر كامل
npm run deploy
# أو
firebase deploy
```

## 🔧 التكوين المفصل

### إعداد Firebase

#### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. انقر على "إضافة مشروع"
3. اسم المشروع: `alashrafi-restaurant`
4. فعّل Google Analytics (اختياري)

#### 2. تكوين Authentication
```javascript
// في Firebase Console > Authentication > Sign-in method
// فعّل المقدمين التاليين:
- Email/Password ✅
- Phone ✅ (للواتساب)
```

#### 3. تكوين Firestore Database
```javascript
// في Firebase Console > Firestore Database
// اختر "Start in test mode" ثم غيّر إلى production mode
// انسخ قواعد الأمان من ملف firestore.rules
```

#### 4. تكوين Storage
```javascript
// في Firebase Console > Storage
// انسخ قواعد الأمان من ملف storage.rules
```

### إعداد Supabase

#### 1. إنشاء مشروع Supabase
1. اذهب إلى [Supabase](https://supabase.com)
2. انقر على "New Project"
3. اسم المشروع: `alashrafi-restaurant`
4. كلمة مرور قاعدة البيانات: (احفظها بأمان)

#### 2. تشغيل SQL Schema
```sql
-- في Supabase SQL Editor، شغّل محتوى ملف database-schema.sql
-- هذا سينشئ جميع الجداول المطلوبة
```

#### 3. تكوين Row Level Security
```sql
-- فعّل RLS على جميع الجداول
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
-- ... إلخ
```

### تحديث متغيرات البيئة

#### ملف .env
```env
# Firebase
FIREBASE_API_KEY=your_actual_api_key
FIREBASE_PROJECT_ID=alashrafi-restaurant
FIREBASE_STORAGE_BUCKET=alashrafi-restaurant.appspot.com

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_actual_anon_key

# Restaurant Info
RESTAURANT_NAME=مطعم محمد الاشرافي
WHATSAPP_NUMBER=201014840269
```

## 📱 دليل الاستخدام

### للمدير

#### تسجيل الدخول
1. اذهب إلى `/admin/login.html`
2. استخدم بيانات الدخول:
   - البريد: `<EMAIL>`
   - كلمة المرور: `admin123`

#### إدارة المنتجات
1. **إضافة منتج جديد:**
   - اذهب إلى "إدارة القائمة"
   - انقر "إضافة منتج جديد"
   - املأ جميع البيانات المطلوبة
   - ارفع صور المنتج (متعددة)
   - احفظ المنتج

2. **تعديل منتج:**
   - انقر "تعديل" على المنتج المطلوب
   - عدّل البيانات
   - احفظ التغييرات

3. **إدارة الصور:**
   - انقر "الصور" على المنتج
   - ارفع صور جديدة
   - احذف الصور غير المرغوبة
   - حدد الصورة الرئيسية

#### إدارة الطلبات
1. **مراجعة الطلبات:**
   - اذهب إلى "الطلبات"
   - راجع الطلبات الجديدة
   - غيّر حالة الطلب

2. **التواصل مع العملاء:**
   - انقر "واتساب" لإرسال تحديث
   - استخدم القوالب الجاهزة

### للعملاء

#### تصفح القائمة
1. ادخل إلى الموقع الرئيسي
2. تصفح الأقسام المختلفة
3. استخدم البحث للعثور على منتجات محددة

#### إجراء طلب
1. **إضافة للسلة:**
   - انقر "إضافة للسلة" على المنتج المرغوب
   - اختر الكمية
   - تابع التسوق أو اذهب للسلة

2. **إتمام الطلب:**
   - انقر على أيقونة السلة
   - راجع الطلب
   - انقر "إتمام الطلب"
   - املأ بياناتك (الاسم، الهاتف، العنوان)
   - انقر "إرسال الطلب"

3. **إرسال عبر الواتساب:**
   - سيتم فتح الواتساب تلقائياً
   - ستجد رسالة منسقة مع تفاصيل الطلب
   - أرسل الرسالة للمطعم

## 🔧 الصيانة والتحديث

### النسخ الاحتياطية
```bash
# نسخ احتياطي لقاعدة البيانات
npm run backup

# استعادة النسخة الاحتياطية
npm run restore
```

### التحديثات
```bash
# تحديث التبعيات
npm update

# تحديث Firebase
firebase deploy

# تحديث قواعد الأمان
firebase deploy --only firestore:rules,storage:rules
```

### مراقبة الأداء
```bash
# تقرير الأداء
npm run analytics

# فحص الأمان
npm run security-audit
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة

#### 1. فشل النشر
```bash
# تحقق من تسجيل الدخول
firebase login --reauth

# تحقق من المشروع
firebase use --add
```

#### 2. مشاكل قاعدة البيانات
```bash
# تحقق من الاتصال
npm run test-db

# إعادة تشغيل قواعد الأمان
firebase deploy --only firestore:rules
```

#### 3. مشاكل الصور
```bash
# تحقق من قواعد Storage
firebase deploy --only storage:rules

# تحقق من أحجام الملفات (الحد الأقصى 5MB)
```

## 📞 الدعم الفني

### معلومات الاتصال
- **المطور:** محمد الاشرافي
- **الهاتف:** +201014840269
- **البريد:** <EMAIL>

### الموارد المفيدة
- [Firebase Documentation](https://firebase.google.com/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [مجتمع المطورين العرب](https://github.com/arab-developers)

## 🔗 الروابط المهمة

### بعد النشر
- **الموقع الرئيسي:** `https://alashrafi-restaurant.web.app`
- **لوحة الإدارة:** `https://alashrafi-restaurant.web.app/admin`
- **Firebase Console:** `https://console.firebase.google.com/project/alashrafi-restaurant`
- **Supabase Dashboard:** `https://app.supabase.com/project/your-project-id`

### أدوات المراقبة
- **Google Analytics:** (إذا تم تفعيله)
- **Firebase Analytics:** في Firebase Console
- **Performance Monitoring:** في Firebase Console

---

## ✅ قائمة التحقق النهائية

قبل الإطلاق، تأكد من:

- [ ] تم تشغيل جميع الاختبارات بنجاح
- [ ] تم تكوين Firebase بالكامل
- [ ] تم تكوين Supabase بالكامل
- [ ] تم اختبار إضافة المنتجات
- [ ] تم اختبار رفع الصور
- [ ] تم اختبار نظام الطلبات
- [ ] تم اختبار الواتساب
- [ ] تم اختبار لوحة الإدارة
- [ ] تم اختبار النظام على الهاتف
- [ ] تم تحديث معلومات المطعم
- [ ] تم تحديث رقم الواتساب

🎉 **مبروك! نظام مطعم محمد الاشرافي جاهز للعمل!**
