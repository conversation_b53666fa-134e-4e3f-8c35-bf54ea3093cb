// Firebase Configuration for Mohamed <PERSON>
// تكوين Firebase لمطعم محمد الاشرافي

// Firebase configuration object
const firebaseConfig = {
    apiKey: "YOUR_API_KEY", // سيتم تحديثه عند إنشاء المشروع
    authDomain: "alashrafi-restaurant.firebaseapp.com",
    projectId: "alashrafi-restaurant",
    storageBucket: "alashrafi-restaurant.appspot.com",
    messagingSenderId: "YOUR_SENDER_ID",
    appId: "YOUR_APP_ID",
    measurementId: "YOUR_MEASUREMENT_ID"
};

// Initialize Firebase
import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';
import { getAnalytics } from 'firebase/analytics';

// Initialize Firebase app
const app = initializeA<PERSON>(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
export const analytics = getAnalytics(app);

// Development environment setup
if (location.hostname === 'localhost') {
    // Connect to emulators in development
    try {
        connectAuthEmulator(auth, 'http://localhost:9099');
        connectFirestoreEmulator(db, 'localhost', 8080);
        connectStorageEmulator(storage, 'localhost', 9199);
    } catch (error) {
        console.log('Emulators already connected or not available');
    }
}

// Firebase Auth configuration
export const authConfig = {
    signInOptions: [
        'password', // Email/password
        'phone',    // Phone authentication
        'google',   // Google sign-in
    ],
    callbacks: {
        signInSuccessWithAuthResult: function(authResult, redirectUrl) {
            console.log('User signed in successfully:', authResult.user);
            return true;
        },
        signInFailure: function(error) {
            console.error('Sign-in failed:', error);
            return Promise.resolve();
        }
    },
    signInFlow: 'popup',
    credentialHelper: 'none'
};

// Storage configuration
export const storageConfig = {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    imagePaths: {
        products: 'products/',
        categories: 'categories/',
        users: 'users/',
        temp: 'temp/'
    },
    compressionQuality: 0.8,
    maxWidth: 1200,
    maxHeight: 1200,
    thumbnailSize: 300
};

// Firestore collections
export const collections = {
    PRODUCTS: 'products',
    CATEGORIES: 'categories',
    ORDERS: 'orders',
    USERS: 'users',
    SETTINGS: 'settings',
    ANALYTICS: 'analytics',
    PROMOTIONS: 'promotions'
};

// Security rules helper
export const securityRules = {
    // Check if user is admin
    isAdmin: (user) => {
        return user && user.customClaims && user.customClaims.admin === true;
    },
    
    // Check if user is manager
    isManager: (user) => {
        return user && user.customClaims && 
               (user.customClaims.admin === true || user.customClaims.manager === true);
    },
    
    // Check if user owns the resource
    isOwner: (user, resourceUserId) => {
        return user && user.uid === resourceUserId;
    }
};

// Error handling
export const handleFirebaseError = (error) => {
    const errorMessages = {
        'auth/user-not-found': 'المستخدم غير موجود',
        'auth/wrong-password': 'كلمة المرور غير صحيحة',
        'auth/email-already-in-use': 'البريد الإلكتروني مستخدم بالفعل',
        'auth/weak-password': 'كلمة المرور ضعيفة',
        'auth/invalid-email': 'البريد الإلكتروني غير صحيح',
        'auth/too-many-requests': 'محاولات كثيرة، حاول لاحقاً',
        'storage/unauthorized': 'غير مصرح لك بهذا الإجراء',
        'storage/canceled': 'تم إلغاء العملية',
        'storage/unknown': 'حدث خطأ غير معروف',
        'firestore/permission-denied': 'ليس لديك صلاحية للوصول',
        'firestore/unavailable': 'الخدمة غير متاحة حالياً'
    };
    
    return errorMessages[error.code] || error.message || 'حدث خطأ غير متوقع';
};

// Utility functions
export const utils = {
    // Generate unique ID
    generateId: () => {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },
    
    // Format timestamp
    formatTimestamp: (timestamp) => {
        return new Date(timestamp.toDate()).toLocaleString('ar-EG');
    },
    
    // Validate image file
    validateImageFile: (file) => {
        if (!file) return { valid: false, error: 'لم يتم اختيار ملف' };
        
        if (!storageConfig.allowedTypes.includes(file.type)) {
            return { valid: false, error: 'نوع الملف غير مدعوم' };
        }
        
        if (file.size > storageConfig.maxFileSize) {
            return { valid: false, error: 'حجم الملف كبير جداً' };
        }
        
        return { valid: true };
    },
    
    // Compress image
    compressImage: (file, quality = storageConfig.compressionQuality) => {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                const { width, height } = img;
                const maxWidth = storageConfig.maxWidth;
                const maxHeight = storageConfig.maxHeight;
                
                let newWidth = width;
                let newHeight = height;
                
                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    newWidth = width * ratio;
                    newHeight = height * ratio;
                }
                
                canvas.width = newWidth;
                canvas.height = newHeight;
                
                ctx.drawImage(img, 0, 0, newWidth, newHeight);
                
                canvas.toBlob(resolve, file.type, quality);
            };
            
            img.src = URL.createObjectURL(file);
        });
    }
};

// Export default configuration
export default {
    app,
    auth,
    db,
    storage,
    analytics,
    collections,
    storageConfig,
    authConfig,
    securityRules,
    handleFirebaseError,
    utils
};
