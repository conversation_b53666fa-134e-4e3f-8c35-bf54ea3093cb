<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار القائمة - مطعم محمد الاشرافي</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-success { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .status-warning { background: #f39c12; }
        .status-pending { background: #95a5a6; }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .test-results {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .menu-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .mini-menu-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .mini-menu-item img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 10px;
        }
        
        .mini-menu-item .icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            color: white;
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-flask"></i> اختبار نظام القائمة</h1>
        <p>اختبار شامل لنظام عرض المنتجات والتزامن في الوقت الفعلي</p>

        <!-- System Status -->
        <div class="test-section">
            <h2><i class="fas fa-heartbeat"></i> حالة النظام</h2>
            <div id="systemStatus">
                <div><span class="status-indicator status-pending" id="supabaseStatus"></span> Supabase Connection</div>
                <div><span class="status-indicator status-pending" id="storageStatus"></span> Storage Manager</div>
                <div><span class="status-indicator status-pending" id="menuManagerStatus"></span> Menu Manager</div>
                <div><span class="status-indicator status-pending" id="dataStatus"></span> Data Loading</div>
            </div>
        </div>

        <!-- Controls -->
        <div class="test-section">
            <h2><i class="fas fa-cogs"></i> أدوات التحكم</h2>
            <button class="btn" onclick="testLoadData()">تحميل البيانات</button>
            <button class="btn" onclick="testAddProduct()">إضافة منتج تجريبي</button>
            <button class="btn" onclick="testRefreshData()">تحديث البيانات</button>
            <button class="btn" onclick="testClearData()">مسح البيانات</button>
            <button class="btn" onclick="exportData()">تصدير البيانات</button>
        </div>

        <!-- Data Display -->
        <div class="test-section">
            <h2><i class="fas fa-database"></i> البيانات المحملة</h2>
            <div id="dataInfo">
                <p>الفئات: <span id="categoriesCount">0</span></p>
                <p>المنتجات: <span id="productsCount">0</span></p>
                <p>آخر تحديث: <span id="lastUpdate">-</span></p>
            </div>
            <div class="test-results" id="dataResults"></div>
        </div>

        <!-- Menu Preview -->
        <div class="test-section">
            <h2><i class="fas fa-eye"></i> معاينة القائمة</h2>
            <div class="menu-preview" id="menuPreview"></div>
        </div>

        <!-- Logs -->
        <div class="test-section">
            <h2><i class="fas fa-terminal"></i> سجل الأحداث</h2>
            <div class="test-results" id="logOutput"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/supabase-storage.js"></script>
    <script src="js/real-time-menu-manager.js"></script>

    <script>
        // Test System
        class MenuTestSystem {
            constructor() {
                this.logs = [];
                this.init();
            }

            init() {
                this.log('🚀 Test system initializing...');
                this.checkSystemStatus();
                this.setupEventListeners();
                
                // Auto-refresh status every 2 seconds
                setInterval(() => this.checkSystemStatus(), 2000);
            }

            log(message) {
                const timestamp = new Date().toLocaleTimeString('ar-EG');
                this.logs.unshift(`[${timestamp}] ${message}`);
                
                // Keep only last 50 logs
                if (this.logs.length > 50) {
                    this.logs = this.logs.slice(0, 50);
                }
                
                this.updateLogDisplay();
            }

            updateLogDisplay() {
                const logOutput = document.getElementById('logOutput');
                logOutput.innerHTML = this.logs.map(log => `<div>${log}</div>`).join('');
            }

            checkSystemStatus() {
                // Check Supabase
                const supabaseStatus = window.supabaseManager && window.supabaseManager.isInitialized;
                this.updateStatus('supabaseStatus', supabaseStatus);

                // Check Storage
                const storageStatus = window.supabaseStorageManager && window.supabaseStorageManager.isInitialized;
                this.updateStatus('storageStatus', storageStatus);

                // Check Menu Manager
                const menuManagerStatus = window.realTimeMenuManager && window.realTimeMenuManager.isDataLoaded();
                this.updateStatus('menuManagerStatus', menuManagerStatus);

                // Check Data
                const dataStatus = menuManagerStatus && window.realTimeMenuManager.getProducts().length > 0;
                this.updateStatus('dataStatus', dataStatus);

                // Update data info
                if (window.realTimeMenuManager) {
                    const categories = window.realTimeMenuManager.getCategories();
                    const products = window.realTimeMenuManager.getProducts();
                    
                    document.getElementById('categoriesCount').textContent = categories.length;
                    document.getElementById('productsCount').textContent = products.length;
                    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('ar-EG');
                    
                    this.updateMenuPreview(products);
                }
            }

            updateStatus(elementId, isSuccess) {
                const element = document.getElementById(elementId);
                element.className = `status-indicator ${isSuccess ? 'status-success' : 'status-error'}`;
            }

            updateMenuPreview(products) {
                const preview = document.getElementById('menuPreview');
                
                if (products.length === 0) {
                    preview.innerHTML = '<p>لا توجد منتجات للعرض</p>';
                    return;
                }

                const previewHtml = products.slice(0, 6).map(product => `
                    <div class="mini-menu-item">
                        ${product.image_url ? 
                            `<img src="${product.image_url}" alt="${product.name}">` :
                            `<div class="icon"><i class="${product.icon || 'fas fa-utensils'}"></i></div>`
                        }
                        <h4>${product.name}</h4>
                        <p>${product.price} جنيه</p>
                        <small>${product.category || 'عام'}</small>
                    </div>
                `).join('');

                preview.innerHTML = previewHtml;
            }

            setupEventListeners() {
                // Listen for menu data events
                window.addEventListener('menuDataLoaded', () => {
                    this.log('✅ Menu data loaded');
                    this.checkSystemStatus();
                });

                window.addEventListener('menuDataRefreshed', () => {
                    this.log('🔄 Menu data refreshed');
                    this.checkSystemStatus();
                });
            }
        }

        // Test Functions
        function testLoadData() {
            testSystem.log('🔄 Testing data load...');
            if (window.realTimeMenuManager) {
                window.realTimeMenuManager.refresh();
            } else {
                testSystem.log('❌ Menu manager not available');
            }
        }

        function testAddProduct() {
            testSystem.log('➕ Adding test product...');
            const testProduct = {
                id: 'test_' + Date.now(),
                name: 'منتج تجريبي',
                description: 'هذا منتج تجريبي للاختبار',
                price: 50,
                category_id: '1',
                category: 'المقبلات',
                icon: 'fas fa-star',
                is_available: true,
                is_featured: true,
                created_at: new Date().toISOString()
            };

            // Add to localStorage to trigger sync
            const products = JSON.parse(localStorage.getItem('restaurant_products') || '[]');
            products.unshift(testProduct);
            localStorage.setItem('restaurant_products', JSON.stringify(products));
            
            // Trigger refresh
            window.dispatchEvent(new CustomEvent('productAdded', { detail: testProduct }));
            
            testSystem.log('✅ Test product added');
        }

        function testRefreshData() {
            testSystem.log('🔄 Refreshing all data...');
            if (window.realTimeMenuManager) {
                window.realTimeMenuManager.refresh();
            }
        }

        function testClearData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                localStorage.removeItem('restaurant_products');
                testSystem.log('🗑️ Data cleared');
                testRefreshData();
            }
        }

        function exportData() {
            const data = {
                categories: window.realTimeMenuManager ? window.realTimeMenuManager.getCategories() : [],
                products: window.realTimeMenuManager ? window.realTimeMenuManager.getProducts() : [],
                timestamp: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `menu-data-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            testSystem.log('📁 Data exported');
        }

        // Initialize test system
        const testSystem = new MenuTestSystem();
    </script>
</body>
</html>
