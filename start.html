<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام طلبات مطعم محمد الاشرافي</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            font-size: 4rem;
            color: #2c5aa0;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #2c5aa0;
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 40px;
        }
        
        .buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .btn {
            padding: 20px;
            border: none;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            color: white;
        }
        
        .btn-customer {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
        }
        
        .btn-admin {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }
        
        .btn i {
            font-size: 2rem;
        }
        
        .features {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }
        
        .features h3 {
            color: #2c5aa0;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            text-align: right;
        }
        
        .feature {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #666;
        }
        
        .feature i {
            color: #28a745;
            font-size: 1.2rem;
        }
        
        .info {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-right: 4px solid #2c5aa0;
        }
        
        .info h4 {
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        
        .info p {
            color: #666;
            line-height: 1.6;
        }
        
        .credentials {
            background: #fff3cd;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            border-right: 4px solid #ffc107;
            font-size: 0.9rem;
        }
        
        .credentials strong {
            color: #856404;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .buttons {
                grid-template-columns: 1fr;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <i class="fas fa-utensils"></i>
        </div>
        
        <h1>مطعم محمد الاشرافي</h1>
        <p class="subtitle">نظام الطلبات الإلكتروني المتكامل</p>
        
        <div class="buttons">
            <a href="index.html" class="btn btn-customer">
                <i class="fas fa-shopping-cart"></i>
                <span>واجهة العملاء</span>
                <small>تصفح القائمة وطلب الطعام</small>
            </a>
            
            <a href="admin/login.html" class="btn btn-admin">
                <i class="fas fa-user-shield"></i>
                <span>لوحة الإدارة</span>
                <small>إدارة المطعم والطلبات</small>
            </a>
        </div>
        
        <div class="features">
            <h3>🌟 مميزات النظام</h3>
            <div class="features-grid">
                <div class="feature">
                    <i class="fas fa-check"></i>
                    <span>تصميم متجاوب</span>
                </div>
                <div class="feature">
                    <i class="fas fa-check"></i>
                    <span>تكامل الواتساب</span>
                </div>
                <div class="feature">
                    <i class="fas fa-check"></i>
                    <span>لوحة إدارة شاملة</span>
                </div>
                <div class="feature">
                    <i class="fas fa-check"></i>
                    <span>إدارة القائمة</span>
                </div>
                <div class="feature">
                    <i class="fas fa-check"></i>
                    <span>متابعة الطلبات</span>
                </div>
                <div class="feature">
                    <i class="fas fa-check"></i>
                    <span>تقارير وإحصائيات</span>
                </div>
            </div>
        </div>
        
        <div class="info">
            <h4>📱 رقم الواتساب للطلبات</h4>
            <p><strong>+201014840269</strong></p>
        </div>
        
        <div class="credentials">
            <h4>🔐 بيانات تسجيل الدخول للإدارة:</h4>
            <p><strong>المدير العام:</strong> admin / admin123</p>
            <p><strong>صاحب المطعم:</strong> محمد / alashrafi2024</p>
            <p><strong>المدير:</strong> manager / restaurant123</p>
            <p><strong>الموظف:</strong> staff / staff2024</p>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e1e5e9; color: #666; font-size: 0.9rem;">
            <p>© 2024 محمد الاشرافي - جميع الحقوق محفوظة</p>
            <p style="margin-top: 5px;">
                <a href="test.html" style="color: #2c5aa0; text-decoration: none;">🧪 صفحة الاختبار</a> |
                <a href="README.md" style="color: #2c5aa0; text-decoration: none;">📖 دليل الاستخدام</a>
            </p>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate logo on load
            const logo = document.querySelector('.logo');
            logo.style.transform = 'scale(0)';
            logo.style.transition = 'transform 0.6s ease';
            
            setTimeout(() => {
                logo.style.transform = 'scale(1)';
            }, 300);
            
            // Add hover sound effect (optional)
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('mouseenter', () => {
                    // You can add sound effects here if needed
                });
            });
            
            // Check system status
            checkSystemStatus();
        });
        
        function checkSystemStatus() {
            // Simple system check
            const features = [
                'localStorage' in window,
                'sessionStorage' in window,
                'fetch' in window,
                'Promise' in window
            ];
            
            const allSupported = features.every(feature => feature);
            
            if (!allSupported) {
                const warning = document.createElement('div');
                warning.style.cssText = `
                    background: #f8d7da;
                    color: #721c24;
                    padding: 15px;
                    border-radius: 10px;
                    margin-top: 20px;
                    border-right: 4px solid #dc3545;
                `;
                warning.innerHTML = `
                    <strong>⚠️ تحذير:</strong> 
                    متصفحك قد لا يدعم جميع ميزات النظام. 
                    يُنصح بتحديث المتصفح للحصول على أفضل تجربة.
                `;
                document.querySelector('.container').appendChild(warning);
            }
        }
    </script>
</body>
</html>
