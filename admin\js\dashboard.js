// Dashboard Management System

class AdminDashboard {
    constructor() {
        this.currentSection = 'overview';
        this.stats = {
            totalOrders: 0,
            totalRevenue: 0,
            totalMenuItems: 0,
            totalCustomers: 0
        };
        this.orders = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadDashboardData();
        this.updateStats();
        this.loadRecentOrders();
        this.loadPopularItems();
        this.showSection('overview');
    }

    bindEvents() {
        // Navigation clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.nav-link')) {
                e.preventDefault();
                const section = e.target.closest('.nav-link').dataset.section;
                this.showSection(section);
            }

            // Menu tab clicks
            if (e.target.closest('.menu-tab')) {
                const category = e.target.dataset.category;
                this.filterMenuItems(category);
            }

            // Order status filter
            if (e.target.id === 'orderStatusFilter') {
                this.filterOrders(e.target.value);
            }
        });

        // Settings form submission
        const restaurantForm = document.getElementById('restaurantInfoForm');
        if (restaurantForm) {
            restaurantForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveRestaurantInfo();
            });
        }

        // Auto-refresh data every 30 seconds
        setInterval(() => {
            this.refreshData();
        }, 30000);
    }

    showSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).closest('.nav-item').classList.add('active');

        // Update sections
        document.querySelectorAll('.dashboard-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(sectionName).classList.add('active');

        this.currentSection = sectionName;

        // Load section-specific data
        switch (sectionName) {
            case 'menu-management':
                this.loadMenuManagement();
                break;
            case 'orders':
                this.loadOrdersManagement();
                break;
            case 'analytics':
                this.loadAnalytics();
                break;
            case 'settings':
                this.loadSettings();
                break;
        }
    }

    loadDashboardData() {
        // Load data from localStorage or initialize
        const savedData = localStorage.getItem('restaurant_dashboard_data');
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                this.stats = data.stats || this.stats;
                this.orders = data.orders || [];
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        // Initialize with menu items count
        this.stats.totalMenuItems = getAllMenuItems().length;
    }

    updateStats() {
        // Calculate stats from orders
        this.stats.totalOrders = this.orders.length;
        this.stats.totalRevenue = this.orders.reduce((total, order) => total + (order.total || 0), 0);
        this.stats.totalCustomers = new Set(this.orders.map(order => order.customerPhone)).size;

        // Update UI
        document.getElementById('totalOrders').textContent = this.stats.totalOrders;
        document.getElementById('totalRevenue').textContent = this.stats.totalRevenue + ' جنيه';
        document.getElementById('totalMenuItems').textContent = this.stats.totalMenuItems;
        document.getElementById('totalCustomers').textContent = this.stats.totalCustomers;
    }

    loadRecentOrders() {
        const recentOrdersList = document.getElementById('recentOrdersList');
        if (!recentOrdersList) return;

        if (this.orders.length === 0) {
            recentOrdersList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-shopping-cart" style="font-size: 2rem; color: #ccc; margin-bottom: 10px;"></i>
                    <p>لا توجد طلبات حتى الآن</p>
                </div>
            `;
            return;
        }

        const recentOrders = this.orders.slice(-5).reverse();
        recentOrdersList.innerHTML = recentOrders.map(order => `
            <div class="order-item">
                <div class="order-header">
                    <span class="order-id">#${order.id}</span>
                    <span class="order-time">${this.formatDate(order.timestamp)}</span>
                </div>
                <div class="order-details">
                    <strong>${order.customerName}</strong><br>
                    ${order.items.map(item => `${item.name} (${item.quantity})`).join(', ')}
                </div>
                <div class="order-total">المجموع: ${order.total} جنيه</div>
            </div>
        `).join('');
    }

    loadPopularItems() {
        const popularItemsList = document.getElementById('popularItemsList');
        if (!popularItemsList) return;

        // Calculate item popularity from orders
        const itemCounts = {};
        this.orders.forEach(order => {
            order.items.forEach(item => {
                itemCounts[item.id] = (itemCounts[item.id] || 0) + item.quantity;
            });
        });

        // Get top 4 items
        const sortedItems = Object.entries(itemCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 4);

        if (sortedItems.length === 0) {
            popularItemsList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-chart-bar" style="font-size: 2rem; color: #ccc; margin-bottom: 10px;"></i>
                    <p>لا توجد بيانات كافية لعرض الأطباق الشائعة</p>
                </div>
            `;
            return;
        }

        popularItemsList.innerHTML = sortedItems.map(([itemId, count]) => {
            const item = getItemById(itemId);
            if (!item) return '';
            
            return `
                <div class="popular-item">
                    <div class="popular-item-icon">
                        <i class="${item.icon}"></i>
                    </div>
                    <div class="popular-item-name">${item.name}</div>
                    <div class="popular-item-count">طُلب ${count} مرة</div>
                </div>
            `;
        }).join('');
    }

    loadMenuManagement() {
        this.renderMenuTable();
    }

    renderMenuTable(category = 'all') {
        const tableBody = document.getElementById('menuItemsTableBody');
        if (!tableBody) return;

        const items = category === 'all' ? getAllMenuItems() : getItemsByCategory(category);
        
        tableBody.innerHTML = items.map(item => `
            <tr>
                <td>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <i class="${item.icon}" style="color: #2c5aa0;"></i>
                        ${item.name}
                    </div>
                </td>
                <td>${this.getCategoryName(item.category)}</td>
                <td>${item.price} جنيه</td>
                <td>
                    <span class="status-badge ${item.available ? 'status-available' : 'status-unavailable'}">
                        ${item.available ? 'متوفر' : 'غير متوفر'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-sm btn-secondary" onclick="dashboard.editMenuItem('${item.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-sm ${item.available ? 'btn-danger' : 'btn-success'}" 
                                onclick="dashboard.toggleItemAvailability('${item.id}')">
                            <i class="fas fa-${item.available ? 'eye-slash' : 'eye'}"></i>
                        </button>
                        <button class="btn-sm btn-danger" onclick="dashboard.deleteMenuItem('${item.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    filterMenuItems(category) {
        // Update active tab
        document.querySelectorAll('.menu-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');

        // Render filtered items
        this.renderMenuTable(category);
    }

    getCategoryName(category) {
        const categoryNames = {
            'appetizers': 'المقبلات',
            'main-dishes': 'الأطباق الرئيسية',
            'beverages': 'المشروبات',
            'desserts': 'الحلويات'
        };
        return categoryNames[category] || category;
    }

    editMenuItem(itemId) {
        // This will be implemented in menu-management.js
        if (window.menuManager) {
            window.menuManager.editItem(itemId);
        }
    }

    toggleItemAvailability(itemId) {
        // This will be implemented in menu-management.js
        if (window.menuManager) {
            window.menuManager.toggleAvailability(itemId);
        }
    }

    deleteMenuItem(itemId) {
        // This will be implemented in menu-management.js
        if (window.menuManager) {
            window.menuManager.deleteItem(itemId);
        }
    }

    loadOrdersManagement() {
        this.renderOrdersList();
    }

    renderOrdersList(filter = 'all') {
        const ordersContainer = document.getElementById('ordersContainer');
        if (!ordersContainer) return;

        let filteredOrders = this.orders;
        if (filter !== 'all') {
            filteredOrders = this.orders.filter(order => order.status === filter);
        }

        if (filteredOrders.length === 0) {
            ordersContainer.innerHTML = `
                <div class="empty-state" style="text-align: center; padding: 40px;">
                    <i class="fas fa-shopping-cart" style="font-size: 3rem; color: #ccc; margin-bottom: 15px;"></i>
                    <h3>لا توجد طلبات</h3>
                    <p>لم يتم استلام أي طلبات بعد</p>
                </div>
            `;
            return;
        }

        ordersContainer.innerHTML = filteredOrders.reverse().map(order => `
            <div class="order-item">
                <div class="order-header">
                    <span class="order-id">#${order.id}</span>
                    <span class="status-badge status-${order.status}">${this.getStatusName(order.status)}</span>
                    <span class="order-time">${this.formatDate(order.timestamp)}</span>
                </div>
                <div class="order-details">
                    <strong>العميل:</strong> ${order.customerName}<br>
                    <strong>الهاتف:</strong> ${order.customerPhone}<br>
                    <strong>العنوان:</strong> ${order.customerAddress}<br>
                    ${order.notes ? `<strong>ملاحظات:</strong> ${order.notes}<br>` : ''}
                    <strong>الطلبات:</strong><br>
                    ${order.items.map(item => `• ${item.name} × ${item.quantity} = ${item.price * item.quantity} جنيه`).join('<br>')}
                </div>
                <div class="order-total">المجموع: ${order.total} جنيه</div>
                <div class="action-buttons" style="margin-top: 10px;">
                    ${this.getOrderActions(order)}
                </div>
            </div>
        `).join('');
    }

    getStatusName(status) {
        const statusNames = {
            'pending': 'قيد الانتظار',
            'processing': 'قيد التحضير',
            'completed': 'مكتملة',
            'cancelled': 'ملغية'
        };
        return statusNames[status] || status;
    }

    getOrderActions(order) {
        switch (order.status) {
            case 'pending':
                return `
                    <button class="btn-sm btn-success" onclick="dashboard.updateOrderStatus('${order.id}', 'processing')">
                        قبول الطلب
                    </button>
                    <button class="btn-sm btn-danger" onclick="dashboard.updateOrderStatus('${order.id}', 'cancelled')">
                        رفض الطلب
                    </button>
                `;
            case 'processing':
                return `
                    <button class="btn-sm btn-success" onclick="dashboard.updateOrderStatus('${order.id}', 'completed')">
                        تم التحضير
                    </button>
                `;
            default:
                return `<span style="color: #666; font-style: italic;">لا توجد إجراءات متاحة</span>`;
        }
    }

    updateOrderStatus(orderId, newStatus) {
        const orderIndex = this.orders.findIndex(order => order.id === orderId);
        if (orderIndex > -1) {
            this.orders[orderIndex].status = newStatus;
            this.orders[orderIndex].updatedAt = new Date().toISOString();
            
            this.saveDashboardData();
            this.renderOrdersList();
            this.updateStats();
            
            this.showMessage(`تم تحديث حالة الطلب #${orderId}`, 'success');
        }
    }

    filterOrders(status) {
        this.renderOrdersList(status);
    }

    refreshOrders() {
        this.loadOrdersManagement();
        this.showMessage('تم تحديث قائمة الطلبات', 'success');
    }

    loadAnalytics() {
        // Placeholder for analytics implementation
        const monthlyChart = document.getElementById('monthlyChart');
        const topItemsChart = document.getElementById('topItemsChart');
        
        if (monthlyChart) {
            monthlyChart.innerHTML = '<p>سيتم إضافة الرسوم البيانية قريباً</p>';
        }
        
        if (topItemsChart) {
            topItemsChart.innerHTML = '<p>سيتم إضافة الرسوم البيانية قريباً</p>';
        }
    }

    loadSettings() {
        // Load current settings
        const settings = this.getSettings();
        
        // Update form fields
        document.getElementById('restaurantName').value = settings.restaurantName;
        document.getElementById('whatsappNumber').value = settings.whatsappNumber;
        document.getElementById('restaurantAddress').value = settings.restaurantAddress;
        document.getElementById('enableNotifications').checked = settings.enableNotifications;
        document.getElementById('autoAcceptOrders').checked = settings.autoAcceptOrders;
        document.getElementById('showPrices').checked = settings.showPrices;
    }

    getSettings() {
        const defaultSettings = {
            restaurantName: 'محمد الاشرافي',
            whatsappNumber: '+201014840269',
            restaurantAddress: 'القاهرة، مصر',
            enableNotifications: true,
            autoAcceptOrders: false,
            showPrices: true
        };

        try {
            const saved = localStorage.getItem('restaurant_settings');
            return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
        } catch (error) {
            return defaultSettings;
        }
    }

    saveRestaurantInfo() {
        const settings = {
            restaurantName: document.getElementById('restaurantName').value,
            whatsappNumber: document.getElementById('whatsappNumber').value,
            restaurantAddress: document.getElementById('restaurantAddress').value,
            enableNotifications: document.getElementById('enableNotifications').checked,
            autoAcceptOrders: document.getElementById('autoAcceptOrders').checked,
            showPrices: document.getElementById('showPrices').checked
        };

        localStorage.setItem('restaurant_settings', JSON.stringify(settings));
        this.showMessage('تم حفظ الإعدادات بنجاح', 'success');
    }

    saveDashboardData() {
        const data = {
            stats: this.stats,
            orders: this.orders,
            lastUpdated: new Date().toISOString()
        };
        localStorage.setItem('restaurant_dashboard_data', JSON.stringify(data));
    }

    refreshData() {
        if (this.currentSection === 'overview') {
            this.updateStats();
            this.loadRecentOrders();
        } else if (this.currentSection === 'orders') {
            this.loadOrdersManagement();
        }
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('ar-EG', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    showMessage(message, type = 'success') {
        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message show`;
        messageDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            ${message}
        `;
        
        // Add to page
        document.body.appendChild(messageDiv);
        
        // Position message
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '20px';
        messageDiv.style.right = '20px';
        messageDiv.style.zIndex = '9999';
        messageDiv.style.maxWidth = '300px';
        messageDiv.style.padding = '15px';
        messageDiv.style.borderRadius = '10px';
        messageDiv.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
        
        // Remove after 3 seconds
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }
}

// Initialize dashboard
const dashboard = new AdminDashboard();

// Global function for showing sections
function showSection(sectionName) {
    dashboard.showSection(sectionName);
}
