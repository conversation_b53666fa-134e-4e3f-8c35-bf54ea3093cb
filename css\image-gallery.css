/* Image Gallery and Management Styles */
/* تصميم معرض الصور ونظام الإدارة */

/* Image Gallery Container */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.gallery-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Image Wrapper */
.image-wrapper {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
}

.image-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    cursor: pointer;
}

.image-wrapper:hover img {
    transform: scale(1.05);
}

/* Image Overlay */
.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-wrapper:hover .image-overlay {
    opacity: 1;
}

.image-overlay button {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #333;
}

.image-overlay button:hover {
    background: white;
    transform: scale(1.1);
}

.btn-view {
    color: #2c5aa0 !important;
}

.btn-edit {
    color: #28a745 !important;
}

.btn-delete {
    color: #dc3545 !important;
}

/* Primary Badge */
.primary-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

/* Image Info */
.image-info {
    padding: 15px;
    text-align: center;
}

.image-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.image-size {
    color: #666;
    font-size: 0.8rem;
    margin: 0;
}

/* No Images State */
.no-images {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.no-images i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #ddd;
}

.no-images p {
    font-size: 1.2rem;
    margin: 0;
}

/* Image Lightbox */
.image-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.lightbox-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    cursor: default;
    animation: zoomIn 0.3s ease;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
}

.lightbox-close {
    position: absolute;
    top: -50px;
    right: 0;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.lightbox-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.lightbox-controls {
    position: absolute;
    bottom: -60px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
}

.btn-download {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-download:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Image Upload Area */
.image-upload-area {
    border: 2px dashed #ddd;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.image-upload-area:hover,
.image-upload-area.dragover {
    border-color: #2c5aa0;
    background: #f0f7ff;
}

.image-upload-area.dragover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(44, 90, 160, 0.1);
    animation: pulse 1s infinite;
}

.upload-icon {
    font-size: 3rem;
    color: #2c5aa0;
    margin-bottom: 20px;
}

.upload-text {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 10px;
}

.upload-hint {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 20px;
}

.upload-button {
    background: linear-gradient(135deg, #2c5aa0, #1e3a8a);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.upload-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(44, 90, 160, 0.3);
}

/* Image Preview */
.image-preview-container {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 300px;
    max-height: 400px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    overflow: hidden;
    animation: slideInRight 0.3s ease;
}

.preview-header {
    background: #2c5aa0;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-title {
    font-weight: 600;
    margin: 0;
}

.preview-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.preview-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.preview-content {
    padding: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.preview-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.preview-item:last-child {
    border-bottom: none;
}

.preview-thumbnail {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
    background: #f8f9fa;
}

.preview-info {
    flex: 1;
}

.preview-name {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 5px;
    color: #333;
}

.preview-size {
    font-size: 0.8rem;
    color: #666;
}

.preview-status {
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.status-uploading {
    background: #fff3cd;
    color: #856404;
}

.status-success {
    background: #d4edda;
    color: #155724;
}

.status-error {
    background: #f8d7da;
    color: #721c24;
}

/* Progress Bar */
.upload-progress {
    width: 100%;
    height: 4px;
    background: #eee;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 10px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #2c5aa0, #1e3a8a);
    border-radius: 2px;
    transition: width 0.3s ease;
    width: 0%;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes zoomIn {
    from { 
        opacity: 0;
        transform: scale(0.8);
    }
    to { 
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 0.8; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .image-gallery {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .gallery-item {
        border-radius: 8px;
    }
    
    .image-wrapper {
        height: 150px;
    }
    
    .image-overlay button {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
    
    .image-info {
        padding: 12px;
    }
    
    .lightbox-content {
        max-width: 95vw;
        max-height: 95vh;
    }
    
    .lightbox-content img {
        max-height: 70vh;
    }
    
    .lightbox-close {
        top: -40px;
        width: 35px;
        height: 35px;
    }
    
    .lightbox-controls {
        bottom: -50px;
    }
    
    .image-preview-container {
        width: 280px;
        right: 10px;
        top: 10px;
    }
    
    .image-upload-area {
        padding: 30px 15px;
    }
    
    .upload-icon {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .image-gallery {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }
    
    .image-wrapper {
        height: 120px;
    }
    
    .image-overlay {
        gap: 5px;
    }
    
    .image-overlay button {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
    
    .image-preview-container {
        width: calc(100vw - 20px);
        right: 10px;
        left: 10px;
    }
}
