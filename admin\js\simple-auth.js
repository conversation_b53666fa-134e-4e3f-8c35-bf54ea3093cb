// Simple and Stable Authentication System
// نظام مصادقة بسيط ومستقر

class SimpleAuth {
    constructor() {
        this.sessionKey = 'admin_session_stable';
        this.users = [
            { username: 'admin', password: 'admin123', role: 'admin' },
            { username: 'محمد', password: 'alashrafi2024', role: 'owner' }
        ];
        
        console.log('SimpleAuth initialized');
        this.init();
    }

    init() {
        // Only run auth check after page is fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => this.checkAuth(), 500);
            });
        } else {
            setTimeout(() => this.checkAuth(), 500);
        }
    }

    checkAuth() {
        const currentPage = window.location.pathname;
        console.log('Checking auth for page:', currentPage);

        if (currentPage.includes('login.html')) {
            // On login page - check if already logged in
            if (this.isLoggedIn()) {
                console.log('Already logged in, redirecting to dashboard');
                this.redirectTo('dashboard.html');
            } else {
                console.log('Not logged in, staying on login page');
                this.setupLoginForm();
            }
        } else {
            // On other admin pages - require login
            if (!this.isLoggedIn()) {
                console.log('Not logged in, redirecting to login');
                this.redirectTo('login.html');
            } else {
                console.log('Logged in, staying on current page');
                this.updateUserInfo();
            }
        }
    }

    isLoggedIn() {
        try {
            const session = localStorage.getItem(this.sessionKey);
            if (!session) {
                console.log('No session found');
                return false;
            }

            const sessionData = JSON.parse(session);
            if (!sessionData.username || !sessionData.loginTime) {
                console.log('Invalid session data');
                return false;
            }

            // Check if session is expired (24 hours)
            const loginTime = new Date(sessionData.loginTime);
            const now = new Date();
            const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

            if (hoursDiff > 24) {
                console.log('Session expired');
                this.logout();
                return false;
            }

            console.log('Valid session found for:', sessionData.username);
            return true;
        } catch (error) {
            console.error('Error checking login status:', error);
            return false;
        }
    }

    login(username, password) {
        console.log('Attempting login for:', username);
        
        const user = this.users.find(u => u.username === username && u.password === password);
        if (!user) {
            console.log('Invalid credentials');
            return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
        }

        // Create session
        const sessionData = {
            username: user.username,
            role: user.role,
            loginTime: new Date().toISOString()
        };

        try {
            localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
            console.log('Session created successfully');
            return { success: true, message: 'تم تسجيل الدخول بنجاح' };
        } catch (error) {
            console.error('Error creating session:', error);
            return { success: false, message: 'خطأ في تسجيل الدخول' };
        }
    }

    logout() {
        console.log('Logging out');
        localStorage.removeItem(this.sessionKey);
        this.redirectTo('login.html');
    }

    redirectTo(page) {
        console.log('Redirecting to:', page);
        // Use a simple redirect without any complex logic
        window.location.href = page;
    }

    setupLoginForm() {
        const form = document.getElementById('loginForm');
        if (!form) {
            console.log('Login form not found');
            return;
        }

        console.log('Setting up login form');
        
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username')?.value;
            const password = document.getElementById('password')?.value;
            
            if (!username || !password) {
                this.showMessage('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }

            const result = this.login(username, password);
            this.showMessage(result.message, result.success ? 'success' : 'error');
            
            if (result.success) {
                setTimeout(() => {
                    this.redirectTo('dashboard.html');
                }, 1000);
            }
        });
    }

    updateUserInfo() {
        try {
            const session = localStorage.getItem(this.sessionKey);
            if (!session) return;

            const sessionData = JSON.parse(session);
            
            // Update user info in UI
            const userNameElement = document.getElementById('userName');
            if (userNameElement) {
                userNameElement.textContent = sessionData.username;
            }

            const userRoleElement = document.getElementById('userRole');
            if (userRoleElement) {
                userRoleElement.textContent = sessionData.role === 'admin' ? 'مدير' : 'مالك';
            }

            console.log('User info updated');
        } catch (error) {
            console.error('Error updating user info:', error);
        }
    }

    showMessage(message, type) {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.auth-message');
        existingMessages.forEach(msg => msg.remove());

        // Create new message
        const messageDiv = document.createElement('div');
        messageDiv.className = `auth-message alert alert-${type}`;
        messageDiv.style.cssText = `
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
            background: ${type === 'success' ? '#d4edda' : '#f8d7da'};
            color: ${type === 'success' ? '#155724' : '#721c24'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : '#f5c6cb'};
        `;
        messageDiv.textContent = message;

        // Insert message
        const form = document.getElementById('loginForm');
        if (form) {
            form.insertBefore(messageDiv, form.firstChild);
        }

        // Auto remove after 3 seconds
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }

    getCurrentUser() {
        try {
            const session = localStorage.getItem(this.sessionKey);
            if (!session) return null;
            
            const sessionData = JSON.parse(session);
            return {
                username: sessionData.username,
                role: sessionData.role
            };
        } catch (error) {
            console.error('Error getting current user:', error);
            return null;
        }
    }
}

// Initialize auth system
console.log('Initializing SimpleAuth...');
const simpleAuth = new SimpleAuth();

// Make available globally
window.simpleAuth = simpleAuth;

// Setup logout functionality
document.addEventListener('DOMContentLoaded', function() {
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                simpleAuth.logout();
            }
        });
    }
});

console.log('SimpleAuth loaded successfully');
