[debug] [2025-07-15T01:12:58.869Z] ----------------------------------------------------------------------
[debug] [2025-07-15T01:12:58.872Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js apps:create web alashrafi-restaurant
[debug] [2025-07-15T01:12:58.872Z] CLI Version:   14.10.1
[debug] [2025-07-15T01:12:58.872Z] Platform:      win32
[debug] [2025-07-15T01:12:58.872Z] Node Version:  v22.14.0
[debug] [2025-07-15T01:12:58.873Z] Time:          Tue Jul 15 2025 04:12:58 GMT+0300 (Eastern European Summer Time)
[debug] [2025-07-15T01:12:58.873Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-15T01:12:59.052Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-15T01:12:59.052Z] > authorizing via signed-in user (<EMAIL>)
[info] Create your WEB app in project al-ishrafi-accounting-2025:
[debug] [2025-07-15T01:12:59.056Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-15T01:12:59.057Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-07-15T01:12:59.058Z] >>> [apiv2][query] POST https://firebase.googleapis.com/v1beta1/projects/al-ishrafi-accounting-2025/webApps [none]
[debug] [2025-07-15T01:12:59.058Z] >>> [apiv2][body] POST https://firebase.googleapis.com/v1beta1/projects/al-ishrafi-accounting-2025/webApps {"displayName":"alashrafi-restaurant"}
[debug] [2025-07-15T01:12:59.290Z] <<< [apiv2][status] POST https://firebase.googleapis.com/v1beta1/projects/al-ishrafi-accounting-2025/webApps 429
[debug] [2025-07-15T01:12:59.290Z] <<< [apiv2][body] POST https://firebase.googleapis.com/v1beta1/projects/al-ishrafi-accounting-2025/webApps {"error":{"code":429,"message":"Quota exceeded for quota metric 'Provision requests' and limit 'Provision requests per minute' of service 'firebase.googleapis.com' for consumer 'project_number:************'.","status":"RESOURCE_EXHAUSTED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"RATE_LIMIT_EXCEEDED","domain":"googleapis.com","metadata":{"quota_limit":"ProvisionPerMinutePerProject","quota_unit":"1/min/{project}","consumer":"projects/************","quota_metric":"firebase.googleapis.com/provision_requests","quota_location":"global","quota_limit_value":"60","service":"firebase.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Request a higher quota limit.","url":"https://cloud.google.com/docs/quotas/help/request_increase"}]}]}}
[debug] [2025-07-15T01:12:59.291Z] Request to https://firebase.googleapis.com/v1beta1/projects/al-ishrafi-accounting-2025/webApps had HTTP Error: 429, Quota exceeded for quota metric 'Provision requests' and limit 'Provision requests per minute' of service 'firebase.googleapis.com' for consumer 'project_number:************'.
[debug] [2025-07-15T01:12:59.463Z] FirebaseError: Request to https://firebase.googleapis.com/v1beta1/projects/al-ishrafi-accounting-2025/webApps had HTTP Error: 429, Quota exceeded for quota metric 'Provision requests' and limit 'Provision requests per minute' of service 'firebase.googleapis.com' for consumer 'project_number:************'.
    at responseToError (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\responseToError.js:52:12)
    at RetryOperation._fn (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\apiv2.js:312:77)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
[error] 
[error] Error: Failed to create Web app for project al-ishrafi-accounting-2025. See firebase-debug.log for more info.
