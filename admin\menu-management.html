<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة القائمة - مطعم محمد الاشرافي</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="../css/advanced-image-manager.css">
    <link rel="stylesheet" href="css/menu-management.css">
</head>
<body>
    <!-- Header -->
    <header class="admin-header">
        <div class="header-content">
            <div class="header-right">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>إدارة القائمة</h1>
            </div>
            <div class="header-left">
                <div class="user-info">
                    <span>مرحباً، <strong id="adminName">المدير</strong></span>
                    <button class="logout-btn" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <aside class="admin-sidebar" id="adminSidebar">
        <div class="sidebar-header">
            <h3>لوحة الإدارة</h3>
        </div>
        <nav class="sidebar-nav">
            <a href="dashboard.html" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </a>
            <a href="menu-management.html" class="nav-item active">
                <i class="fas fa-utensils"></i>
                <span>إدارة القائمة</span>
            </a>
            <a href="orders.html" class="nav-item">
                <i class="fas fa-shopping-cart"></i>
                <span>الطلبات</span>
            </a>
            <a href="customers.html" class="nav-item">
                <i class="fas fa-users"></i>
                <span>العملاء</span>
            </a>
            <a href="analytics.html" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير</span>
            </a>
            <a href="settings.html" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="content-wrapper">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <h2>إدارة القائمة والمنتجات</h2>
                    <p>إضافة وتعديل وإدارة منتجات المطعم</p>
                </div>
                <div class="page-actions">
                    <button class="btn-primary" onclick="openProductModal()">
                        <i class="fas fa-plus"></i>
                        إضافة منتج جديد
                    </button>
                    <button class="btn-secondary" onclick="openCategoryModal()">
                        <i class="fas fa-tags"></i>
                        إدارة الأقسام
                    </button>
                    <button class="btn-secondary" onclick="advancedImageManager.openGalleryModal()">
                        <i class="fas fa-images"></i>
                        معرض الصور
                    </button>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="filters-section">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchProducts" placeholder="البحث في المنتجات...">
                </div>
                <div class="filter-controls">
                    <select id="categoryFilter">
                        <option value="">جميع الأقسام</option>
                    </select>
                    <select id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="available">متوفر</option>
                        <option value="unavailable">غير متوفر</option>
                    </select>
                    <select id="sortBy">
                        <option value="newest">الأحدث أولاً</option>
                        <option value="oldest">الأقدم أولاً</option>
                        <option value="name">حسب الاسم</option>
                        <option value="price">حسب السعر</option>
                    </select>
                </div>
                <div class="view-controls">
                    <button class="view-btn active" data-view="grid" onclick="changeView('grid')">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-btn" data-view="list" onclick="changeView('list')">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="products-container">
                <div class="products-grid" id="productsGrid">
                    <!-- Products will be loaded here -->
                </div>
            </div>

            <!-- Pagination -->
            <div class="pagination-container" id="paginationContainer" style="display: none;">
                <div class="pagination">
                    <button class="page-btn" onclick="loadPreviousPage()">
                        <i class="fas fa-chevron-right"></i>
                        السابق
                    </button>
                    <div class="page-numbers" id="pageNumbers">
                        <!-- Page numbers will be generated here -->
                    </div>
                    <button class="page-btn" onclick="loadNextPage()">
                        التالي
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
                <div class="pagination-info">
                    <span id="paginationInfo">عرض 1-10 من 50 منتج</span>
                </div>
            </div>
        </div>
    </main>

    <!-- Product Modal -->
    <div class="modal" id="productModal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3 id="productModalTitle">إضافة منتج جديد</h3>
                <button class="close-modal" onclick="closeProductModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="productForm" class="product-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="productName">اسم المنتج *</label>
                            <input type="text" id="productName" required>
                        </div>
                        <div class="form-group">
                            <label for="productNameEn">الاسم بالإنجليزية</label>
                            <input type="text" id="productNameEn">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="productPrice">السعر (جنيه) *</label>
                            <input type="number" id="productPrice" step="0.01" min="0" required>
                        </div>
                        <div class="form-group">
                            <label for="productCategory">القسم *</label>
                            <select id="productCategory" required>
                                <option value="">اختر القسم</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="productDescription">الوصف *</label>
                        <textarea id="productDescription" rows="3" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="productDescriptionEn">الوصف بالإنجليزية</label>
                        <textarea id="productDescriptionEn" rows="3"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="preparationTime">وقت التحضير (دقيقة)</label>
                            <input type="number" id="preparationTime" min="1" value="15">
                        </div>
                        <div class="form-group">
                            <label for="calories">السعرات الحرارية</label>
                            <input type="number" id="calories" min="0">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="ingredients">المكونات (مفصولة بفاصلة)</label>
                        <textarea id="ingredients" rows="2" placeholder="مثال: لحم بقري، خبز، خس، طماطم"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="allergens">مسببات الحساسية (مفصولة بفاصلة)</label>
                        <textarea id="allergens" rows="2" placeholder="مثال: جلوتين، ألبان، مكسرات"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="tags">العلامات (مفصولة بفاصلة)</label>
                        <input type="text" id="tags" placeholder="مثال: مشوي، حار، نباتي">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="isAvailable" checked>
                                <span class="checkmark"></span>
                                متوفر للطلب
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="isFeatured">
                                <span class="checkmark"></span>
                                منتج مميز
                            </label>
                        </div>
                    </div>

                    <!-- Product Images Section -->
                    <div class="form-section">
                        <h4>صور المنتج</h4>
                        <div class="images-management">
                            <div class="current-images" id="currentImages">
                                <!-- Current images will be displayed here -->
                            </div>
                            <div class="image-actions">
                                <button type="button" class="btn-secondary" onclick="advancedImageManager.openUploadModal(currentProductId)">
                                    <i class="fas fa-plus"></i>
                                    إضافة صور
                                </button>
                                <button type="button" class="btn-secondary" onclick="advancedImageManager.openGalleryModal(currentProductId)">
                                    <i class="fas fa-images"></i>
                                    إدارة الصور
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ المنتج
                        </button>
                        <button type="button" class="btn-secondary" onclick="closeProductModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Category Modal -->
    <div class="modal" id="categoryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إدارة الأقسام</h3>
                <button class="close-modal" onclick="closeCategoryModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="category-form">
                    <h4>إضافة قسم جديد</h4>
                    <form id="categoryForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="categoryName">اسم القسم</label>
                                <input type="text" id="categoryName" required>
                            </div>
                            <div class="form-group">
                                <label for="categoryNameEn">الاسم بالإنجليزية</label>
                                <input type="text" id="categoryNameEn">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="categoryDescription">الوصف</label>
                            <textarea id="categoryDescription" rows="2"></textarea>
                        </div>
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-plus"></i>
                            إضافة القسم
                        </button>
                    </form>
                </div>

                <div class="categories-list">
                    <h4>الأقسام الموجودة</h4>
                    <div class="categories-grid" id="categoriesGrid">
                        <!-- Categories will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-storage-compat.js"></script>

    <!-- Supabase SDK -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- Application Scripts -->
    <script src="../js/error-handler.js"></script>
    <script src="../js/performance-optimizer.js"></script>
    <script src="../js/firebase-config.js"></script>
    <script src="../js/supabase-config.js"></script>
    <script src="../js/database-sync.js"></script>
    <script src="../js/advanced-image-manager.js"></script>
    <script src="js/admin-auth.js"></script>
    <script src="js/advanced-menu-manager.js"></script>
</body>
</html>
