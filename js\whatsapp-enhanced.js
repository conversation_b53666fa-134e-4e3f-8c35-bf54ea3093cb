// Enhanced WhatsApp Integration System
// نظام تكامل الواتساب المحسن

class WhatsAppManager {
    constructor() {
        this.phoneNumber = '201014840269'; // رقم الواتساب المصري
        this.restaurantName = 'محمد الاشرافي';
        this.restaurantAddress = 'القاهرة، مصر';
        this.restaurantPhone = '+201014840269';
        this.currency = 'جنيه';
        
        this.init();
    }

    init() {
        this.loadSettings();
    }

    // Load restaurant settings
    async loadSettings() {
        try {
            // Load settings from database or localStorage
            const settings = JSON.parse(localStorage.getItem('restaurant_settings') || '{}');
            
            this.restaurantName = settings.restaurantName || this.restaurantName;
            this.restaurantAddress = settings.restaurantAddress || this.restaurantAddress;
            this.restaurantPhone = settings.whatsappNumber || this.restaurantPhone;
            this.phoneNumber = settings.whatsappNumber?.replace(/[^0-9]/g, '') || this.phoneNumber;
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    // Generate professional invoice message
    generateInvoiceMessage(orderData, customerData) {
        const orderNumber = this.generateOrderNumber();
        const orderDate = this.formatDate(new Date());
        const orderTime = this.formatTime(new Date());
        
        let message = '';
        
        // Header with restaurant branding
        message += this.createHeader();
        
        // Order information
        message += this.createOrderInfo(orderNumber, orderDate, orderTime);
        
        // Customer information
        message += this.createCustomerInfo(customerData);
        
        // Order items
        message += this.createOrderItems(orderData.items);
        
        // Order summary
        message += this.createOrderSummary(orderData);
        
        // Special instructions
        if (customerData.notes) {
            message += this.createSpecialInstructions(customerData.notes);
        }
        
        // Footer with contact info
        message += this.createFooter();
        
        return message;
    }

    createHeader() {
        return `🍽️ *${this.restaurantName}* 🍽️
${'═'.repeat(35)}
📋 *فاتورة طلب جديد*
${'═'.repeat(35)}

`;
    }

    createOrderInfo(orderNumber, orderDate, orderTime) {
        return `📊 *معلومات الطلب:*
┌─ رقم الطلب: #${orderNumber}
├─ التاريخ: ${orderDate}
├─ الوقت: ${orderTime}
└─ الحالة: قيد المراجعة ⏳

`;
    }

    createCustomerInfo(customerData) {
        return `👤 *بيانات العميل:*
┌─ الاسم: ${customerData.name}
├─ الهاتف: ${customerData.phone}
└─ العنوان: ${customerData.address}

`;
    }

    createOrderItems(items) {
        let itemsText = `🛒 *تفاصيل الطلب:*
${'─'.repeat(35)}
`;

        let itemNumber = 1;
        items.forEach(item => {
            const itemTotal = item.price * item.quantity;
            
            itemsText += `${itemNumber}. *${item.name}*
   📦 الكمية: ${item.quantity}
   💵 السعر: ${item.price} ${this.currency}
   💰 المجموع: ${itemTotal} ${this.currency}
   ${'─'.repeat(25)}
`;
            itemNumber++;
        });

        return itemsText;
    }

    createOrderSummary(orderData) {
        const subtotal = orderData.subtotal || this.calculateSubtotal(orderData.items);
        const deliveryFee = orderData.deliveryFee || 0;
        const tax = orderData.tax || 0;
        const discount = orderData.discount || 0;
        const total = subtotal + deliveryFee + tax - discount;

        let summaryText = `
💰 *ملخص الفاتورة:*
${'═'.repeat(35)}
`;

        summaryText += `┌─ المجموع الفرعي: ${subtotal} ${this.currency}
`;

        if (deliveryFee > 0) {
            summaryText += `├─ رسوم التوصيل: ${deliveryFee} ${this.currency}
`;
        }

        if (tax > 0) {
            summaryText += `├─ الضريبة: ${tax} ${this.currency}
`;
        }

        if (discount > 0) {
            summaryText += `├─ الخصم: -${discount} ${this.currency}
`;
        }

        summaryText += `└─ *المجموع الكلي: ${total} ${this.currency}* 💳

`;

        return summaryText;
    }

    createSpecialInstructions(notes) {
        return `📝 *ملاحظات خاصة:*
${notes}

`;
    }

    createFooter() {
        const estimatedTime = this.calculateEstimatedDeliveryTime();
        
        return `⏰ *وقت التوصيل المتوقع:*
${estimatedTime}

📞 *للاستفسار والمتابعة:*
┌─ الهاتف: ${this.restaurantPhone}
├─ العنوان: ${this.restaurantAddress}
└─ ساعات العمل: يومياً 9ص - 12م

🙏 *شكراً لاختياركم ${this.restaurantName}*
نتطلع لخدمتكم دائماً! ❤️

${'═'.repeat(35)}
*يرجى الرد على هذه الرسالة لتأكيد الطلب*`;
    }

    // Generate order number
    generateOrderNumber() {
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `${timestamp.slice(-6)}${random}`;
    }

    // Calculate subtotal
    calculateSubtotal(items) {
        return items.reduce((total, item) => total + (item.price * item.quantity), 0);
    }

    // Calculate estimated delivery time
    calculateEstimatedDeliveryTime() {
        const now = new Date();
        const estimatedTime = new Date(now.getTime() + (45 * 60 * 1000)); // 45 minutes
        return this.formatTime(estimatedTime);
    }

    // Format date in Arabic
    formatDate(date) {
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        return date.toLocaleDateString('ar-EG', options);
    }

    // Format time in Arabic
    formatTime(date) {
        const options = {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };
        
        return date.toLocaleTimeString('ar-EG', options);
    }

    // Send order via WhatsApp
    async sendOrder(orderData, customerData) {
        try {
            // Generate the invoice message
            const message = this.generateInvoiceMessage(orderData, customerData);
            
            // Create WhatsApp URL
            const whatsappUrl = this.createWhatsAppUrl(message);
            
            // Save order to database before sending
            await this.saveOrderToDatabase(orderData, customerData, message);
            
            // Open WhatsApp
            window.open(whatsappUrl, '_blank');
            
            // Track analytics
            this.trackOrderSent(orderData);
            
            return {
                success: true,
                message: 'تم إرسال الطلب بنجاح عبر الواتساب',
                orderNumber: this.generateOrderNumber()
            };
            
        } catch (error) {
            console.error('Error sending WhatsApp order:', error);
            return {
                success: false,
                error: 'فشل في إرسال الطلب عبر الواتساب'
            };
        }
    }

    // Create WhatsApp URL
    createWhatsAppUrl(message) {
        const encodedMessage = encodeURIComponent(message);
        return `https://wa.me/${this.phoneNumber}?text=${encodedMessage}`;
    }

    // Save order to database
    async saveOrderToDatabase(orderData, customerData, whatsappMessage) {
        try {
            const order = {
                id: this.generateOrderNumber(),
                customerName: customerData.name,
                customerPhone: customerData.phone,
                customerAddress: customerData.address,
                notes: customerData.notes || '',
                items: orderData.items.map(item => ({
                    id: item.id,
                    name: item.name,
                    price: item.price,
                    quantity: item.quantity
                })),
                subtotal: this.calculateSubtotal(orderData.items),
                deliveryFee: orderData.deliveryFee || 0,
                tax: orderData.tax || 0,
                discount: orderData.discount || 0,
                total: orderData.total || this.calculateSubtotal(orderData.items),
                status: 'pending',
                whatsappMessage: whatsappMessage,
                whatsappSent: true,
                whatsappSentAt: new Date().toISOString(),
                timestamp: new Date().toISOString(),
                source: 'website'
            };

            // Get existing dashboard data
            const dashboardData = JSON.parse(localStorage.getItem('restaurant_dashboard_data') || '{"orders": [], "stats": {}}');
            
            // Add new order
            dashboardData.orders = dashboardData.orders || [];
            dashboardData.orders.push(order);
            
            // Update stats
            dashboardData.stats = dashboardData.stats || {};
            dashboardData.stats.totalOrders = dashboardData.orders.length;
            dashboardData.stats.totalRevenue = dashboardData.orders.reduce((total, order) => total + (order.total || 0), 0);
            dashboardData.stats.totalCustomers = new Set(dashboardData.orders.map(order => order.customerPhone)).size;
            
            // Save back to localStorage
            localStorage.setItem('restaurant_dashboard_data', JSON.stringify(dashboardData));
            
            // Also save to customer data
            this.saveCustomerData(customerData, order);
            
            console.log('Order saved to database:', order);
            
        } catch (error) {
            console.error('Error saving order to database:', error);
        }
    }

    // Save customer data
    saveCustomerData(customerData, order) {
        try {
            const customers = JSON.parse(localStorage.getItem('restaurant_customers') || '[]');
            
            // Find existing customer or create new one
            let customer = customers.find(c => c.phone === customerData.phone);
            
            if (customer) {
                // Update existing customer
                customer.name = customerData.name;
                customer.address = customerData.address;
                customer.totalOrders = (customer.totalOrders || 0) + 1;
                customer.totalSpent = (customer.totalSpent || 0) + order.total;
                customer.lastOrderAt = order.timestamp;
                customer.updatedAt = new Date().toISOString();
            } else {
                // Create new customer
                customer = {
                    id: Date.now().toString(),
                    name: customerData.name,
                    phone: customerData.phone,
                    address: customerData.address,
                    totalOrders: 1,
                    totalSpent: order.total,
                    firstOrderAt: order.timestamp,
                    lastOrderAt: order.timestamp,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                customers.push(customer);
            }
            
            localStorage.setItem('restaurant_customers', JSON.stringify(customers));
            
        } catch (error) {
            console.error('Error saving customer data:', error);
        }
    }

    // Track analytics
    trackOrderSent(orderData) {
        try {
            const analytics = JSON.parse(localStorage.getItem('restaurant_analytics') || '[]');
            
            const event = {
                id: Date.now().toString(),
                type: 'order_sent_whatsapp',
                data: {
                    itemCount: orderData.items.length,
                    totalAmount: orderData.total || this.calculateSubtotal(orderData.items),
                    orderSource: 'website'
                },
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                sessionId: this.getSessionId()
            };
            
            analytics.push(event);
            
            // Keep only last 1000 events
            if (analytics.length > 1000) {
                analytics.splice(0, analytics.length - 1000);
            }
            
            localStorage.setItem('restaurant_analytics', JSON.stringify(analytics));
            
        } catch (error) {
            console.error('Error tracking analytics:', error);
        }
    }

    // Get session ID
    getSessionId() {
        let sessionId = sessionStorage.getItem('session_id');
        if (!sessionId) {
            sessionId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem('session_id', sessionId);
        }
        return sessionId;
    }

    // Generate order preview for customer
    generateOrderPreview(orderData, customerData) {
        const subtotal = this.calculateSubtotal(orderData.items);
        
        let preview = `📋 *معاينة الطلب*\n\n`;
        
        preview += `👤 *العميل:* ${customerData.name}\n`;
        preview += `📍 *العنوان:* ${customerData.address}\n\n`;
        
        preview += `🛒 *الطلبات:*\n`;
        orderData.items.forEach((item, index) => {
            preview += `${index + 1}. ${item.name} × ${item.quantity} = ${item.price * item.quantity} ${this.currency}\n`;
        });
        
        preview += `\n💰 *المجموع:* ${subtotal} ${this.currency}`;
        
        if (customerData.notes) {
            preview += `\n\n📝 *ملاحظات:* ${customerData.notes}`;
        }
        
        return preview;
    }

    // Validate order data
    validateOrderData(orderData, customerData) {
        const errors = [];
        
        if (!customerData.name || customerData.name.trim().length < 2) {
            errors.push('يرجى إدخال اسم صحيح');
        }
        
        if (!customerData.phone || !this.validatePhoneNumber(customerData.phone)) {
            errors.push('يرجى إدخال رقم هاتف صحيح');
        }
        
        if (!customerData.address || customerData.address.trim().length < 5) {
            errors.push('يرجى إدخال عنوان مفصل');
        }
        
        if (!orderData.items || orderData.items.length === 0) {
            errors.push('السلة فارغة');
        }
        
        if (orderData.items && orderData.items.some(item => item.quantity <= 0)) {
            errors.push('كمية غير صحيحة في أحد المنتجات');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // Validate phone number
    validatePhoneNumber(phone) {
        // Egyptian phone number validation
        const phoneRegex = /^(\+20|0)?1[0125]\d{8}$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    }

    // Format phone number
    formatPhoneNumber(phone) {
        const cleaned = phone.replace(/\D/g, '');
        
        if (cleaned.startsWith('20')) {
            return '+' + cleaned;
        } else if (cleaned.startsWith('01')) {
            return '+20' + cleaned.substring(1);
        } else if (cleaned.startsWith('1') && cleaned.length === 10) {
            return '+20' + cleaned;
        }
        
        return phone;
    }

    // Get order status message
    getOrderStatusMessage(status) {
        const statusMessages = {
            'pending': 'قيد المراجعة ⏳',
            'confirmed': 'تم التأكيد ✅',
            'preparing': 'قيد التحضير 👨‍🍳',
            'ready': 'جاهز للتوصيل 🚚',
            'delivered': 'تم التوصيل ✅',
            'cancelled': 'ملغي ❌'
        };
        
        return statusMessages[status] || status;
    }

    // Send status update via WhatsApp
    async sendStatusUpdate(orderNumber, status, customerPhone) {
        try {
            const statusMessage = this.getOrderStatusMessage(status);
            
            let message = `🔔 *تحديث حالة الطلب*\n\n`;
            message += `📋 رقم الطلب: #${orderNumber}\n`;
            message += `📊 الحالة الجديدة: ${statusMessage}\n\n`;
            
            if (status === 'ready') {
                message += `🚚 طلبكم جاهز للتوصيل!\n`;
                message += `⏰ سيصل خلال 15-20 دقيقة\n\n`;
            } else if (status === 'delivered') {
                message += `✅ تم توصيل طلبكم بنجاح!\n`;
                message += `🙏 شكراً لاختياركم ${this.restaurantName}\n\n`;
            }
            
            message += `📞 للاستفسار: ${this.restaurantPhone}`;
            
            const whatsappUrl = `https://wa.me/${customerPhone.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`;
            
            return {
                success: true,
                url: whatsappUrl,
                message: message
            };
            
        } catch (error) {
            console.error('Error creating status update:', error);
            return {
                success: false,
                error: 'فشل في إنشاء رسالة التحديث'
            };
        }
    }
}

// Create global instance
const whatsappManager = new WhatsAppManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WhatsAppManager;
}

// Make available globally
window.whatsappManager = whatsappManager;
