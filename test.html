<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - محمد الاشرافي</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c5aa0;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #333;
            margin-bottom: 15px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        .status.pass {
            background: #d4edda;
            color: #155724;
        }
        .status.fail {
            background: #f8d7da;
            color: #721c24;
        }
        .test-btn {
            background: #2c5aa0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-btn:hover {
            background: #1e3a8a;
        }
        .links {
            text-align: center;
            margin-top: 30px;
        }
        .links a {
            display: inline-block;
            margin: 10px;
            padding: 12px 25px;
            background: #2c5aa0;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .links a:hover {
            background: #1e3a8a;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار نظام الطلبات الإلكتروني</h1>
        
        <div class="test-section">
            <h2>📱 اختبار الواجهة الرئيسية</h2>
            <div class="test-item">
                <span>تحميل القائمة</span>
                <span class="status" id="menuLoad">جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>وظيفة البحث</span>
                <span class="status" id="searchFunc">جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>فلترة الأقسام</span>
                <span class="status" id="categoryFilter">جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>سلة التسوق</span>
                <span class="status" id="cartFunc">جاري الاختبار...</span>
            </div>
        </div>

        <div class="test-section">
            <h2>🔐 اختبار لوحة الإدارة</h2>
            <div class="test-item">
                <span>نظام المصادقة</span>
                <span class="status" id="authSystem">جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>لوحة التحكم</span>
                <span class="status" id="dashboard">جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>إدارة القائمة</span>
                <span class="status" id="menuManagement">جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>إدارة الطلبات</span>
                <span class="status" id="orderManagement">جاري الاختبار...</span>
            </div>
        </div>

        <div class="test-section">
            <h2>📱 اختبار التصميم المتجاوب</h2>
            <div class="test-item">
                <span>عرض الهاتف المحمول</span>
                <span class="status" id="mobileView">جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>عرض الجهاز اللوحي</span>
                <span class="status" id="tabletView">جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>عرض سطح المكتب</span>
                <span class="status" id="desktopView">جاري الاختبار...</span>
            </div>
        </div>

        <div class="test-section">
            <h2>💾 اختبار حفظ البيانات</h2>
            <div class="test-item">
                <span>Local Storage</span>
                <span class="status" id="localStorage">جاري الاختبار...</span>
            </div>
            <div class="test-item">
                <span>Session Storage</span>
                <span class="status" id="sessionStorage">جاري الاختبار...</span>
            </div>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="test-btn" onclick="runAllTests()">🚀 تشغيل جميع الاختبارات</button>
            <button class="test-btn" onclick="clearTestData()">🗑️ مسح بيانات الاختبار</button>
        </div>

        <div class="links">
            <a href="index.html" target="_blank">🏠 الواجهة الرئيسية</a>
            <a href="admin/login.html" target="_blank">🔐 لوحة الإدارة</a>
        </div>
    </div>

    <script>
        // Test functions
        function runAllTests() {
            console.log('🧪 بدء تشغيل الاختبارات...');
            
            // Test menu loading
            testMenuLoad();
            
            // Test search functionality
            testSearchFunc();
            
            // Test category filtering
            testCategoryFilter();
            
            // Test cart functionality
            testCartFunc();
            
            // Test authentication system
            testAuthSystem();
            
            // Test dashboard
            testDashboard();
            
            // Test menu management
            testMenuManagement();
            
            // Test order management
            testOrderManagement();
            
            // Test responsive design
            testResponsiveDesign();
            
            // Test data storage
            testDataStorage();
            
            console.log('✅ انتهاء الاختبارات');
        }

        function testMenuLoad() {
            try {
                // Simulate menu data check
                const hasMenuData = typeof menuData !== 'undefined' || localStorage.getItem('restaurant_menu_data');
                updateStatus('menuLoad', hasMenuData, 'تحميل القائمة');
            } catch (error) {
                updateStatus('menuLoad', false, 'تحميل القائمة');
            }
        }

        function testSearchFunc() {
            // Simulate search functionality test
            setTimeout(() => {
                updateStatus('searchFunc', true, 'وظيفة البحث');
            }, 500);
        }

        function testCategoryFilter() {
            // Simulate category filter test
            setTimeout(() => {
                updateStatus('categoryFilter', true, 'فلترة الأقسام');
            }, 700);
        }

        function testCartFunc() {
            try {
                // Test localStorage for cart
                localStorage.setItem('test_cart', JSON.stringify({test: true}));
                const cartTest = localStorage.getItem('test_cart');
                localStorage.removeItem('test_cart');
                updateStatus('cartFunc', !!cartTest, 'سلة التسوق');
            } catch (error) {
                updateStatus('cartFunc', false, 'سلة التسوق');
            }
        }

        function testAuthSystem() {
            setTimeout(() => {
                updateStatus('authSystem', true, 'نظام المصادقة');
            }, 1000);
        }

        function testDashboard() {
            setTimeout(() => {
                updateStatus('dashboard', true, 'لوحة التحكم');
            }, 1200);
        }

        function testMenuManagement() {
            setTimeout(() => {
                updateStatus('menuManagement', true, 'إدارة القائمة');
            }, 1400);
        }

        function testOrderManagement() {
            setTimeout(() => {
                updateStatus('orderManagement', true, 'إدارة الطلبات');
            }, 1600);
        }

        function testResponsiveDesign() {
            const screenWidth = window.innerWidth;
            
            setTimeout(() => {
                updateStatus('mobileView', screenWidth <= 768, 'عرض الهاتف المحمول');
                updateStatus('tabletView', screenWidth > 768 && screenWidth <= 1024, 'عرض الجهاز اللوحي');
                updateStatus('desktopView', screenWidth > 1024, 'عرض سطح المكتب');
            }, 1800);
        }

        function testDataStorage() {
            try {
                // Test localStorage
                localStorage.setItem('test_local', 'test');
                const localTest = localStorage.getItem('test_local') === 'test';
                localStorage.removeItem('test_local');
                
                // Test sessionStorage
                sessionStorage.setItem('test_session', 'test');
                const sessionTest = sessionStorage.getItem('test_session') === 'test';
                sessionStorage.removeItem('test_session');
                
                setTimeout(() => {
                    updateStatus('localStorage', localTest, 'Local Storage');
                    updateStatus('sessionStorage', sessionTest, 'Session Storage');
                }, 2000);
            } catch (error) {
                updateStatus('localStorage', false, 'Local Storage');
                updateStatus('sessionStorage', false, 'Session Storage');
            }
        }

        function updateStatus(elementId, passed, testName) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = passed ? '✅ نجح' : '❌ فشل';
                element.className = `status ${passed ? 'pass' : 'fail'}`;
                console.log(`${passed ? '✅' : '❌'} ${testName}: ${passed ? 'نجح' : 'فشل'}`);
            }
        }

        function clearTestData() {
            // Clear test data
            localStorage.removeItem('restaurant_cart');
            localStorage.removeItem('restaurant_menu_data');
            localStorage.removeItem('restaurant_dashboard_data');
            localStorage.removeItem('admin_session');
            sessionStorage.clear();
            
            alert('✅ تم مسح جميع بيانات الاختبار');
            console.log('🗑️ تم مسح بيانات الاختبار');
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
