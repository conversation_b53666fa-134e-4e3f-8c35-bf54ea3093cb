#!/usr/bin/env node

// Comprehensive System Testing Script
// سكريپت الاختبار الشامل للنظام

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class SystemTester {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.testResults = {
            passed: 0,
            failed: 0,
            warnings: 0,
            details: []
        };
        
        console.log('🧪 Starting comprehensive system testing...');
    }

    async runAllTests() {
        try {
            console.log('📋 Running file structure tests...');
            await this.testFileStructure();
            
            console.log('🔧 Testing configuration files...');
            await this.testConfiguration();
            
            console.log('📱 Testing HTML structure...');
            await this.testHtmlStructure();
            
            console.log('🎨 Testing CSS and styling...');
            await this.testCssFiles();
            
            console.log('⚡ Testing JavaScript functionality...');
            await this.testJavaScript();
            
            console.log('🔒 Testing security configurations...');
            await this.testSecurity();
            
            console.log('📊 Testing database configurations...');
            await this.testDatabaseConfig();
            
            console.log('🚀 Testing deployment readiness...');
            await this.testDeploymentReadiness();
            
            this.generateTestReport();
            
        } catch (error) {
            console.error('❌ Testing failed:', error.message);
            process.exit(1);
        }
    }

    async testFileStructure() {
        const requiredFiles = [
            'index.html',
            'package.json',
            'firebase.json',
            '.env.example',
            'README.md',
            'css/style.css',
            'css/image-gallery.css',
            'css/advanced-image-manager.css',
            'js/main.js',
            'js/cart.js',
            'js/menu-data.js',
            'js/whatsapp-enhanced.js',
            'js/firebase-config.js',
            'js/supabase-config.js',
            'js/database-sync.js',
            'js/error-handler.js',
            'js/performance-optimizer.js',
            'js/advanced-image-manager.js',
            'admin/login.html',
            'admin/dashboard.html',
            'admin/menu-management.html',
            'admin/css/admin.css',
            'admin/css/menu-management.css',
            'admin/js/admin-auth.js',
            'admin/js/dashboard.js',
            'admin/js/advanced-menu-manager.js',
            'firestore.rules',
            'storage.rules',
            'database-schema.sql'
        ];

        const requiredDirectories = [
            'css',
            'js',
            'admin',
            'admin/css',
            'admin/js',
            'images',
            'scripts'
        ];

        // Test required files
        for (const file of requiredFiles) {
            const filePath = path.join(this.projectRoot, file);
            if (fs.existsSync(filePath)) {
                this.addResult('pass', `File exists: ${file}`);
            } else {
                this.addResult('fail', `Missing required file: ${file}`);
            }
        }

        // Test required directories
        for (const dir of requiredDirectories) {
            const dirPath = path.join(this.projectRoot, dir);
            if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
                this.addResult('pass', `Directory exists: ${dir}`);
            } else {
                this.addResult('fail', `Missing required directory: ${dir}`);
            }
        }
    }

    async testConfiguration() {
        // Test package.json
        try {
            const packageJson = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'package.json'), 'utf8'));
            
            if (packageJson.name && packageJson.version) {
                this.addResult('pass', 'package.json has valid name and version');
            } else {
                this.addResult('fail', 'package.json missing name or version');
            }

            if (packageJson.scripts && packageJson.scripts.deploy) {
                this.addResult('pass', 'Deploy script configured');
            } else {
                this.addResult('warn', 'Deploy script not configured');
            }
        } catch (error) {
            this.addResult('fail', `Invalid package.json: ${error.message}`);
        }

        // Test firebase.json
        try {
            const firebaseConfig = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'firebase.json'), 'utf8'));
            
            if (firebaseConfig.hosting) {
                this.addResult('pass', 'Firebase hosting configured');
            } else {
                this.addResult('fail', 'Firebase hosting not configured');
            }

            if (firebaseConfig.firestore && firebaseConfig.storage) {
                this.addResult('pass', 'Firebase services configured');
            } else {
                this.addResult('warn', 'Some Firebase services not configured');
            }
        } catch (error) {
            this.addResult('fail', `Invalid firebase.json: ${error.message}`);
        }

        // Test .env.example
        const envExample = path.join(this.projectRoot, '.env.example');
        if (fs.existsSync(envExample)) {
            const content = fs.readFileSync(envExample, 'utf8');
            if (content.includes('FIREBASE_API_KEY') && content.includes('SUPABASE_URL')) {
                this.addResult('pass', 'Environment variables template configured');
            } else {
                this.addResult('fail', 'Environment variables template incomplete');
            }
        }
    }

    async testHtmlStructure() {
        const htmlFiles = [
            'index.html',
            'admin/login.html',
            'admin/dashboard.html',
            'admin/menu-management.html'
        ];

        for (const file of htmlFiles) {
            const filePath = path.join(this.projectRoot, file);
            if (!fs.existsSync(filePath)) continue;

            const content = fs.readFileSync(filePath, 'utf8');

            // Test basic HTML structure
            if (content.includes('<!DOCTYPE html>')) {
                this.addResult('pass', `${file}: Valid DOCTYPE`);
            } else {
                this.addResult('fail', `${file}: Missing DOCTYPE`);
            }

            if (content.includes('<html lang="ar"')) {
                this.addResult('pass', `${file}: Arabic language set`);
            } else {
                this.addResult('warn', `${file}: Language not set to Arabic`);
            }

            if (content.includes('dir="rtl"')) {
                this.addResult('pass', `${file}: RTL direction set`);
            } else {
                this.addResult('warn', `${file}: RTL direction not set`);
            }

            // Test meta tags
            if (content.includes('<meta charset="UTF-8">')) {
                this.addResult('pass', `${file}: UTF-8 charset set`);
            } else {
                this.addResult('fail', `${file}: Missing UTF-8 charset`);
            }

            if (content.includes('viewport')) {
                this.addResult('pass', `${file}: Viewport meta tag present`);
            } else {
                this.addResult('fail', `${file}: Missing viewport meta tag`);
            }

            // Test CSS and JS includes
            if (content.includes('style.css')) {
                this.addResult('pass', `${file}: Main CSS included`);
            } else {
                this.addResult('fail', `${file}: Main CSS not included`);
            }

            if (content.includes('Font Awesome')) {
                this.addResult('pass', `${file}: Font Awesome included`);
            } else {
                this.addResult('warn', `${file}: Font Awesome not included`);
            }
        }
    }

    async testCssFiles() {
        const cssFiles = [
            'css/style.css',
            'css/image-gallery.css',
            'css/advanced-image-manager.css',
            'admin/css/admin.css',
            'admin/css/menu-management.css'
        ];

        for (const file of cssFiles) {
            const filePath = path.join(this.projectRoot, file);
            if (!fs.existsSync(filePath)) {
                this.addResult('fail', `Missing CSS file: ${file}`);
                continue;
            }

            const content = fs.readFileSync(filePath, 'utf8');

            // Test for responsive design
            if (content.includes('@media')) {
                this.addResult('pass', `${file}: Responsive design implemented`);
            } else {
                this.addResult('warn', `${file}: No responsive design found`);
            }

            // Test for RTL support
            if (content.includes('direction: rtl') || content.includes('text-align: right')) {
                this.addResult('pass', `${file}: RTL support implemented`);
            } else {
                this.addResult('warn', `${file}: Limited RTL support`);
            }

            // Test file size
            const stats = fs.statSync(filePath);
            if (stats.size > 100 * 1024) { // 100KB
                this.addResult('warn', `${file}: Large file size (${Math.round(stats.size / 1024)}KB)`);
            } else {
                this.addResult('pass', `${file}: Reasonable file size`);
            }
        }
    }

    async testJavaScript() {
        const jsFiles = [
            'js/main.js',
            'js/cart.js',
            'js/menu-data.js',
            'js/whatsapp-enhanced.js',
            'js/firebase-config.js',
            'js/supabase-config.js',
            'js/database-sync.js',
            'js/error-handler.js',
            'js/performance-optimizer.js',
            'js/advanced-image-manager.js',
            'admin/js/admin-auth.js',
            'admin/js/dashboard.js',
            'admin/js/advanced-menu-manager.js'
        ];

        for (const file of jsFiles) {
            const filePath = path.join(this.projectRoot, file);
            if (!fs.existsSync(filePath)) {
                this.addResult('fail', `Missing JavaScript file: ${file}`);
                continue;
            }

            const content = fs.readFileSync(filePath, 'utf8');

            // Test for basic syntax (simple check)
            const openBraces = (content.match(/{/g) || []).length;
            const closeBraces = (content.match(/}/g) || []).length;
            
            if (openBraces === closeBraces) {
                this.addResult('pass', `${file}: Balanced braces`);
            } else {
                this.addResult('fail', `${file}: Unbalanced braces`);
            }

            // Test for error handling
            if (content.includes('try') && content.includes('catch')) {
                this.addResult('pass', `${file}: Error handling implemented`);
            } else {
                this.addResult('warn', `${file}: Limited error handling`);
            }

            // Test for modern JavaScript features
            if (content.includes('async') && content.includes('await')) {
                this.addResult('pass', `${file}: Modern async/await used`);
            } else {
                this.addResult('warn', `${file}: No async/await found`);
            }

            // Test for console.log (should be minimal in production)
            const consoleLogs = (content.match(/console\.log/g) || []).length;
            if (consoleLogs > 5) {
                this.addResult('warn', `${file}: Many console.log statements (${consoleLogs})`);
            } else {
                this.addResult('pass', `${file}: Reasonable console.log usage`);
            }
        }
    }

    async testSecurity() {
        // Test firestore.rules
        const firestoreRules = path.join(this.projectRoot, 'firestore.rules');
        if (fs.existsSync(firestoreRules)) {
            const content = fs.readFileSync(firestoreRules, 'utf8');
            
            if (content.includes('rules_version = \'2\'')) {
                this.addResult('pass', 'Firestore rules version 2');
            } else {
                this.addResult('fail', 'Firestore rules not version 2');
            }

            if (content.includes('isAuthenticated()') || content.includes('request.auth')) {
                this.addResult('pass', 'Authentication checks in Firestore rules');
            } else {
                this.addResult('fail', 'No authentication checks in Firestore rules');
            }
        } else {
            this.addResult('fail', 'Missing firestore.rules file');
        }

        // Test storage.rules
        const storageRules = path.join(this.projectRoot, 'storage.rules');
        if (fs.existsSync(storageRules)) {
            const content = fs.readFileSync(storageRules, 'utf8');
            
            if (content.includes('request.auth')) {
                this.addResult('pass', 'Authentication checks in Storage rules');
            } else {
                this.addResult('fail', 'No authentication checks in Storage rules');
            }

            if (content.includes('resource.size') && content.includes('resource.contentType')) {
                this.addResult('pass', 'File validation in Storage rules');
            } else {
                this.addResult('warn', 'Limited file validation in Storage rules');
            }
        } else {
            this.addResult('fail', 'Missing storage.rules file');
        }

        // Check for sensitive data in files
        const sensitivePatterns = [
            /password\s*[:=]\s*['"][^'"]+['"]/gi,
            /api[_-]?key\s*[:=]\s*['"][a-zA-Z0-9]{20,}['"]/gi,
            /secret\s*[:=]\s*['"][^'"]+['"]/gi
        ];

        const jsFiles = this.findFiles(this.projectRoot, '.js');
        let sensitiveDataFound = false;

        for (const file of jsFiles) {
            const content = fs.readFileSync(file, 'utf8');
            
            for (const pattern of sensitivePatterns) {
                if (pattern.test(content)) {
                    this.addResult('fail', `Potential sensitive data in ${path.relative(this.projectRoot, file)}`);
                    sensitiveDataFound = true;
                }
            }
        }

        if (!sensitiveDataFound) {
            this.addResult('pass', 'No sensitive data found in source files');
        }
    }

    async testDatabaseConfig() {
        // Test Firebase config
        const firebaseConfig = path.join(this.projectRoot, 'js/firebase-config.js');
        if (fs.existsSync(firebaseConfig)) {
            const content = fs.readFileSync(firebaseConfig, 'utf8');
            
            if (content.includes('firebaseConfig') && content.includes('apiKey')) {
                this.addResult('pass', 'Firebase configuration structure present');
            } else {
                this.addResult('fail', 'Firebase configuration incomplete');
            }

            if (content.includes('initializeApp')) {
                this.addResult('pass', 'Firebase initialization code present');
            } else {
                this.addResult('fail', 'Firebase initialization missing');
            }
        }

        // Test Supabase config
        const supabaseConfig = path.join(this.projectRoot, 'js/supabase-config.js');
        if (fs.existsSync(supabaseConfig)) {
            const content = fs.readFileSync(supabaseConfig, 'utf8');
            
            if (content.includes('supabaseConfig') && content.includes('url')) {
                this.addResult('pass', 'Supabase configuration structure present');
            } else {
                this.addResult('fail', 'Supabase configuration incomplete');
            }

            if (content.includes('createClient')) {
                this.addResult('pass', 'Supabase client initialization present');
            } else {
                this.addResult('fail', 'Supabase client initialization missing');
            }
        }

        // Test database schema
        const schema = path.join(this.projectRoot, 'database-schema.sql');
        if (fs.existsSync(schema)) {
            const content = fs.readFileSync(schema, 'utf8');
            
            const requiredTables = ['products', 'categories', 'orders', 'order_items', 'product_images'];
            let tablesFound = 0;
            
            for (const table of requiredTables) {
                if (content.includes(`CREATE TABLE ${table}`) || content.includes(`create table ${table}`)) {
                    tablesFound++;
                }
            }
            
            if (tablesFound === requiredTables.length) {
                this.addResult('pass', 'All required database tables defined');
            } else {
                this.addResult('fail', `Missing database tables (${tablesFound}/${requiredTables.length})`);
            }
        } else {
            this.addResult('fail', 'Missing database schema file');
        }
    }

    async testDeploymentReadiness() {
        // Test if Firebase CLI is available
        try {
            execSync('firebase --version', { stdio: 'ignore' });
            this.addResult('pass', 'Firebase CLI available');
        } catch (error) {
            this.addResult('fail', 'Firebase CLI not installed');
        }

        // Test firebase.json configuration
        try {
            const firebaseConfig = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'firebase.json'), 'utf8'));
            
            if (firebaseConfig.hosting && firebaseConfig.hosting.public) {
                this.addResult('pass', 'Firebase hosting public directory configured');
            } else {
                this.addResult('fail', 'Firebase hosting public directory not configured');
            }

            if (firebaseConfig.hosting.rewrites) {
                this.addResult('pass', 'Firebase hosting rewrites configured');
            } else {
                this.addResult('warn', 'No Firebase hosting rewrites configured');
            }

            if (firebaseConfig.hosting.headers) {
                this.addResult('pass', 'Firebase hosting headers configured');
            } else {
                this.addResult('warn', 'No Firebase hosting headers configured');
            }
        } catch (error) {
            this.addResult('fail', 'Invalid firebase.json configuration');
        }

        // Test package.json scripts
        try {
            const packageJson = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'package.json'), 'utf8'));
            
            if (packageJson.scripts) {
                const requiredScripts = ['deploy', 'build', 'test'];
                let scriptsFound = 0;
                
                for (const script of requiredScripts) {
                    if (packageJson.scripts[script]) {
                        scriptsFound++;
                    }
                }
                
                if (scriptsFound >= 2) {
                    this.addResult('pass', 'Deployment scripts configured');
                } else {
                    this.addResult('warn', 'Limited deployment scripts');
                }
            }
        } catch (error) {
            this.addResult('fail', 'Cannot read package.json scripts');
        }

        // Test file sizes for deployment
        const totalSize = this.calculateProjectSize();
        if (totalSize < 100 * 1024 * 1024) { // 100MB
            this.addResult('pass', `Project size acceptable (${Math.round(totalSize / 1024 / 1024)}MB)`);
        } else {
            this.addResult('warn', `Large project size (${Math.round(totalSize / 1024 / 1024)}MB)`);
        }
    }

    findFiles(dir, extension) {
        const files = [];
        
        function traverse(currentDir) {
            try {
                const items = fs.readdirSync(currentDir);
                
                for (const item of items) {
                    const fullPath = path.join(currentDir, item);
                    
                    // Skip node_modules and other unnecessary directories
                    if (item === 'node_modules' || item === '.git' || item === 'dist') {
                        continue;
                    }
                    
                    const stat = fs.statSync(fullPath);
                    
                    if (stat.isDirectory()) {
                        traverse(fullPath);
                    } else if (item.endsWith(extension)) {
                        files.push(fullPath);
                    }
                }
            } catch (error) {
                // Skip directories we can't read
            }
        }
        
        traverse(dir);
        return files;
    }

    calculateProjectSize() {
        let totalSize = 0;
        
        function calculateSize(dir) {
            try {
                const items = fs.readdirSync(dir);
                
                for (const item of items) {
                    if (item === 'node_modules' || item === '.git' || item === 'dist') {
                        continue;
                    }
                    
                    const fullPath = path.join(dir, item);
                    const stat = fs.statSync(fullPath);
                    
                    if (stat.isDirectory()) {
                        calculateSize(fullPath);
                    } else {
                        totalSize += stat.size;
                    }
                }
            } catch (error) {
                // Skip directories we can't read
            }
        }
        
        calculateSize(this.projectRoot);
        return totalSize;
    }

    addResult(type, message) {
        this.testResults.details.push({ type, message });
        
        switch (type) {
            case 'pass':
                this.testResults.passed++;
                console.log(`✅ ${message}`);
                break;
            case 'fail':
                this.testResults.failed++;
                console.log(`❌ ${message}`);
                break;
            case 'warn':
                this.testResults.warnings++;
                console.log(`⚠️ ${message}`);
                break;
        }
    }

    generateTestReport() {
        const total = this.testResults.passed + this.testResults.failed + this.testResults.warnings;
        const passRate = ((this.testResults.passed / total) * 100).toFixed(1);
        
        console.log('\n📊 Test Results Summary:');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        console.log(`✅ Passed: ${this.testResults.passed}`);
        console.log(`❌ Failed: ${this.testResults.failed}`);
        console.log(`⚠️ Warnings: ${this.testResults.warnings}`);
        console.log(`📈 Pass Rate: ${passRate}%`);
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        
        // Save detailed report
        const report = {
            timestamp: new Date().toISOString(),
            summary: this.testResults,
            passRate: parseFloat(passRate),
            details: this.testResults.details
        };
        
        const reportsDir = path.join(this.projectRoot, 'reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }
        
        fs.writeFileSync(
            path.join(reportsDir, `test-report-${Date.now()}.json`),
            JSON.stringify(report, null, 2)
        );
        
        if (this.testResults.failed > 0) {
            console.log('\n❌ Some tests failed. Please fix the issues before deployment.');
            process.exit(1);
        } else {
            console.log('\n🎉 All tests passed! System is ready for deployment.');
        }
    }
}

// Run tests if this script is executed directly
if (require.main === module) {
    const tester = new SystemTester();
    tester.runAllTests().catch(error => {
        console.error('💥 Testing failed:', error.message);
        process.exit(1);
    });
}

module.exports = SystemTester;
