<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات المتقدمة - مطعم محمد الاشرافي</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #2c3e50;
            line-height: 1.6;
        }

        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .statistics-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.products { background: linear-gradient(45deg, #3498db, #2980b9); }
        .stat-icon.categories { background: linear-gradient(45deg, #27ae60, #229954); }
        .stat-icon.featured { background: linear-gradient(45deg, #f39c12, #e67e22); }
        .stat-icon.revenue { background: linear-gradient(45deg, #e74c3c, #c0392b); }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .filters-container {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .filters-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
        }

        .search-box {
            position: relative;
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
        }

        .search-box input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .search-box input:focus {
            outline: none;
            border-color: #3498db;
        }

        .filter-select {
            padding: 12px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .filter-select:focus {
            outline: none;
            border-color: #3498db;
        }

        .actions-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .bulk-actions {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .bulk-actions input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .main-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-primary { background: linear-gradient(45deg, #3498db, #2980b9); }
        .btn-success { background: linear-gradient(45deg, #27ae60, #229954); }
        .btn-warning { background: linear-gradient(45deg, #f39c12, #e67e22); }
        .btn-danger { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        .btn-info { background: linear-gradient(45deg, #17a2b8, #138496); }
        .btn-secondary { background: linear-gradient(45deg, #6c757d, #5a6268); }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .product-card.unavailable {
            opacity: 0.7;
            filter: grayscale(30%);
        }

        .product-card-header {
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 2;
        }

        .product-select {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        .product-actions-menu {
            position: relative;
        }

        .actions-toggle {
            background: rgba(255,255,255,0.9);
            border: none;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .actions-toggle:hover {
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .actions-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            padding: 10px 0;
            min-width: 180px;
            display: none;
            z-index: 10;
        }

        .actions-dropdown button {
            width: 100%;
            padding: 10px 15px;
            border: none;
            background: none;
            text-align: right;
            cursor: pointer;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .actions-dropdown button:hover {
            background: #f8f9fa;
        }

        .actions-dropdown hr {
            margin: 5px 0;
            border: none;
            border-top: 1px solid #ecf0f1;
        }

        .product-image-container {
            position: relative;
            height: 200px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .product-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }

        .product-icon-fallback {
            color: white;
            font-size: 3rem;
        }

        .product-badges {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
            text-align: center;
        }

        .badge-featured { background: #ffd700; color: #333; }
        .badge-new { background: #27ae60; color: white; }
        .badge-popular { background: #e74c3c; color: white; }
        .badge-discount { background: #f39c12; color: white; }
        .badge-vegetarian { background: #27ae60; color: white; }
        .badge-spicy { background: #e74c3c; color: white; }

        .product-info {
            padding: 20px;
        }

        .product-category {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .product-name {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .product-description {
            color: #7f8c8d;
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .product-price {
            margin-bottom: 15px;
        }

        .current-price {
            font-size: 1.4rem;
            font-weight: bold;
            color: #27ae60;
        }

        .original-price {
            font-size: 1rem;
            color: #95a5a6;
            text-decoration: line-through;
            margin-right: 10px;
        }

        .discount-badge {
            background: #e74c3c;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: bold;
        }

        .product-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.8rem;
            color: #7f8c8d;
        }

        .product-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 15px;
        }

        .tag {
            background: #ecf0f1;
            color: #2c3e50;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .product-card-footer {
            padding: 0 20px 20px;
        }

        .quick-actions {
            display: flex;
            gap: 8px;
        }

        .quick-actions .btn {
            flex: 1;
            justify-content: center;
        }

        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: #7f8c8d;
        }

        .empty-icon {
            font-size: 4rem;
            color: #bdc3c7;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 30px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .pagination button:hover {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination button.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .filters-row {
                grid-template-columns: 1fr;
            }
            
            .actions-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .products-grid {
                grid-template-columns: 1fr;
            }
            
            .page-header h1 {
                font-size: 2rem;
            }
        }

        /* Animation for sortable */
        .sortable-ghost {
            opacity: 0.4;
        }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #3498db;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1><i class="fas fa-utensils"></i> إدارة المنتجات المتقدمة</h1>
            <p>نظام شامل لإدارة منتجات المطعم مع الصور والتصنيفات والإحصائيات</p>
        </div>

        <!-- Statistics -->
        <div class="statistics-row" id="statisticsContainer">
            <div class="stat-card">
                <div class="stat-icon products">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="stat-number" id="totalProducts">0</div>
                <div class="stat-label">إجمالي المنتجات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon categories">
                    <i class="fas fa-list"></i>
                </div>
                <div class="stat-number" id="totalCategories">0</div>
                <div class="stat-label">الفئات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon featured">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-number" id="featuredProducts">0</div>
                <div class="stat-label">منتجات مميزة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon revenue">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-number" id="averagePrice">0</div>
                <div class="stat-label">متوسط السعر</div>
            </div>
        </div>

        <!-- Filters and Controls -->
        <div class="filters-container" id="filtersContainer">
            <!-- Filters will be rendered here -->
        </div>

        <!-- Products Grid -->
        <div class="products-grid" id="productsGrid">
            <!-- Products will be rendered here -->
        </div>

        <!-- Pagination -->
        <div class="pagination" id="paginationContainer">
            <!-- Pagination will be rendered here -->
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Simple Auth Check
        function checkAuth() {
            const session = localStorage.getItem('admin_logged_in');
            if (!session) {
                window.location.replace('login.html');
                return false;
            }
            return true;
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkAuth()) return;
            
            // Show loading state
            document.body.classList.add('loading');
            
            // Remove loading state after initialization
            setTimeout(() => {
                document.body.classList.remove('loading');
            }, 2000);
        });
    </script>

    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- Application Scripts -->
    <script src="../js/supabase-config.js"></script>
    <script src="../js/supabase-storage.js"></script>
    <script src="js/supabase-image-manager.js"></script>
    <script src="js/advanced-product-manager.js"></script>
</body>
</html>
