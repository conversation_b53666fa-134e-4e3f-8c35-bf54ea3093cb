// Shopping Cart Management
class ShoppingCart {
    constructor() {
        this.items = [];
        this.loadFromStorage();
        this.bindEvents();
    }

    // Load cart from localStorage
    loadFromStorage() {
        try {
            const savedCart = localStorage.getItem('restaurant_cart');
            if (savedCart) {
                this.items = JSON.parse(savedCart);
            }
        } catch (error) {
            console.error('Error loading cart from storage:', error);
            this.items = [];
        }
    }

    // Save cart to localStorage
    saveToStorage() {
        try {
            localStorage.setItem('restaurant_cart', JSON.stringify(this.items));
        } catch (error) {
            console.error('Error saving cart to storage:', error);
        }
    }

    // Add item to cart
    addItem(itemId, quantity = 1) {
        const menuItem = getItemById(itemId);
        if (!menuItem || !menuItem.available) {
            this.showMessage('هذا المنتج غير متوفر حالياً', 'error');
            return false;
        }

        const existingItem = this.items.find(item => item.id === itemId);
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            this.items.push({
                id: itemId,
                name: menuItem.name,
                price: menuItem.price,
                quantity: quantity,
                icon: menuItem.icon
            });
        }

        this.saveToStorage();
        this.updateCartDisplay();
        this.showMessage(`تم إضافة ${menuItem.name} إلى السلة`, 'success');
        return true;
    }

    // Remove item from cart
    removeItem(itemId) {
        const itemIndex = this.items.findIndex(item => item.id === itemId);
        if (itemIndex > -1) {
            const removedItem = this.items[itemIndex];
            this.items.splice(itemIndex, 1);
            this.saveToStorage();
            this.updateCartDisplay();
            this.showMessage(`تم حذف ${removedItem.name} من السلة`, 'success');
        }
    }

    // Update item quantity
    updateQuantity(itemId, newQuantity) {
        const item = this.items.find(item => item.id === itemId);
        if (item) {
            if (newQuantity <= 0) {
                this.removeItem(itemId);
            } else {
                item.quantity = newQuantity;
                this.saveToStorage();
                this.updateCartDisplay();
            }
        }
    }

    // Get cart total
    getTotal() {
        return this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
    }

    // Get cart item count
    getItemCount() {
        return this.items.reduce((count, item) => count + item.quantity, 0);
    }

    // Clear cart
    clear() {
        this.items = [];
        this.saveToStorage();
        this.updateCartDisplay();
    }

    // Check if cart is empty
    isEmpty() {
        return this.items.length === 0;
    }

    // Update cart display
    updateCartDisplay() {
        this.updateCartCount();
        this.updateCartSidebar();
        this.updateCheckoutButton();
    }

    // Update cart count in header
    updateCartCount() {
        const cartCountElement = document.getElementById('cartCount');
        if (cartCountElement) {
            const count = this.getItemCount();
            cartCountElement.textContent = count;
            cartCountElement.style.display = count > 0 ? 'flex' : 'none';
        }
    }

    // Update cart sidebar content
    updateCartSidebar() {
        const cartItemsContainer = document.getElementById('cartItems');
        const cartTotalElement = document.getElementById('cartTotal');

        if (!cartItemsContainer || !cartTotalElement) return;

        if (this.isEmpty()) {
            cartItemsContainer.innerHTML = `
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <p>السلة فارغة</p>
                    <button onclick="cart.closeSidebar()">تصفح القائمة</button>
                </div>
            `;
        } else {
            cartItemsContainer.innerHTML = this.items.map(item => `
                <div class="cart-item" data-id="${item.id}">
                    <div class="cart-item-info">
                        <h5><i class="${item.icon}"></i> ${item.name}</h5>
                        <p>${item.price} جنيه × ${item.quantity}</p>
                    </div>
                    <div class="cart-item-controls">
                        <button class="quantity-btn" onclick="cart.updateQuantity('${item.id}', ${item.quantity - 1})">
                            <i class="fas fa-minus"></i>
                        </button>
                        <span class="quantity">${item.quantity}</span>
                        <button class="quantity-btn" onclick="cart.updateQuantity('${item.id}', ${item.quantity + 1})">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="quantity-btn" onclick="cart.removeItem('${item.id}')" style="margin-right: 10px; color: #ff4757;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        cartTotalElement.textContent = this.getTotal();
    }

    // Update checkout button state
    updateCheckoutButton() {
        const checkoutBtn = document.getElementById('checkoutBtn');
        if (checkoutBtn) {
            checkoutBtn.disabled = this.isEmpty();
        }
    }

    // Open cart sidebar
    openSidebar() {
        const cartSidebar = document.getElementById('cartSidebar');
        const overlay = document.getElementById('overlay');
        
        if (cartSidebar && overlay) {
            cartSidebar.classList.add('open');
            overlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }

    // Close cart sidebar
    closeSidebar() {
        const cartSidebar = document.getElementById('cartSidebar');
        const overlay = document.getElementById('overlay');
        
        if (cartSidebar && overlay) {
            cartSidebar.classList.remove('open');
            overlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    }

    // Show order modal
    showOrderModal() {
        if (this.isEmpty()) {
            this.showMessage('السلة فارغة! أضف بعض المنتجات أولاً', 'error');
            return;
        }

        const modal = document.getElementById('orderModal');
        const overlay = document.getElementById('overlay');
        
        if (modal && overlay) {
            this.updateOrderSummary();
            modal.classList.add('show');
            overlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }

    // Close order modal
    closeOrderModal() {
        const modal = document.getElementById('orderModal');
        const overlay = document.getElementById('overlay');
        
        if (modal && overlay) {
            modal.classList.remove('show');
            overlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    }

    // Update order summary in modal
    updateOrderSummary() {
        const orderSummaryElement = document.getElementById('orderSummary');
        const finalTotalElement = document.getElementById('finalTotal');

        if (!orderSummaryElement || !finalTotalElement) return;

        orderSummaryElement.innerHTML = this.items.map(item => `
            <div class="summary-item">
                <span>${item.name} × ${item.quantity}</span>
                <span>${item.price * item.quantity} جنيه</span>
            </div>
        `).join('');

        finalTotalElement.textContent = this.getTotal();
    }

    // Submit order via WhatsApp
    async submitOrder(customerData) {
        if (this.isEmpty()) {
            this.showMessage('السلة فارغة!', 'error');
            return false;
        }

        // Validate order data using enhanced WhatsApp manager
        const validation = window.whatsappManager.validateOrderData(
            { items: this.items, total: this.getTotal() },
            customerData
        );

        if (!validation.isValid) {
            this.showMessage(validation.errors.join('\n'), 'error');
            return false;
        }

        try {
            // Show loading state
            this.showLoadingState(true);

            // Prepare order data
            const orderData = {
                items: this.items,
                subtotal: this.getTotal(),
                deliveryFee: 0,
                tax: 0,
                discount: 0,
                total: this.getTotal()
            };

            // Send order via enhanced WhatsApp manager
            const result = await window.whatsappManager.sendOrder(orderData, customerData);

            if (result.success) {
                // Show success message with order number
                this.showMessage(`تم إرسال الطلب بنجاح! رقم الطلب: #${result.orderNumber}`, 'success');

                // Track successful order
                this.trackOrderSuccess(orderData, customerData);

                // Clear cart and close modal
                this.clear();
                this.closeOrderModal();
                this.closeSidebar();

                // Show order confirmation
                this.showOrderConfirmation(result.orderNumber);

                return true;
            } else {
                throw new Error(result.error);
            }

        } catch (error) {
            console.error('Error submitting order:', error);
            this.showMessage('فشل في إرسال الطلب. يرجى المحاولة مرة أخرى.', 'error');
            return false;
        } finally {
            this.showLoadingState(false);
        }
    }

    // Save order to admin dashboard
    saveOrderToAdmin(customerData) {
        try {
            // Generate order ID
            const orderId = this.generateOrderId();

            // Create order object
            const order = {
                id: orderId,
                customerName: customerData.name,
                customerPhone: customerData.phone,
                customerAddress: customerData.address,
                notes: customerData.notes || '',
                items: this.items.map(item => ({
                    id: item.id,
                    name: item.name,
                    price: item.price,
                    quantity: item.quantity
                })),
                total: this.getTotal(),
                status: 'pending',
                timestamp: new Date().toISOString(),
                source: 'website'
            };

            // Get existing dashboard data
            const dashboardData = JSON.parse(localStorage.getItem('restaurant_dashboard_data') || '{"orders": [], "stats": {}}');

            // Add new order
            dashboardData.orders = dashboardData.orders || [];
            dashboardData.orders.push(order);

            // Update stats
            dashboardData.stats = dashboardData.stats || {};
            dashboardData.stats.totalOrders = dashboardData.orders.length;
            dashboardData.stats.totalRevenue = dashboardData.orders.reduce((total, order) => total + (order.total || 0), 0);
            dashboardData.stats.totalCustomers = new Set(dashboardData.orders.map(order => order.customerPhone)).size;

            // Save back to localStorage
            localStorage.setItem('restaurant_dashboard_data', JSON.stringify(dashboardData));

            console.log('Order saved to admin dashboard:', order);
        } catch (error) {
            console.error('Error saving order to admin dashboard:', error);
        }
    }

    // Generate unique order ID
    generateOrderId() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000);
        return `ORD${timestamp}${random}`.slice(-10);
    }

    // Show loading state
    showLoadingState(show) {
        const submitBtn = document.querySelector('.submit-order-btn');
        const modal = document.getElementById('orderModal');

        if (show) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = `
                <i class="fas fa-spinner fa-spin"></i>
                جاري الإرسال...
            `;
            modal.style.pointerEvents = 'none';
        } else {
            submitBtn.disabled = false;
            submitBtn.innerHTML = `
                <i class="fas fa-paper-plane"></i>
                إرسال الطلب
            `;
            modal.style.pointerEvents = 'auto';
        }
    }

    // Track successful order
    trackOrderSuccess(orderData, customerData) {
        try {
            // Track analytics
            const analytics = JSON.parse(localStorage.getItem('restaurant_analytics') || '[]');

            analytics.push({
                id: Date.now().toString(),
                type: 'order_completed',
                data: {
                    itemCount: orderData.items.length,
                    totalAmount: orderData.total,
                    customerPhone: customerData.phone,
                    orderSource: 'website'
                },
                timestamp: new Date().toISOString()
            });

            localStorage.setItem('restaurant_analytics', JSON.stringify(analytics));

            // Update customer preferences
            this.updateCustomerPreferences(customerData, orderData.items);

        } catch (error) {
            console.error('Error tracking order success:', error);
        }
    }

    // Update customer preferences
    updateCustomerPreferences(customerData, items) {
        try {
            const preferences = JSON.parse(localStorage.getItem('customer_preferences') || '{}');
            const phone = customerData.phone;

            if (!preferences[phone]) {
                preferences[phone] = {
                    name: customerData.name,
                    address: customerData.address,
                    favoriteItems: {},
                    orderHistory: []
                };
            }

            // Update favorite items
            items.forEach(item => {
                if (!preferences[phone].favoriteItems[item.id]) {
                    preferences[phone].favoriteItems[item.id] = 0;
                }
                preferences[phone].favoriteItems[item.id] += item.quantity;
            });

            // Add to order history
            preferences[phone].orderHistory.push({
                date: new Date().toISOString(),
                items: items.map(item => ({ id: item.id, name: item.name, quantity: item.quantity })),
                total: this.getTotal()
            });

            // Keep only last 10 orders
            if (preferences[phone].orderHistory.length > 10) {
                preferences[phone].orderHistory = preferences[phone].orderHistory.slice(-10);
            }

            localStorage.setItem('customer_preferences', JSON.stringify(preferences));

        } catch (error) {
            console.error('Error updating customer preferences:', error);
        }
    }

    // Show order confirmation
    showOrderConfirmation(orderNumber) {
        const confirmationModal = document.createElement('div');
        confirmationModal.className = 'modal show';
        confirmationModal.innerHTML = `
            <div class="modal-content" style="max-width: 500px; text-align: center;">
                <div class="modal-header" style="background: linear-gradient(135deg, #28a745, #20c997); color: white;">
                    <h3>تم إرسال الطلب بنجاح!</h3>
                </div>
                <div class="modal-body" style="padding: 30px;">
                    <div style="font-size: 4rem; color: #28a745; margin-bottom: 20px;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h4 style="color: #333; margin-bottom: 15px;">رقم الطلب: #${orderNumber}</h4>
                    <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
                        تم إرسال طلبكم بنجاح عبر الواتساب. سيتم التواصل معكم قريباً لتأكيد الطلب وتحديد موعد التوصيل.
                    </p>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h5 style="color: #2c5aa0; margin-bottom: 10px;">
                            <i class="fas fa-clock"></i>
                            وقت التوصيل المتوقع
                        </h5>
                        <p style="margin: 0; font-weight: 600; color: #333;">30-45 دقيقة</p>
                    </div>
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button class="btn-primary" onclick="this.closest('.modal').remove()">
                            <i class="fas fa-check"></i>
                            حسناً
                        </button>
                        <a href="tel:+201014840269" class="btn-secondary" style="text-decoration: none;">
                            <i class="fas fa-phone"></i>
                            اتصال مباشر
                        </a>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(confirmationModal);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (confirmationModal.parentNode) {
                confirmationModal.remove();
            }
        }, 10000);
    }

    // Get customer suggestions based on previous orders
    getCustomerSuggestions(phone) {
        try {
            const preferences = JSON.parse(localStorage.getItem('customer_preferences') || '{}');

            if (preferences[phone]) {
                const customer = preferences[phone];

                // Get most ordered items
                const favoriteItems = Object.entries(customer.favoriteItems)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 3)
                    .map(([itemId]) => itemId);

                return {
                    name: customer.name,
                    address: customer.address,
                    favoriteItems: favoriteItems,
                    lastOrder: customer.orderHistory[customer.orderHistory.length - 1]
                };
            }

            return null;
        } catch (error) {
            console.error('Error getting customer suggestions:', error);
            return null;
        }
    }

    // Auto-fill customer data if available
    autoFillCustomerData(phone) {
        const suggestions = this.getCustomerSuggestions(phone);

        if (suggestions) {
            const nameInput = document.getElementById('customerName');
            const addressInput = document.getElementById('customerAddress');

            if (nameInput && !nameInput.value) {
                nameInput.value = suggestions.name;
            }

            if (addressInput && !addressInput.value) {
                addressInput.value = suggestions.address;
            }

            // Show suggestion message
            this.showMessage(`تم العثور على بيانات سابقة للعميل ${suggestions.name}`, 'info');
        }
    }

    // Handle phone input with auto-suggestions
    handlePhoneInput(phone) {
        // Format phone number as user types
        const formatted = this.formatPhoneInput(phone);
        const phoneInput = document.getElementById('customerPhone');

        if (phoneInput.value !== formatted) {
            phoneInput.value = formatted;
        }

        // Auto-fill if phone number is complete
        if (phone.length >= 11) {
            this.autoFillCustomerData(phone);
        }
    }

    // Format phone input
    formatPhoneInput(phone) {
        // Remove all non-digits
        const digits = phone.replace(/\D/g, '');

        // Format Egyptian phone number
        if (digits.length <= 11) {
            if (digits.startsWith('01')) {
                return digits.replace(/(\d{2})(\d{3})(\d{3})(\d{3})/, '$1 $2 $3 $4');
            } else if (digits.startsWith('1') && digits.length <= 10) {
                return '0' + digits.replace(/(\d{1})(\d{3})(\d{3})(\d{3})/, '1 $2 $3 $4');
            }
        }

        return digits.slice(0, 11);
    }

    // Format order message for WhatsApp
    formatOrderMessage(customerData) {
        const orderDate = new Date().toLocaleString('ar-EG', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        let message = `🍽️ *طلب جديد من مطعم محمد الاشرافي* 🍽️\n`;
        message += `═══════════════════════════\n\n`;

        message += `📅 *التاريخ والوقت:*\n${orderDate}\n\n`;

        message += `👤 *بيانات العميل:*\n`;
        message += `┌─ الاسم: ${customerData.name}\n`;
        message += `├─ الهاتف: ${customerData.phone}\n`;
        message += `└─ العنوان: ${customerData.address}\n\n`;

        if (customerData.notes) {
            message += `📝 *ملاحظات خاصة:*\n${customerData.notes}\n\n`;
        }

        message += `🛒 *تفاصيل الطلب:*\n`;
        message += `═══════════════════════════\n`;

        this.items.forEach((item, index) => {
            message += `${index + 1}. *${item.name}*\n`;
            message += `   📦 الكمية: ${item.quantity}\n`;
            message += `   💵 السعر الواحد: ${item.price} جنيه\n`;
            message += `   💰 المجموع: ${item.price * item.quantity} جنيه\n`;
            message += `   ─────────────────────\n`;
        });

        message += `\n💰 *المجموع الكلي: ${this.getTotal()} جنيه*\n`;
        message += `═══════════════════════════\n\n`;

        message += `📞 *للاستفسار:*\n`;
        message += `يرجى الرد على هذه الرسالة لتأكيد الطلب\n`;
        message += `أو الاتصال على: +201014840269\n\n`;

        message += `🙏 *شكراً لاختياركم مطعم محمد الاشرافي*\n`;
        message += `نتطلع لخدمتكم دائماً! ❤️`;

        return message;
    }

    // Show message to user
    showMessage(message, type = 'success') {
        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message show`;
        messageDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            ${message}
        `;
        
        // Add to page
        document.body.appendChild(messageDiv);
        
        // Position message
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '20px';
        messageDiv.style.right = '20px';
        messageDiv.style.zIndex = '9999';
        messageDiv.style.maxWidth = '300px';
        messageDiv.style.padding = '15px';
        messageDiv.style.borderRadius = '10px';
        messageDiv.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
        
        // Remove after 3 seconds
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }

    // Bind events
    bindEvents() {
        // Cart button click
        document.addEventListener('click', (e) => {
            if (e.target.closest('#cartBtn')) {
                this.openSidebar();
            }
            
            if (e.target.closest('#closeCart')) {
                this.closeSidebar();
            }
            
            if (e.target.closest('#checkoutBtn')) {
                this.showOrderModal();
            }
            
            if (e.target.closest('#closeModal')) {
                this.closeOrderModal();
            }
            
            if (e.target.closest('#overlay')) {
                this.closeSidebar();
                this.closeOrderModal();
            }
        });

        // Order form submission
        document.addEventListener('submit', (e) => {
            if (e.target.id === 'orderForm') {
                e.preventDefault();
                
                const formData = new FormData(e.target);
                const customerData = {
                    name: document.getElementById('customerName').value.trim(),
                    phone: document.getElementById('customerPhone').value.trim(),
                    address: document.getElementById('customerAddress').value.trim(),
                    notes: document.getElementById('orderNotes').value.trim()
                };
                
                this.submitOrder(customerData);
            }
        });

        // Escape key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeSidebar();
                this.closeOrderModal();
            }
        });
    }
}

// Initialize cart
const cart = new ShoppingCart();
