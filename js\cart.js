// Shopping Cart Management
class ShoppingCart {
    constructor() {
        this.items = [];
        this.loadFromStorage();
        this.bindEvents();
    }

    // Load cart from localStorage
    loadFromStorage() {
        try {
            const savedCart = localStorage.getItem('restaurant_cart');
            if (savedCart) {
                this.items = JSON.parse(savedCart);
            }
        } catch (error) {
            console.error('Error loading cart from storage:', error);
            this.items = [];
        }
    }

    // Save cart to localStorage
    saveToStorage() {
        try {
            localStorage.setItem('restaurant_cart', JSON.stringify(this.items));
        } catch (error) {
            console.error('Error saving cart to storage:', error);
        }
    }

    // Add item to cart
    addItem(itemId, quantity = 1) {
        const menuItem = getItemById(itemId);
        if (!menuItem || !menuItem.available) {
            this.showMessage('هذا المنتج غير متوفر حالياً', 'error');
            return false;
        }

        const existingItem = this.items.find(item => item.id === itemId);
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            this.items.push({
                id: itemId,
                name: menuItem.name,
                price: menuItem.price,
                quantity: quantity,
                icon: menuItem.icon
            });
        }

        this.saveToStorage();
        this.updateCartDisplay();
        this.showMessage(`تم إضافة ${menuItem.name} إلى السلة`, 'success');
        return true;
    }

    // Remove item from cart
    removeItem(itemId) {
        const itemIndex = this.items.findIndex(item => item.id === itemId);
        if (itemIndex > -1) {
            const removedItem = this.items[itemIndex];
            this.items.splice(itemIndex, 1);
            this.saveToStorage();
            this.updateCartDisplay();
            this.showMessage(`تم حذف ${removedItem.name} من السلة`, 'success');
        }
    }

    // Update item quantity
    updateQuantity(itemId, newQuantity) {
        const item = this.items.find(item => item.id === itemId);
        if (item) {
            if (newQuantity <= 0) {
                this.removeItem(itemId);
            } else {
                item.quantity = newQuantity;
                this.saveToStorage();
                this.updateCartDisplay();
            }
        }
    }

    // Get cart total
    getTotal() {
        return this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
    }

    // Get cart item count
    getItemCount() {
        return this.items.reduce((count, item) => count + item.quantity, 0);
    }

    // Clear cart
    clear() {
        this.items = [];
        this.saveToStorage();
        this.updateCartDisplay();
    }

    // Check if cart is empty
    isEmpty() {
        return this.items.length === 0;
    }

    // Update cart display
    updateCartDisplay() {
        this.updateCartCount();
        this.updateCartSidebar();
        this.updateCheckoutButton();
    }

    // Update cart count in header
    updateCartCount() {
        const cartCountElement = document.getElementById('cartCount');
        if (cartCountElement) {
            const count = this.getItemCount();
            cartCountElement.textContent = count;
            cartCountElement.style.display = count > 0 ? 'flex' : 'none';
        }
    }

    // Update cart sidebar content
    updateCartSidebar() {
        const cartItemsContainer = document.getElementById('cartItems');
        const cartTotalElement = document.getElementById('cartTotal');

        if (!cartItemsContainer || !cartTotalElement) return;

        if (this.isEmpty()) {
            cartItemsContainer.innerHTML = `
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <p>السلة فارغة</p>
                    <button onclick="cart.closeSidebar()">تصفح القائمة</button>
                </div>
            `;
        } else {
            cartItemsContainer.innerHTML = this.items.map(item => `
                <div class="cart-item" data-id="${item.id}">
                    <div class="cart-item-info">
                        <h5><i class="${item.icon}"></i> ${item.name}</h5>
                        <p>${item.price} جنيه × ${item.quantity}</p>
                    </div>
                    <div class="cart-item-controls">
                        <button class="quantity-btn" onclick="cart.updateQuantity('${item.id}', ${item.quantity - 1})">
                            <i class="fas fa-minus"></i>
                        </button>
                        <span class="quantity">${item.quantity}</span>
                        <button class="quantity-btn" onclick="cart.updateQuantity('${item.id}', ${item.quantity + 1})">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="quantity-btn" onclick="cart.removeItem('${item.id}')" style="margin-right: 10px; color: #ff4757;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        cartTotalElement.textContent = this.getTotal();
    }

    // Update checkout button state
    updateCheckoutButton() {
        const checkoutBtn = document.getElementById('checkoutBtn');
        if (checkoutBtn) {
            checkoutBtn.disabled = this.isEmpty();
        }
    }

    // Open cart sidebar
    openSidebar() {
        const cartSidebar = document.getElementById('cartSidebar');
        const overlay = document.getElementById('overlay');
        
        if (cartSidebar && overlay) {
            cartSidebar.classList.add('open');
            overlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }

    // Close cart sidebar
    closeSidebar() {
        const cartSidebar = document.getElementById('cartSidebar');
        const overlay = document.getElementById('overlay');
        
        if (cartSidebar && overlay) {
            cartSidebar.classList.remove('open');
            overlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    }

    // Show order modal
    showOrderModal() {
        if (this.isEmpty()) {
            this.showMessage('السلة فارغة! أضف بعض المنتجات أولاً', 'error');
            return;
        }

        const modal = document.getElementById('orderModal');
        const overlay = document.getElementById('overlay');
        
        if (modal && overlay) {
            this.updateOrderSummary();
            modal.classList.add('show');
            overlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }

    // Close order modal
    closeOrderModal() {
        const modal = document.getElementById('orderModal');
        const overlay = document.getElementById('overlay');
        
        if (modal && overlay) {
            modal.classList.remove('show');
            overlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    }

    // Update order summary in modal
    updateOrderSummary() {
        const orderSummaryElement = document.getElementById('orderSummary');
        const finalTotalElement = document.getElementById('finalTotal');

        if (!orderSummaryElement || !finalTotalElement) return;

        orderSummaryElement.innerHTML = this.items.map(item => `
            <div class="summary-item">
                <span>${item.name} × ${item.quantity}</span>
                <span>${item.price * item.quantity} جنيه</span>
            </div>
        `).join('');

        finalTotalElement.textContent = this.getTotal();
    }

    // Submit order via WhatsApp
    submitOrder(customerData) {
        if (this.isEmpty()) {
            this.showMessage('السلة فارغة!', 'error');
            return false;
        }

        // Validate customer data
        if (!customerData.name || !customerData.phone || !customerData.address) {
            this.showMessage('يرجى ملء جميع البيانات المطلوبة', 'error');
            return false;
        }

        // Format order message
        const orderMessage = this.formatOrderMessage(customerData);
        
        // WhatsApp number
        const whatsappNumber = '201014840269';
        
        // Create WhatsApp URL
        const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(orderMessage)}`;
        
        // Open WhatsApp
        window.open(whatsappUrl, '_blank');
        
        // Show success message
        this.showMessage('تم إرسال الطلب بنجاح! سيتم التواصل معك قريباً', 'success');
        
        // Clear cart and close modal
        this.clear();
        this.closeOrderModal();
        this.closeSidebar();
        
        return true;
    }

    // Format order message for WhatsApp
    formatOrderMessage(customerData) {
        const orderDate = new Date().toLocaleString('ar-EG');
        
        let message = `🍽️ *طلب جديد من مطعم محمد الاشرافي* 🍽️\n\n`;
        message += `📅 *التاريخ والوقت:* ${orderDate}\n\n`;
        message += `👤 *بيانات العميل:*\n`;
        message += `• الاسم: ${customerData.name}\n`;
        message += `• الهاتف: ${customerData.phone}\n`;
        message += `• العنوان: ${customerData.address}\n\n`;
        
        if (customerData.notes) {
            message += `📝 *ملاحظات:* ${customerData.notes}\n\n`;
        }
        
        message += `🛒 *تفاصيل الطلب:*\n`;
        this.items.forEach((item, index) => {
            message += `${index + 1}. ${item.name}\n`;
            message += `   الكمية: ${item.quantity}\n`;
            message += `   السعر: ${item.price} جنيه\n`;
            message += `   المجموع: ${item.price * item.quantity} جنيه\n\n`;
        });
        
        message += `💰 *المجموع الكلي: ${this.getTotal()} جنيه*\n\n`;
        message += `شكراً لاختياركم مطعم محمد الاشرافي! 🙏`;
        
        return message;
    }

    // Show message to user
    showMessage(message, type = 'success') {
        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message show`;
        messageDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            ${message}
        `;
        
        // Add to page
        document.body.appendChild(messageDiv);
        
        // Position message
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '20px';
        messageDiv.style.right = '20px';
        messageDiv.style.zIndex = '9999';
        messageDiv.style.maxWidth = '300px';
        messageDiv.style.padding = '15px';
        messageDiv.style.borderRadius = '10px';
        messageDiv.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
        
        // Remove after 3 seconds
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }

    // Bind events
    bindEvents() {
        // Cart button click
        document.addEventListener('click', (e) => {
            if (e.target.closest('#cartBtn')) {
                this.openSidebar();
            }
            
            if (e.target.closest('#closeCart')) {
                this.closeSidebar();
            }
            
            if (e.target.closest('#checkoutBtn')) {
                this.showOrderModal();
            }
            
            if (e.target.closest('#closeModal')) {
                this.closeOrderModal();
            }
            
            if (e.target.closest('#overlay')) {
                this.closeSidebar();
                this.closeOrderModal();
            }
        });

        // Order form submission
        document.addEventListener('submit', (e) => {
            if (e.target.id === 'orderForm') {
                e.preventDefault();
                
                const formData = new FormData(e.target);
                const customerData = {
                    name: document.getElementById('customerName').value.trim(),
                    phone: document.getElementById('customerPhone').value.trim(),
                    address: document.getElementById('customerAddress').value.trim(),
                    notes: document.getElementById('orderNotes').value.trim()
                };
                
                this.submitOrder(customerData);
            }
        });

        // Escape key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeSidebar();
                this.closeOrderModal();
            }
        });
    }
}

// Initialize cart
const cart = new ShoppingCart();
