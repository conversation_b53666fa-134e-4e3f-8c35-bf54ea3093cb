# Firebase Configuration
FIREBASE_API_KEY=your_firebase_api_key_here
FIREBASE_AUTH_DOMAIN=alashrafi-restaurant.firebaseapp.com
FIREBASE_PROJECT_ID=alashrafi-restaurant
FIREBASE_STORAGE_BUCKET=alashrafi-restaurant.appspot.com
FIREBASE_MESSAGING_SENDER_ID=123456789
FIREBASE_APP_ID=1:123456789:web:abcdef123456789
FIREBASE_MEASUREMENT_ID=G-XXXXXXXXXX

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Restaurant Configuration
RESTAURANT_NAME=مطعم محمد الاشرافي
RESTAURANT_NAME_EN=<PERSON> Restaurant
RESTAURANT_PHONE=+201014840269
RESTAURANT_ADDRESS=القاهرة، مصر
RESTAURANT_EMAIL=<EMAIL>
RESTAURANT_WEBSITE=https://alashrafi-restaurant.web.app

# WhatsApp Configuration
WHATSAPP_NUMBER=201014840269
WHATSAPP_API_TOKEN=your_whatsapp_api_token_here

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
MANAGER_EMAIL=<EMAIL>
MANAGER_PASSWORD=restaurant123

# Security Configuration
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here
CSRF_SECRET=your_csrf_secret_here

# API Configuration
API_BASE_URL=https://alashrafi-restaurant.web.app/api
API_VERSION=v1
API_TIMEOUT=30000

# Performance Configuration
CACHE_DURATION=3600
IMAGE_COMPRESSION_QUALITY=0.8
MAX_IMAGE_SIZE=5242880
MAX_UPLOAD_SIZE=10485760

# Analytics Configuration
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
FACEBOOK_PIXEL_ID=your_facebook_pixel_id
HOTJAR_ID=your_hotjar_id

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
FROM_EMAIL=<EMAIL>
FROM_NAME=مطعم محمد الاشرافي

# SMS Configuration
SMS_PROVIDER=twilio
SMS_API_KEY=your_sms_api_key
SMS_API_SECRET=your_sms_api_secret
SMS_FROM_NUMBER=+**********

# Payment Configuration
PAYMENT_PROVIDER=stripe
STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret

# Development Configuration
NODE_ENV=development
DEBUG=true
LOG_LEVEL=debug
PORT=3000

# Production Configuration
# NODE_ENV=production
# DEBUG=false
# LOG_LEVEL=error
# PORT=80
