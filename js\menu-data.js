// Menu Data for Mohamed Al-Ashrafi Restaurant
const menuData = {
    appetizers: [
        {
            id: 'app1',
            name: 'حمص بالطحينة',
            description: 'حمص طازج مع الطحينة والزيت والبقدونس',
            price: 25,
            category: 'appetizers',
            icon: 'fas fa-seedling',
            available: true
        },
        {
            id: 'app2',
            name: 'متبل باذنجان',
            description: 'باذنجان مشوي مع الطحينة والثوم والليمون',
            price: 30,
            category: 'appetizers',
            icon: 'fas fa-pepper-hot',
            available: true
        },
        {
            id: 'app3',
            name: 'فتوش',
            description: 'سلطة خضار مشكلة مع الخبز المحمص والسماق',
            price: 35,
            category: 'appetizers',
            icon: 'fas fa-leaf',
            available: true
        },
        {
            id: 'app4',
            name: 'تبولة',
            description: 'بقدونس مفروم مع الطماطم والبرغل والليمون',
            price: 30,
            category: 'appetizers',
            icon: 'fas fa-seedling',
            available: true
        },
        {
            id: 'app5',
            name: 'كبة نية',
            description: 'برغل ناعم مع اللحم النيء والبصل والبهارات',
            price: 45,
            category: 'appetizers',
            icon: 'fas fa-circle',
            available: true
        }
    ],
    
    'main-dishes': [
        {
            id: 'main1',
            name: 'شاورما لحم',
            description: 'لحم مشوي مع الخضار والصلصة في خبز عربي',
            price: 55,
            category: 'main-dishes',
            icon: 'fas fa-drumstick-bite',
            available: true
        },
        {
            id: 'main2',
            name: 'شاورما دجاج',
            description: 'دجاج مشوي مع الخضار والثوم في خبز عربي',
            price: 50,
            category: 'main-dishes',
            icon: 'fas fa-drumstick-bite',
            available: true
        },
        {
            id: 'main3',
            name: 'كباب مشوي',
            description: 'كباب لحم مشوي مع الأرز والسلطة',
            price: 70,
            category: 'main-dishes',
            icon: 'fas fa-fire',
            available: true
        },
        {
            id: 'main4',
            name: 'فراخ مشوية',
            description: 'نصف فرخة مشوية مع البطاطس والخضار',
            price: 65,
            category: 'main-dishes',
            icon: 'fas fa-drumstick-bite',
            available: true
        },
        {
            id: 'main5',
            name: 'مكرونة بولونيز',
            description: 'مكرونة مع صلصة اللحم والطماطم والجبن',
            price: 45,
            category: 'main-dishes',
            icon: 'fas fa-utensils',
            available: true
        },
        {
            id: 'main6',
            name: 'برجر لحم',
            description: 'برجر لحم مع الخضار والجبن والبطاطس',
            price: 60,
            category: 'main-dishes',
            icon: 'fas fa-hamburger',
            available: true
        },
        {
            id: 'main7',
            name: 'بيتزا مارجريتا',
            description: 'بيتزا بالطماطم والجبن والريحان',
            price: 55,
            category: 'main-dishes',
            icon: 'fas fa-pizza-slice',
            available: true
        },
        {
            id: 'main8',
            name: 'ملوخية بالفراخ',
            description: 'ملوخية خضراء مع قطع الفراخ والأرز الأبيض',
            price: 50,
            category: 'main-dishes',
            icon: 'fas fa-bowl-food',
            available: true
        }
    ],
    
    beverages: [
        {
            id: 'bev1',
            name: 'شاي أحمر',
            description: 'شاي أحمر طازج مع النعناع',
            price: 10,
            category: 'beverages',
            icon: 'fas fa-mug-hot',
            available: true
        },
        {
            id: 'bev2',
            name: 'قهوة عربية',
            description: 'قهوة عربية أصيلة مع الهيل',
            price: 15,
            category: 'beverages',
            icon: 'fas fa-coffee',
            available: true
        },
        {
            id: 'bev3',
            name: 'عصير برتقال طازج',
            description: 'عصير برتقال طبيعي 100%',
            price: 20,
            category: 'beverages',
            icon: 'fas fa-glass-water',
            available: true
        },
        {
            id: 'bev4',
            name: 'عصير مانجو',
            description: 'عصير مانجو طازج ولذيذ',
            price: 25,
            category: 'beverages',
            icon: 'fas fa-glass-water',
            available: true
        },
        {
            id: 'bev5',
            name: 'موز بالحليب',
            description: 'عصير موز طازج مع الحليب والعسل',
            price: 22,
            category: 'beverages',
            icon: 'fas fa-glass-water',
            available: true
        },
        {
            id: 'bev6',
            name: 'كابتشينو',
            description: 'قهوة كابتشينو مع الحليب المرغي',
            price: 18,
            category: 'beverages',
            icon: 'fas fa-coffee',
            available: true
        },
        {
            id: 'bev7',
            name: 'شاي أخضر',
            description: 'شاي أخضر طبيعي مع الليمون',
            price: 12,
            category: 'beverages',
            icon: 'fas fa-mug-hot',
            available: true
        },
        {
            id: 'bev8',
            name: 'عصير ليمون بالنعناع',
            description: 'عصير ليمون طازج مع النعناع والسكر',
            price: 18,
            category: 'beverages',
            icon: 'fas fa-glass-water',
            available: true
        },
        {
            id: 'bev9',
            name: 'مياه معدنية',
            description: 'مياه معدنية طبيعية باردة',
            price: 8,
            category: 'beverages',
            icon: 'fas fa-bottle-water',
            available: true
        },
        {
            id: 'bev10',
            name: 'كولا',
            description: 'مشروب غازي كولا بارد',
            price: 12,
            category: 'beverages',
            icon: 'fas fa-wine-bottle',
            available: true
        }
    ],
    
    desserts: [
        {
            id: 'des1',
            name: 'كنافة نابلسية',
            description: 'كنافة طازجة بالجبن والقطر',
            price: 35,
            category: 'desserts',
            icon: 'fas fa-birthday-cake',
            available: true
        },
        {
            id: 'des2',
            name: 'بقلاوة',
            description: 'بقلاوة محشوة بالفستق والعسل',
            price: 30,
            category: 'desserts',
            icon: 'fas fa-cookie',
            available: true
        },
        {
            id: 'des3',
            name: 'مهلبية',
            description: 'مهلبية بالحليب والفستق المطحون',
            price: 20,
            category: 'desserts',
            icon: 'fas fa-ice-cream',
            available: true
        },
        {
            id: 'des4',
            name: 'أم علي',
            description: 'حلوى أم علي بالحليب والمكسرات',
            price: 25,
            category: 'desserts',
            icon: 'fas fa-bowl-food',
            available: true
        },
        {
            id: 'des5',
            name: 'تيراميسو',
            description: 'تيراميسو إيطالي أصلي بالقهوة',
            price: 40,
            category: 'desserts',
            icon: 'fas fa-birthday-cake',
            available: true
        },
        {
            id: 'des6',
            name: 'آيس كريم فانيليا',
            description: 'آيس كريم فانيليا طبيعي بارد',
            price: 18,
            category: 'desserts',
            icon: 'fas fa-ice-cream',
            available: true
        },
        {
            id: 'des7',
            name: 'كيك شوكولاتة',
            description: 'قطعة كيك شوكولاتة غنية ولذيذة',
            price: 28,
            category: 'desserts',
            icon: 'fas fa-birthday-cake',
            available: true
        }
    ]
};

// Get all menu items as a flat array
function getAllMenuItems() {
    const allItems = [];
    Object.values(menuData).forEach(categoryItems => {
        allItems.push(...categoryItems);
    });
    return allItems;
}

// Get items by category
function getItemsByCategory(category) {
    if (category === 'all') {
        return getAllMenuItems();
    }
    return menuData[category] || [];
}

// Get item by ID
function getItemById(id) {
    const allItems = getAllMenuItems();
    return allItems.find(item => item.id === id);
}

// Search items by name or description
function searchItems(query) {
    const allItems = getAllMenuItems();
    const searchTerm = query.toLowerCase().trim();
    
    if (!searchTerm) {
        return allItems;
    }
    
    return allItems.filter(item => 
        item.name.toLowerCase().includes(searchTerm) ||
        item.description.toLowerCase().includes(searchTerm)
    );
}

// Get available items only
function getAvailableItems(items = null) {
    const itemsToFilter = items || getAllMenuItems();
    return itemsToFilter.filter(item => item.available);
}

// Categories configuration
const categories = {
    all: {
        name: 'الكل',
        icon: 'fas fa-th-large'
    },
    appetizers: {
        name: 'المقبلات',
        icon: 'fas fa-seedling'
    },
    'main-dishes': {
        name: 'الأطباق الرئيسية',
        icon: 'fas fa-drumstick-bite'
    },
    beverages: {
        name: 'المشروبات',
        icon: 'fas fa-coffee'
    },
    desserts: {
        name: 'الحلويات',
        icon: 'fas fa-ice-cream'
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        menuData,
        getAllMenuItems,
        getItemsByCategory,
        getItemById,
        searchItems,
        getAvailableItems,
        categories
    };
}
