/* Admin Panel Styles */

/* Admin Body */
.admin-body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* Login Container */
.admin-login-container {
    width: 100%;
    max-width: 450px;
    position: relative;
}

.login-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    animation: slideUp 0.6s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Login Header */
.login-header {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
}

.login-header .logo i {
    font-size: 3rem;
    color: #ffd700;
    margin-bottom: 15px;
    display: block;
}

.login-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.login-header p {
    font-size: 1rem;
    opacity: 0.9;
}

/* Login Form */
.login-form-container {
    padding: 40px 30px 30px;
}

.login-form .form-group {
    margin-bottom: 25px;
}

.login-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c5aa0;
    font-size: 0.95rem;
}

.login-form label i {
    margin-left: 8px;
    width: 16px;
}

.login-form input[type="text"],
.login-form input[type="password"] {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
    background: #f8f9fa;
}

.login-form input:focus {
    outline: none;
    border-color: #2c5aa0;
    background: white;
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

/* Password Input */
.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.toggle-password:hover {
    background: #f0f0f0;
    color: #2c5aa0;
}

/* Checkbox */
.checkbox-label {
    display: flex !important;
    align-items: center;
    cursor: pointer;
    font-weight: 400 !important;
    color: #666 !important;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e1e5e9;
    border-radius: 4px;
    margin-left: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input:checked + .checkmark {
    background: #2c5aa0;
    border-color: #2c5aa0;
}

.checkbox-label input:checked + .checkmark::after {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: white;
    font-size: 12px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Login Button */
.login-btn {
    width: 100%;
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(44, 90, 160, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

/* Login Footer */
.login-footer {
    text-align: center;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.back-to-site {
    color: #666;
    text-decoration: none;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.back-to-site:hover {
    color: #2c5aa0;
}

/* Error Message */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px 20px;
    border-radius: 12px;
    margin-top: 20px;
    border: 1px solid #f5c6cb;
    display: none;
    align-items: center;
    gap: 10px;
    animation: slideDown 0.3s ease;
}

.error-message.show {
    display: flex;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.loading-spinner i {
    font-size: 2rem;
    color: #2c5aa0;
    margin-bottom: 15px;
}

.loading-spinner p {
    color: #666;
    margin: 0;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 480px) {
    .admin-login-container {
        max-width: 100%;
    }
    
    .login-header {
        padding: 30px 20px;
    }
    
    .login-form-container {
        padding: 30px 20px 20px;
    }
    
    .login-header h1 {
        font-size: 1.7rem;
    }
    
    .login-header .logo i {
        font-size: 2.5rem;
    }
}

/* Dashboard Styles */
.admin-dashboard {
    background: #f8f9fa;
    min-height: 100vh;
}

.admin-header {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
    color: white;
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.admin-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.admin-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-logo i {
    font-size: 2rem;
    color: #ffd700;
}

.admin-logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.admin-nav {
    display: flex;
    align-items: center;
    gap: 20px;
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 15px;
    background: rgba(255,255,255,0.1);
    border-radius: 25px;
}

.logout-btn {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.logout-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: calc(100vh - 70px);
}

.dashboard-sidebar {
    width: 280px;
    background: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
}

.sidebar-nav {
    flex: 1;
    padding: 30px 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 30px;
    color: #666;
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background: #f8f9fa;
    color: #2c5aa0;
}

.nav-item.active .nav-link {
    background: #e3f2fd;
    color: #2c5aa0;
    border-right-color: #2c5aa0;
}

.nav-link i {
    font-size: 1.2rem;
    width: 20px;
}

.sidebar-footer {
    padding: 20px 30px;
    border-top: 1px solid #e1e5e9;
}

.view-site-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: #f8f9fa;
    color: #666;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    justify-content: center;
}

.view-site-btn:hover {
    background: #2c5aa0;
    color: white;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    padding: 30px;
    background: #f8f9fa;
    overflow-y: auto;
}

.dashboard-section {
    display: none;
}

.dashboard-section.active {
    display: block;
}

.section-header {
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.section-header h2 {
    color: #2c5aa0;
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
}

.section-header p {
    color: #666;
    margin: 5px 0 0 0;
    font-size: 1rem;
}

.section-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    text-decoration: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(44, 90, 160, 0.3);
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 2px solid #e1e5e9;
    padding: 10px 18px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    text-decoration: none;
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #2c5aa0;
    color: #2c5aa0;
}

.btn-danger {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-success {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
}

.btn-success:hover {
    background: #218838;
}
