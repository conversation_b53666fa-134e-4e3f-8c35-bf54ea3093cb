// Dashboard Product Manager
// مدير المنتجات داخل لوحة الإدارة

class DashboardProductManager {
    constructor() {
        this.products = [];
        this.categories = [
            { id: '1', name: 'المقبلات', color: '#27ae60' },
            { id: '2', name: 'الأطباق الرئيسية', color: '#e74c3c' },
            { id: '3', name: 'المشروبات', color: '#3498db' },
            { id: '4', name: 'الحلويات', color: '#f39c12' }
        ];
        this.filters = {
            search: '',
            category: 'all',
            availability: 'all'
        };
        this.selectedProducts = new Set();
        this.storageManager = null;
        
        console.log('🚀 DashboardProductManager initializing...');
        this.init();
    }

    async init() {
        try {
            // Wait for storage manager
            await this.waitForStorageManager();
            
            // Load products
            this.loadProducts();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Render initial state
            this.renderProducts();
            this.updateStatistics();
            
            console.log('✅ DashboardProductManager initialized');
            
        } catch (error) {
            console.error('❌ Error initializing DashboardProductManager:', error);
        }
    }

    async waitForStorageManager() {
        return new Promise((resolve) => {
            const check = () => {
                if (window.supabaseStorageManager) {
                    this.storageManager = window.supabaseStorageManager;
                    resolve();
                } else {
                    setTimeout(check, 100);
                }
            };
            check();
        });
    }

    loadProducts() {
        try {
            const savedProducts = localStorage.getItem('restaurant_products');
            if (savedProducts && savedProducts !== 'undefined') {
                this.products = JSON.parse(savedProducts);
            } else {
                this.products = this.getDefaultProducts();
                this.saveProducts();
            }
            console.log(`📦 Loaded ${this.products.length} products`);
        } catch (error) {
            console.error('Error loading products:', error);
            this.products = this.getDefaultProducts();
        }
    }

    getDefaultProducts() {
        return [
            {
                id: 'p1',
                name: 'حمص بالطحينة',
                description: 'حمص طازج مع الطحينة والزيت والبقدونس',
                price: 25,
                category_id: '1',
                category: 'المقبلات',
                icon: 'fas fa-seedling',
                is_available: true,
                is_featured: false,
                images: [],
                image_url: null,
                created_at: new Date().toISOString()
            },
            {
                id: 'p2',
                name: 'كباب مشوي',
                description: 'كباب لحم مشوي مع الخضار والأرز',
                price: 85,
                category_id: '2',
                category: 'الأطباق الرئيسية',
                icon: 'fas fa-drumstick-bite',
                is_available: true,
                is_featured: true,
                images: [],
                image_url: null,
                created_at: new Date().toISOString()
            },
            {
                id: 'p3',
                name: 'شاي أحمر',
                description: 'شاي أحمر طازج مع النعناع',
                price: 15,
                category_id: '3',
                category: 'المشروبات',
                icon: 'fas fa-coffee',
                is_available: true,
                is_featured: false,
                images: [],
                image_url: null,
                created_at: new Date().toISOString()
            }
        ];
    }

    saveProducts() {
        try {
            localStorage.setItem('restaurant_products', JSON.stringify(this.products));
            // Trigger update for main site
            window.dispatchEvent(new CustomEvent('menuDataRefreshed', {
                detail: { products: this.products }
            }));
        } catch (error) {
            console.error('Error saving products:', error);
        }
    }

    setupEventListeners() {
        // Search
        const searchInput = document.getElementById('dashboardProductSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value;
                this.applyFilters();
            });
        }

        // Category filter
        const categoryFilter = document.getElementById('dashboardCategoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filters.category = e.target.value;
                this.applyFilters();
            });
        }

        // Availability filter
        const availabilityFilter = document.getElementById('dashboardAvailabilityFilter');
        if (availabilityFilter) {
            availabilityFilter.addEventListener('change', (e) => {
                this.filters.availability = e.target.value;
                this.applyFilters();
            });
        }

        // Select all
        const selectAllCheckbox = document.getElementById('dashboardSelectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.selectAllProducts(e.target.checked);
            });
        }

        // Product actions
        document.addEventListener('click', (e) => {
            const target = e.target.closest('[data-action]');
            if (!target) return;

            const action = target.dataset.action;
            const productId = target.dataset.productId;

            switch (action) {
                case 'edit-product':
                    this.editProduct(productId);
                    break;
                case 'delete-product':
                    this.deleteProduct(productId);
                    break;
                case 'toggle-availability':
                    this.toggleAvailability(productId);
                    break;
                case 'toggle-featured':
                    this.toggleFeatured(productId);
                    break;
            }
        });

        // Product selection
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('product-select')) {
                const productId = e.target.dataset.productId;
                if (e.target.checked) {
                    this.selectedProducts.add(productId);
                } else {
                    this.selectedProducts.delete(productId);
                }
                this.updateSelectAllState();
            }
        });
    }

    applyFilters() {
        this.renderProducts();
    }

    getFilteredProducts() {
        let filtered = [...this.products];

        // Search filter
        if (this.filters.search) {
            const searchTerm = this.filters.search.toLowerCase();
            filtered = filtered.filter(product => 
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm)
            );
        }

        // Category filter
        if (this.filters.category !== 'all') {
            filtered = filtered.filter(product => product.category_id === this.filters.category);
        }

        // Availability filter
        if (this.filters.availability !== 'all') {
            const isAvailable = this.filters.availability === 'available';
            filtered = filtered.filter(product => product.is_available === isAvailable);
        }

        return filtered;
    }

    renderProducts() {
        const container = document.getElementById('dashboardProductsGrid');
        if (!container) return;

        const filteredProducts = this.getFilteredProducts();

        if (filteredProducts.length === 0) {
            container.innerHTML = this.renderEmptyState();
            return;
        }

        const productsHtml = filteredProducts.map(product => this.renderProductCard(product)).join('');
        container.innerHTML = productsHtml;
        
        this.updateStatistics();
    }

    renderProductCard(product) {
        const categoryColor = this.getCategoryColor(product.category_id);
        const imageHtml = this.renderProductImage(product);
        const badgesHtml = this.renderProductBadges(product);

        return `
            <div class="product-card ${product.is_available ? 'available' : 'unavailable'}" 
                 data-product-id="${product.id}">
                <div class="product-card-header">
                    <input type="checkbox" class="product-select" data-product-id="${product.id}">
                    <div class="product-actions">
                        <button class="btn btn-sm btn-primary" data-action="edit-product" data-product-id="${product.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm ${product.is_available ? 'btn-warning' : 'btn-success'}" 
                                data-action="toggle-availability" data-product-id="${product.id}" 
                                title="${product.is_available ? 'إخفاء' : 'إظهار'}">
                            <i class="fas ${product.is_available ? 'fa-eye-slash' : 'fa-eye'}"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" data-action="delete-product" data-product-id="${product.id}" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div class="product-image-container">
                    ${imageHtml}
                    ${badgesHtml}
                </div>
                
                <div class="product-info">
                    <div class="product-category" style="background-color: ${categoryColor}20; color: ${categoryColor};">
                        ${product.category || this.getCategoryName(product.category_id)}
                    </div>
                    
                    <h3 class="product-name">${product.name}</h3>
                    <p class="product-description">${product.description}</p>
                    
                    <div class="product-price">${product.price} جنيه</div>
                    
                    <div class="product-meta">
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span>${product.preparation_time || 0} دقيقة</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-eye"></i>
                            <span>${product.view_count || 0}</span>
                        </div>
                    </div>
                </div>
                
                <div class="product-card-footer">
                    <div class="quick-actions">
                        <button class="btn btn-primary btn-sm" data-action="edit-product" data-product-id="${product.id}">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn ${product.is_featured ? 'btn-warning' : 'btn-secondary'} btn-sm" 
                                data-action="toggle-featured" data-product-id="${product.id}">
                            <i class="fas fa-star"></i> ${product.is_featured ? 'إلغاء التمييز' : 'تمييز'}
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    renderProductImage(product) {
        if (product.image_url) {
            return `
                <img src="${product.image_url}" alt="${product.name}" class="product-image" 
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="product-icon-fallback" style="display: none;">
                    <i class="${product.icon || 'fas fa-utensils'}"></i>
                </div>
            `;
        } else {
            return `
                <div class="product-icon-fallback">
                    <i class="${product.icon || 'fas fa-utensils'}"></i>
                </div>
            `;
        }
    }

    renderProductBadges(product) {
        const badges = [];
        
        if (product.is_featured) badges.push('<span class="badge badge-featured">مميز</span>');
        if (product.is_new) badges.push('<span class="badge badge-new">جديد</span>');
        if (product.is_popular) badges.push('<span class="badge badge-popular">شائع</span>');
        if (product.discount_percentage > 0) badges.push(`<span class="badge badge-discount">خصم ${product.discount_percentage}%</span>`);
        
        return badges.length > 0 ? `<div class="product-badges">${badges.join('')}</div>` : '';
    }

    renderEmptyState() {
        return `
            <div class="empty-state">
                <i class="fas fa-utensils"></i>
                <h3>لا توجد منتجات</h3>
                <p>لم يتم العثور على منتجات تطابق المعايير المحددة</p>
                <button class="btn btn-primary" onclick="dashboardProductManager.openProductModal()">
                    <i class="fas fa-plus"></i> إضافة منتج جديد
                </button>
            </div>
        `;
    }

    getCategoryName(categoryId) {
        const category = this.categories.find(cat => cat.id === categoryId);
        return category ? category.name : 'عام';
    }

    getCategoryColor(categoryId) {
        const category = this.categories.find(cat => cat.id === categoryId);
        return category ? category.color : '#95a5a6';
    }

    updateStatistics() {
        const totalProducts = this.products.length;
        const featuredProducts = this.products.filter(p => p.is_featured).length;
        const availableProducts = this.products.filter(p => p.is_available).length;
        const averagePrice = totalProducts > 0 ? 
            Math.round(this.products.reduce((sum, p) => sum + p.price, 0) / totalProducts) : 0;

        document.getElementById('dashboardTotalProducts').textContent = totalProducts;
        document.getElementById('dashboardFeaturedProducts').textContent = featuredProducts;
        document.getElementById('dashboardAvailableProducts').textContent = availableProducts;
        document.getElementById('dashboardAveragePrice').textContent = averagePrice + ' جنيه';
    }

    openProductModal(productId = null) {
        // Create modal if it doesn't exist
        if (!document.getElementById('dashboardProductModal')) {
            this.createProductModal();
        }

        const modal = document.getElementById('dashboardProductModal');
        const form = document.getElementById('dashboardProductForm');

        // Reset form
        form.reset();

        if (productId) {
            // Edit mode
            const product = this.products.find(p => p.id === productId);
            if (product) {
                this.populateForm(product);
            }
            document.getElementById('dashboardModalTitle').textContent = 'تعديل المنتج';
        } else {
            // Add mode
            document.getElementById('dashboardModalTitle').textContent = 'إضافة منتج جديد';
        }

        modal.classList.add('active');
        this.currentProductId = productId;
    }

    createProductModal() {
        const modalHtml = `
            <div class="product-modal" id="dashboardProductModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 id="dashboardModalTitle">إضافة منتج جديد</h2>
                        <button class="modal-close" onclick="dashboardProductManager.closeProductModal()">&times;</button>
                    </div>
                    <form id="dashboardProductForm" class="product-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="dashboardProductName">اسم المنتج *</label>
                                <input type="text" id="dashboardProductName" name="name" required>
                            </div>

                            <div class="form-group">
                                <label for="dashboardProductPrice">السعر (جنيه) *</label>
                                <input type="number" id="dashboardProductPrice" name="price" min="0" step="0.01" required>
                            </div>

                            <div class="form-group full-width">
                                <label for="dashboardProductDescription">الوصف</label>
                                <textarea id="dashboardProductDescription" name="description" rows="3"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="dashboardProductCategory">الفئة *</label>
                                <select id="dashboardProductCategory" name="category_id" required>
                                    <option value="">اختر الفئة</option>
                                    ${this.categories.map(cat =>
                                        `<option value="${cat.id}">${cat.name}</option>`
                                    ).join('')}
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="dashboardProductIcon">الأيقونة</label>
                                <input type="text" id="dashboardProductIcon" name="icon" placeholder="fas fa-utensils">
                            </div>

                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="dashboardProductAvailable" name="is_available" checked>
                                    <label for="dashboardProductAvailable">متوفر للطلب</label>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="dashboardProductFeatured" name="is_featured">
                                    <label for="dashboardProductFeatured">منتج مميز</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ المنتج
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="dashboardProductManager.closeProductModal()">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Setup form submission
        const form = document.getElementById('dashboardProductForm');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleProductSubmit(e.target);
        });
    }

    populateForm(product) {
        document.getElementById('dashboardProductName').value = product.name || '';
        document.getElementById('dashboardProductDescription').value = product.description || '';
        document.getElementById('dashboardProductPrice').value = product.price || '';
        document.getElementById('dashboardProductCategory').value = product.category_id || '';
        document.getElementById('dashboardProductIcon').value = product.icon || '';
        document.getElementById('dashboardProductAvailable').checked = product.is_available !== false;
        document.getElementById('dashboardProductFeatured').checked = product.is_featured || false;
    }

    closeProductModal() {
        const modal = document.getElementById('dashboardProductModal');
        if (modal) {
            modal.classList.remove('active');
        }
        this.currentProductId = null;
    }

    async handleProductSubmit(form) {
        try {
            const formData = new FormData(form);
            const productData = {
                name: formData.get('name'),
                description: formData.get('description'),
                price: parseFloat(formData.get('price')) || 0,
                category_id: formData.get('category_id'),
                category: this.getCategoryName(formData.get('category_id')),
                icon: formData.get('icon') || 'fas fa-utensils',
                is_available: formData.get('is_available') === 'on',
                is_featured: formData.get('is_featured') === 'on'
            };

            if (this.currentProductId) {
                // Update existing product
                this.updateProduct(this.currentProductId, productData);
            } else {
                // Create new product
                this.createProduct(productData);
            }

            this.closeProductModal();
            this.renderProducts();
            this.saveProducts();

        } catch (error) {
            console.error('Error saving product:', error);
            this.showNotification('خطأ في حفظ المنتج', 'error');
        }
    }

    createProduct(productData) {
        const newProduct = {
            id: 'p_' + Date.now(),
            ...productData,
            images: [],
            image_url: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        this.products.unshift(newProduct);
        this.showNotification('تم إضافة المنتج بنجاح', 'success');

        // Notify main site
        window.dispatchEvent(new CustomEvent('productAdded', { detail: newProduct }));
    }

    updateProduct(productId, productData) {
        const index = this.products.findIndex(p => p.id === productId);
        if (index !== -1) {
            this.products[index] = {
                ...this.products[index],
                ...productData,
                updated_at: new Date().toISOString()
            };
            this.showNotification('تم تحديث المنتج بنجاح', 'success');

            // Notify main site
            window.dispatchEvent(new CustomEvent('productUpdated', { detail: this.products[index] }));
        }
    }

    editProduct(productId) {
        this.openProductModal(productId);
    }

    deleteProduct(productId) {
        if (!confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            return;
        }

        const index = this.products.findIndex(p => p.id === productId);
        if (index !== -1) {
            this.products.splice(index, 1);
            this.renderProducts();
            this.saveProducts();
            this.showNotification('تم حذف المنتج بنجاح', 'success');

            // Notify main site
            window.dispatchEvent(new CustomEvent('productDeleted', { detail: { id: productId } }));
        }
    }

    toggleAvailability(productId) {
        const product = this.products.find(p => p.id === productId);
        if (product) {
            product.is_available = !product.is_available;
            product.updated_at = new Date().toISOString();
            this.renderProducts();
            this.saveProducts();

            const status = product.is_available ? 'متوفر' : 'غير متوفر';
            this.showNotification(`تم تغيير حالة ${product.name} إلى ${status}`, 'success');

            // Notify main site
            window.dispatchEvent(new CustomEvent('productUpdated', { detail: product }));
        }
    }

    toggleFeatured(productId) {
        const product = this.products.find(p => p.id === productId);
        if (product) {
            product.is_featured = !product.is_featured;
            product.updated_at = new Date().toISOString();
            this.renderProducts();
            this.saveProducts();

            const status = product.is_featured ? 'مميز' : 'عادي';
            this.showNotification(`تم تغيير ${product.name} إلى ${status}`, 'success');

            // Notify main site
            window.dispatchEvent(new CustomEvent('productUpdated', { detail: product }));
        }
    }

    selectAllProducts(checked) {
        const checkboxes = document.querySelectorAll('.product-select');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const productId = checkbox.dataset.productId;
            if (checked) {
                this.selectedProducts.add(productId);
            } else {
                this.selectedProducts.delete(productId);
            }
        });
    }

    updateSelectAllState() {
        const selectAllCheckbox = document.getElementById('dashboardSelectAll');
        const checkboxes = document.querySelectorAll('.product-select');
        const checkedCount = document.querySelectorAll('.product-select:checked').length;

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkedCount === checkboxes.length && checkboxes.length > 0;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
        }
    }

    bulkDelete() {
        if (this.selectedProducts.size === 0) {
            this.showNotification('يرجى اختيار منتجات للحذف', 'warning');
            return;
        }

        if (!confirm(`هل أنت متأكد من حذف ${this.selectedProducts.size} منتج؟`)) {
            return;
        }

        this.selectedProducts.forEach(productId => {
            const index = this.products.findIndex(p => p.id === productId);
            if (index !== -1) {
                this.products.splice(index, 1);
            }
        });

        this.selectedProducts.clear();
        this.renderProducts();
        this.saveProducts();
        this.showNotification('تم حذف المنتجات المحددة بنجاح', 'success');
    }

    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.dashboard-notification');
        existingNotifications.forEach(notification => notification.remove());

        const notification = document.createElement('div');
        notification.className = `dashboard-notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas ${type === 'success' ? 'fa-check-circle' :
                           type === 'error' ? 'fa-exclamation-circle' :
                           type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#d4edda' :
                        type === 'error' ? '#f8d7da' :
                        type === 'warning' ? '#fff3cd' : '#d1ecf1'};
            color: ${type === 'success' ? '#155724' :
                    type === 'error' ? '#721c24' :
                    type === 'warning' ? '#856404' : '#0c5460'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' :
                               type === 'error' ? '#f5c6cb' :
                               type === 'warning' ? '#ffeaa7' : '#bee5eb'};
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 300px;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
}

// Initialize
const dashboardProductManager = new DashboardProductManager();
window.dashboardProductManager = dashboardProductManager;

console.log('✅ DashboardProductManager loaded');
