// Dashboard Product Manager
// مدير المنتجات داخل لوحة الإدارة

class DashboardProductManager {
    constructor() {
        this.products = [];
        this.categories = [
            { id: '1', name: 'المقبلات', color: '#27ae60' },
            { id: '2', name: 'الأطباق الرئيسية', color: '#e74c3c' },
            { id: '3', name: 'المشروبات', color: '#3498db' },
            { id: '4', name: 'الحلويات', color: '#f39c12' }
        ];
        this.filters = {
            search: '',
            category: 'all',
            availability: 'all'
        };
        this.selectedProducts = new Set();
        this.storageManager = null;
        
        console.log('🚀 DashboardProductManager initializing...');
        this.init();
    }

    async init() {
        try {
            // Wait for dependencies
            await this.waitForDependencies();

            // Load products from database
            await this.loadProductsFromDatabase();

            // Setup event listeners
            this.setupEventListeners();

            // Render initial state
            this.renderProducts();
            this.updateStatistics();

            // Setup real-time sync
            this.setupRealTimeSync();

            // Setup database status monitoring
            this.setupDatabaseStatusMonitoring();

            console.log('✅ DashboardProductManager initialized with database connection');

        } catch (error) {
            console.error('❌ Error initializing DashboardProductManager:', error);
            // Fallback to local data
            this.loadProducts();
            this.renderProducts();
            this.updateStatistics();
        }
    }

    async waitForDependencies() {
        // Wait for Supabase Manager
        await this.waitForSupabaseManager();

        // Wait for Storage Manager
        await this.waitForStorageManager();
    }

    async waitForSupabaseManager() {
        return new Promise((resolve) => {
            const check = () => {
                if (window.supabaseManager && window.supabaseManager.isInitialized) {
                    this.supabase = window.supabaseManager;
                    console.log('✅ Supabase Manager connected');
                    resolve();
                } else {
                    setTimeout(check, 100);
                }
            };
            check();
        });
    }

    async waitForStorageManager() {
        return new Promise((resolve) => {
            const check = () => {
                if (window.supabaseStorageManager) {
                    this.storageManager = window.supabaseStorageManager;
                    resolve();
                } else {
                    setTimeout(check, 100);
                }
            };
            check();
        });
    }

    async loadProductsFromDatabase() {
        try {
            if (this.supabase && this.supabase.isInitialized) {
                console.log('🔄 Loading products from Supabase...');
                const result = await this.supabase.getProducts();

                if (result.success && result.data) {
                    this.products = result.data.map(product => this.normalizeProduct(product));
                    console.log(`✅ Loaded ${this.products.length} products from Supabase`);

                    // Save to localStorage as backup
                    this.saveToLocalStorage();
                    return;
                }
            }

            // Fallback to localStorage
            this.loadProducts();

        } catch (error) {
            console.error('❌ Error loading from database:', error);
            this.loadProducts();
        }
    }

    loadProducts() {
        try {
            const savedProducts = localStorage.getItem('restaurant_products');
            if (savedProducts && savedProducts !== 'undefined') {
                this.products = JSON.parse(savedProducts).map(product => this.normalizeProduct(product));
            } else {
                this.products = this.getDefaultProducts();
                this.saveProducts();
            }
            console.log(`📦 Loaded ${this.products.length} products from localStorage`);
        } catch (error) {
            console.error('Error loading products:', error);
            this.products = this.getDefaultProducts();
        }
    }

    normalizeProduct(product) {
        return {
            id: product.id,
            name: product.name || '',
            name_en: product.name_en || product.name || '',
            description: product.description || '',
            description_en: product.description_en || product.description || '',
            price: parseFloat(product.price) || 0,
            original_price: parseFloat(product.original_price) || parseFloat(product.price) || 0,
            discount_percentage: parseFloat(product.discount_percentage) || 0,
            category_id: product.category_id || '1',
            category: product.category || this.getCategoryName(product.category_id),
            images: Array.isArray(product.images) ? product.images : [],
            image_url: product.image_url || this.getPrimaryImageUrl(product.images),
            icon: product.icon || 'fas fa-utensils',
            is_available: product.is_available !== false,
            is_featured: product.is_featured || false,
            is_new: product.is_new || false,
            is_popular: product.is_popular || false,
            preparation_time: parseInt(product.preparation_time) || 0,
            view_count: parseInt(product.view_count) || 0,
            order_count: parseInt(product.order_count) || 0,
            created_at: product.created_at || new Date().toISOString(),
            updated_at: product.updated_at || new Date().toISOString()
        };
    }

    getPrimaryImageUrl(images) {
        if (!Array.isArray(images) || images.length === 0) return null;
        const primaryImage = images.find(img => img.isPrimary || img.is_primary) || images[0];
        return primaryImage.url || primaryImage.image_url || primaryImage.path || null;
    }

    getDefaultProducts() {
        return [
            {
                id: 'p1',
                name: 'حمص بالطحينة',
                description: 'حمص طازج مع الطحينة والزيت والبقدونس',
                price: 25,
                category_id: '1',
                category: 'المقبلات',
                icon: 'fas fa-seedling',
                is_available: true,
                is_featured: false,
                images: [],
                image_url: null,
                created_at: new Date().toISOString()
            },
            {
                id: 'p2',
                name: 'كباب مشوي',
                description: 'كباب لحم مشوي مع الخضار والأرز',
                price: 85,
                category_id: '2',
                category: 'الأطباق الرئيسية',
                icon: 'fas fa-drumstick-bite',
                is_available: true,
                is_featured: true,
                images: [],
                image_url: null,
                created_at: new Date().toISOString()
            },
            {
                id: 'p3',
                name: 'شاي أحمر',
                description: 'شاي أحمر طازج مع النعناع',
                price: 15,
                category_id: '3',
                category: 'المشروبات',
                icon: 'fas fa-coffee',
                is_available: true,
                is_featured: false,
                images: [],
                image_url: null,
                created_at: new Date().toISOString()
            }
        ];
    }

    async saveProducts() {
        try {
            // Save to localStorage first (immediate backup)
            this.saveToLocalStorage();

            // Save to Supabase if available
            if (this.supabase && this.supabase.isInitialized) {
                console.log('💾 Syncing products to Supabase...');
                // Note: Individual product operations will handle Supabase sync
            }

        } catch (error) {
            console.error('Error saving products:', error);
        }
    }

    saveToLocalStorage() {
        try {
            localStorage.setItem('restaurant_products', JSON.stringify(this.products));
            // Trigger update for main site
            window.dispatchEvent(new CustomEvent('menuDataRefreshed', {
                detail: { products: this.products }
            }));
        } catch (error) {
            console.error('Error saving to localStorage:', error);
        }
    }

    setupRealTimeSync() {
        // Listen for storage changes (cross-tab sync)
        window.addEventListener('storage', (e) => {
            if (e.key === 'restaurant_products') {
                console.log('🔄 Products updated in another tab');
                this.loadProducts();
                this.renderProducts();
                this.updateStatistics();
            }
        });

        // Listen for custom events
        window.addEventListener('productAdded', (e) => {
            console.log('➕ Product added externally:', e.detail);
            this.handleExternalProductAdd(e.detail);
        });

        window.addEventListener('productUpdated', (e) => {
            console.log('✏️ Product updated externally:', e.detail);
            this.handleExternalProductUpdate(e.detail);
        });

        window.addEventListener('productDeleted', (e) => {
            console.log('🗑️ Product deleted externally:', e.detail);
            this.handleExternalProductDelete(e.detail);
        });
    }

    handleExternalProductAdd(productData) {
        const existingIndex = this.products.findIndex(p => p.id === productData.id);
        if (existingIndex === -1) {
            this.products.unshift(this.normalizeProduct(productData));
            this.renderProducts();
            this.updateStatistics();
        }
    }

    handleExternalProductUpdate(productData) {
        const index = this.products.findIndex(p => p.id === productData.id);
        if (index !== -1) {
            this.products[index] = this.normalizeProduct(productData);
            this.renderProducts();
            this.updateStatistics();
        }
    }

    handleExternalProductDelete(data) {
        const index = this.products.findIndex(p => p.id === data.id);
        if (index !== -1) {
            this.products.splice(index, 1);
            this.renderProducts();
            this.updateStatistics();
        }
    }

    setupEventListeners() {
        // Search
        const searchInput = document.getElementById('dashboardProductSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value;
                this.applyFilters();
            });
        }

        // Category filter
        const categoryFilter = document.getElementById('dashboardCategoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filters.category = e.target.value;
                this.applyFilters();
            });
        }

        // Availability filter
        const availabilityFilter = document.getElementById('dashboardAvailabilityFilter');
        if (availabilityFilter) {
            availabilityFilter.addEventListener('change', (e) => {
                this.filters.availability = e.target.value;
                this.applyFilters();
            });
        }

        // Select all
        const selectAllCheckbox = document.getElementById('dashboardSelectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.selectAllProducts(e.target.checked);
            });
        }

        // Product actions
        document.addEventListener('click', (e) => {
            const target = e.target.closest('[data-action]');
            if (!target) return;

            const action = target.dataset.action;
            const productId = target.dataset.productId;

            switch (action) {
                case 'edit-product':
                    this.editProduct(productId);
                    break;
                case 'delete-product':
                    this.deleteProduct(productId);
                    break;
                case 'toggle-availability':
                    this.toggleAvailability(productId);
                    break;
                case 'toggle-featured':
                    this.toggleFeatured(productId);
                    break;
            }
        });

        // Product selection
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('product-select')) {
                const productId = e.target.dataset.productId;
                if (e.target.checked) {
                    this.selectedProducts.add(productId);
                } else {
                    this.selectedProducts.delete(productId);
                }
                this.updateSelectAllState();
            }
        });
    }

    applyFilters() {
        this.renderProducts();
    }

    getFilteredProducts() {
        let filtered = [...this.products];

        // Search filter
        if (this.filters.search) {
            const searchTerm = this.filters.search.toLowerCase();
            filtered = filtered.filter(product => 
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm)
            );
        }

        // Category filter
        if (this.filters.category !== 'all') {
            filtered = filtered.filter(product => product.category_id === this.filters.category);
        }

        // Availability filter
        if (this.filters.availability !== 'all') {
            const isAvailable = this.filters.availability === 'available';
            filtered = filtered.filter(product => product.is_available === isAvailable);
        }

        return filtered;
    }

    renderProducts() {
        const container = document.getElementById('dashboardProductsGrid');
        if (!container) return;

        const filteredProducts = this.getFilteredProducts();

        if (filteredProducts.length === 0) {
            container.innerHTML = this.renderEmptyState();
            return;
        }

        const productsHtml = filteredProducts.map(product => this.renderProductCard(product)).join('');
        container.innerHTML = productsHtml;
        
        this.updateStatistics();
    }

    renderProductCard(product) {
        const categoryColor = this.getCategoryColor(product.category_id);
        const imageHtml = this.renderProductImage(product);
        const badgesHtml = this.renderProductBadges(product);

        return `
            <div class="product-card ${product.is_available ? 'available' : 'unavailable'}" 
                 data-product-id="${product.id}">
                <div class="product-card-header">
                    <input type="checkbox" class="product-select" data-product-id="${product.id}">
                    <div class="product-actions">
                        <button class="btn btn-sm btn-primary" data-action="edit-product" data-product-id="${product.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm ${product.is_available ? 'btn-warning' : 'btn-success'}" 
                                data-action="toggle-availability" data-product-id="${product.id}" 
                                title="${product.is_available ? 'إخفاء' : 'إظهار'}">
                            <i class="fas ${product.is_available ? 'fa-eye-slash' : 'fa-eye'}"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" data-action="delete-product" data-product-id="${product.id}" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div class="product-image-container">
                    ${imageHtml}
                    ${badgesHtml}
                </div>
                
                <div class="product-info">
                    <div class="product-category" style="background-color: ${categoryColor}20; color: ${categoryColor};">
                        ${product.category || this.getCategoryName(product.category_id)}
                    </div>
                    
                    <h3 class="product-name">${product.name}</h3>
                    <p class="product-description">${product.description}</p>
                    
                    <div class="product-price">${product.price} جنيه</div>
                    
                    <div class="product-meta">
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span>${product.preparation_time || 0} دقيقة</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-eye"></i>
                            <span>${product.view_count || 0}</span>
                        </div>
                    </div>
                </div>
                
                <div class="product-card-footer">
                    <div class="quick-actions">
                        <button class="btn btn-primary btn-sm" data-action="edit-product" data-product-id="${product.id}">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn ${product.is_featured ? 'btn-warning' : 'btn-secondary'} btn-sm" 
                                data-action="toggle-featured" data-product-id="${product.id}">
                            <i class="fas fa-star"></i> ${product.is_featured ? 'إلغاء التمييز' : 'تمييز'}
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    renderProductImage(product) {
        if (product.image_url) {
            return `
                <img src="${product.image_url}" alt="${product.name}" class="product-image" 
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="product-icon-fallback" style="display: none;">
                    <i class="${product.icon || 'fas fa-utensils'}"></i>
                </div>
            `;
        } else {
            return `
                <div class="product-icon-fallback">
                    <i class="${product.icon || 'fas fa-utensils'}"></i>
                </div>
            `;
        }
    }

    renderProductBadges(product) {
        const badges = [];
        
        if (product.is_featured) badges.push('<span class="badge badge-featured">مميز</span>');
        if (product.is_new) badges.push('<span class="badge badge-new">جديد</span>');
        if (product.is_popular) badges.push('<span class="badge badge-popular">شائع</span>');
        if (product.discount_percentage > 0) badges.push(`<span class="badge badge-discount">خصم ${product.discount_percentage}%</span>`);
        
        return badges.length > 0 ? `<div class="product-badges">${badges.join('')}</div>` : '';
    }

    renderEmptyState() {
        return `
            <div class="empty-state">
                <i class="fas fa-utensils"></i>
                <h3>لا توجد منتجات</h3>
                <p>لم يتم العثور على منتجات تطابق المعايير المحددة</p>
                <button class="btn btn-primary" onclick="dashboardProductManager.openProductModal()">
                    <i class="fas fa-plus"></i> إضافة منتج جديد
                </button>
            </div>
        `;
    }

    getCategoryName(categoryId) {
        const category = this.categories.find(cat => cat.id === categoryId);
        return category ? category.name : 'عام';
    }

    getCategoryColor(categoryId) {
        const category = this.categories.find(cat => cat.id === categoryId);
        return category ? category.color : '#95a5a6';
    }

    updateStatistics() {
        const totalProducts = this.products.length;
        const featuredProducts = this.products.filter(p => p.is_featured).length;
        const availableProducts = this.products.filter(p => p.is_available).length;
        const averagePrice = totalProducts > 0 ? 
            Math.round(this.products.reduce((sum, p) => sum + p.price, 0) / totalProducts) : 0;

        document.getElementById('dashboardTotalProducts').textContent = totalProducts;
        document.getElementById('dashboardFeaturedProducts').textContent = featuredProducts;
        document.getElementById('dashboardAvailableProducts').textContent = availableProducts;
        document.getElementById('dashboardAveragePrice').textContent = averagePrice + ' جنيه';
    }

    openProductModal(productId = null) {
        // Create modal if it doesn't exist
        if (!document.getElementById('dashboardProductModal')) {
            this.createProductModal();
        }

        const modal = document.getElementById('dashboardProductModal');
        const form = document.getElementById('dashboardProductForm');

        // Reset form
        form.reset();

        if (productId) {
            // Edit mode
            const product = this.products.find(p => p.id === productId);
            if (product) {
                this.populateForm(product);
            }
            document.getElementById('dashboardModalTitle').textContent = 'تعديل المنتج';
        } else {
            // Add mode
            document.getElementById('dashboardModalTitle').textContent = 'إضافة منتج جديد';
        }

        modal.classList.add('active');
        this.currentProductId = productId;
    }

    createProductModal() {
        const modalHtml = `
            <div class="product-modal" id="dashboardProductModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 id="dashboardModalTitle">إضافة منتج جديد</h2>
                        <button class="modal-close" onclick="dashboardProductManager.closeProductModal()">&times;</button>
                    </div>
                    <form id="dashboardProductForm" class="product-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="dashboardProductName">اسم المنتج *</label>
                                <input type="text" id="dashboardProductName" name="name" required>
                            </div>

                            <div class="form-group">
                                <label for="dashboardProductPrice">السعر (جنيه) *</label>
                                <input type="number" id="dashboardProductPrice" name="price" min="0" step="0.01" required>
                            </div>

                            <div class="form-group full-width">
                                <label for="dashboardProductDescription">الوصف</label>
                                <textarea id="dashboardProductDescription" name="description" rows="3"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="dashboardProductCategory">الفئة *</label>
                                <select id="dashboardProductCategory" name="category_id" required>
                                    <option value="">اختر الفئة</option>
                                    ${this.categories.map(cat =>
                                        `<option value="${cat.id}">${cat.name}</option>`
                                    ).join('')}
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="dashboardProductIcon">الأيقونة</label>
                                <input type="text" id="dashboardProductIcon" name="icon" placeholder="fas fa-utensils">
                            </div>

                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="dashboardProductAvailable" name="is_available" checked>
                                    <label for="dashboardProductAvailable">متوفر للطلب</label>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="dashboardProductFeatured" name="is_featured">
                                    <label for="dashboardProductFeatured">منتج مميز</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ المنتج
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="dashboardProductManager.closeProductModal()">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Setup form submission
        const form = document.getElementById('dashboardProductForm');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleProductSubmit(e.target);
        });
    }

    populateForm(product) {
        document.getElementById('dashboardProductName').value = product.name || '';
        document.getElementById('dashboardProductDescription').value = product.description || '';
        document.getElementById('dashboardProductPrice').value = product.price || '';
        document.getElementById('dashboardProductCategory').value = product.category_id || '';
        document.getElementById('dashboardProductIcon').value = product.icon || '';
        document.getElementById('dashboardProductAvailable').checked = product.is_available !== false;
        document.getElementById('dashboardProductFeatured').checked = product.is_featured || false;
    }

    closeProductModal() {
        const modal = document.getElementById('dashboardProductModal');
        if (modal) {
            modal.classList.remove('active');
        }
        this.currentProductId = null;
    }

    async handleProductSubmit(form) {
        try {
            const formData = new FormData(form);
            const productData = {
                name: formData.get('name'),
                description: formData.get('description'),
                price: parseFloat(formData.get('price')) || 0,
                category_id: formData.get('category_id'),
                category: this.getCategoryName(formData.get('category_id')),
                icon: formData.get('icon') || 'fas fa-utensils',
                is_available: formData.get('is_available') === 'on',
                is_featured: formData.get('is_featured') === 'on'
            };

            if (this.currentProductId) {
                // Update existing product
                this.updateProduct(this.currentProductId, productData);
            } else {
                // Create new product
                this.createProduct(productData);
            }

            this.closeProductModal();
            this.renderProducts();
            this.saveProducts();

        } catch (error) {
            console.error('Error saving product:', error);
            this.showNotification('خطأ في حفظ المنتج', 'error');
        }
    }

    async createProduct(productData) {
        try {
            const newProduct = {
                id: 'p_' + Date.now(),
                ...productData,
                images: [],
                image_url: null,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            // Save to Supabase first
            if (this.supabase && this.supabase.isInitialized) {
                console.log('💾 Creating product in Supabase...');
                const result = await this.supabase.createProduct(newProduct);

                if (result.success) {
                    // Use the ID from Supabase
                    newProduct.id = result.data.id || newProduct.id;
                    console.log('✅ Product created in Supabase with ID:', newProduct.id);
                } else {
                    console.warn('⚠️ Failed to create in Supabase, using local storage');
                }
            }

            // Add to local array
            this.products.unshift(newProduct);
            this.saveToLocalStorage();
            this.showNotification('تم إضافة المنتج بنجاح', 'success');

            // Notify main site
            window.dispatchEvent(new CustomEvent('productAdded', { detail: newProduct }));

        } catch (error) {
            console.error('❌ Error creating product:', error);
            this.showNotification('خطأ في إضافة المنتج', 'error');
        }
    }

    async updateProduct(productId, productData) {
        try {
            const index = this.products.findIndex(p => p.id === productId);
            if (index === -1) {
                this.showNotification('المنتج غير موجود', 'error');
                return;
            }

            const updatedProduct = {
                ...this.products[index],
                ...productData,
                updated_at: new Date().toISOString()
            };

            // Update in Supabase first
            if (this.supabase && this.supabase.isInitialized) {
                console.log('💾 Updating product in Supabase...');
                const result = await this.supabase.updateProduct(productId, updatedProduct);

                if (result.success) {
                    console.log('✅ Product updated in Supabase');
                } else {
                    console.warn('⚠️ Failed to update in Supabase, using local storage');
                }
            }

            // Update local array
            this.products[index] = updatedProduct;
            this.saveToLocalStorage();
            this.showNotification('تم تحديث المنتج بنجاح', 'success');

            // Notify main site
            window.dispatchEvent(new CustomEvent('productUpdated', { detail: updatedProduct }));

        } catch (error) {
            console.error('❌ Error updating product:', error);
            this.showNotification('خطأ في تحديث المنتج', 'error');
        }
    }

    editProduct(productId) {
        this.openProductModal(productId);
    }

    async deleteProduct(productId) {
        if (!confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            return;
        }

        try {
            const index = this.products.findIndex(p => p.id === productId);
            if (index === -1) {
                this.showNotification('المنتج غير موجود', 'error');
                return;
            }

            const productToDelete = this.products[index];

            // Delete from Supabase first
            if (this.supabase && this.supabase.isInitialized) {
                console.log('🗑️ Deleting product from Supabase...');
                const result = await this.supabase.deleteProduct(productId);

                if (result.success) {
                    console.log('✅ Product deleted from Supabase');
                } else {
                    console.warn('⚠️ Failed to delete from Supabase, removing locally');
                }
            }

            // Remove from local array
            this.products.splice(index, 1);
            this.renderProducts();
            this.saveToLocalStorage();
            this.showNotification('تم حذف المنتج بنجاح', 'success');

            // Notify main site
            window.dispatchEvent(new CustomEvent('productDeleted', { detail: { id: productId } }));

        } catch (error) {
            console.error('❌ Error deleting product:', error);
            this.showNotification('خطأ في حذف المنتج', 'error');
        }
    }

    toggleAvailability(productId) {
        const product = this.products.find(p => p.id === productId);
        if (product) {
            product.is_available = !product.is_available;
            product.updated_at = new Date().toISOString();
            this.renderProducts();
            this.saveProducts();

            const status = product.is_available ? 'متوفر' : 'غير متوفر';
            this.showNotification(`تم تغيير حالة ${product.name} إلى ${status}`, 'success');

            // Notify main site
            window.dispatchEvent(new CustomEvent('productUpdated', { detail: product }));
        }
    }

    toggleFeatured(productId) {
        const product = this.products.find(p => p.id === productId);
        if (product) {
            product.is_featured = !product.is_featured;
            product.updated_at = new Date().toISOString();
            this.renderProducts();
            this.saveProducts();

            const status = product.is_featured ? 'مميز' : 'عادي';
            this.showNotification(`تم تغيير ${product.name} إلى ${status}`, 'success');

            // Notify main site
            window.dispatchEvent(new CustomEvent('productUpdated', { detail: product }));
        }
    }

    selectAllProducts(checked) {
        const checkboxes = document.querySelectorAll('.product-select');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const productId = checkbox.dataset.productId;
            if (checked) {
                this.selectedProducts.add(productId);
            } else {
                this.selectedProducts.delete(productId);
            }
        });
    }

    updateSelectAllState() {
        const selectAllCheckbox = document.getElementById('dashboardSelectAll');
        const checkboxes = document.querySelectorAll('.product-select');
        const checkedCount = document.querySelectorAll('.product-select:checked').length;

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkedCount === checkboxes.length && checkboxes.length > 0;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
        }
    }

    bulkDelete() {
        if (this.selectedProducts.size === 0) {
            this.showNotification('يرجى اختيار منتجات للحذف', 'warning');
            return;
        }

        if (!confirm(`هل أنت متأكد من حذف ${this.selectedProducts.size} منتج؟`)) {
            return;
        }

        this.selectedProducts.forEach(productId => {
            const index = this.products.findIndex(p => p.id === productId);
            if (index !== -1) {
                this.products.splice(index, 1);
            }
        });

        this.selectedProducts.clear();
        this.renderProducts();
        this.saveProducts();
        this.showNotification('تم حذف المنتجات المحددة بنجاح', 'success');
    }

    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.dashboard-notification');
        existingNotifications.forEach(notification => notification.remove());

        const notification = document.createElement('div');
        notification.className = `dashboard-notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas ${type === 'success' ? 'fa-check-circle' :
                           type === 'error' ? 'fa-exclamation-circle' :
                           type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#d4edda' :
                        type === 'error' ? '#f8d7da' :
                        type === 'warning' ? '#fff3cd' : '#d1ecf1'};
            color: ${type === 'success' ? '#155724' :
                    type === 'error' ? '#721c24' :
                    type === 'warning' ? '#856404' : '#0c5460'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' :
                               type === 'error' ? '#f5c6cb' :
                               type === 'warning' ? '#ffeaa7' : '#bee5eb'};
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 300px;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    setupDatabaseStatusMonitoring() {
        this.updateDatabaseStatus();

        // Check status every 30 seconds
        setInterval(() => {
            this.updateDatabaseStatus();
        }, 30000);
    }

    updateDatabaseStatus() {
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');

        if (!statusIndicator || !statusText) return;

        if (this.supabase && this.supabase.isInitialized) {
            // Test connection
            this.testDatabaseConnection().then(isConnected => {
                if (isConnected) {
                    statusIndicator.className = 'status-indicator connected';
                    statusText.textContent = 'متصل بقاعدة البيانات';
                } else {
                    statusIndicator.className = 'status-indicator disconnected';
                    statusText.textContent = 'انقطع الاتصال';
                }
            });
        } else {
            statusIndicator.className = 'status-indicator disconnected';
            statusText.textContent = 'غير متصل';
        }
    }

    async testDatabaseConnection() {
        try {
            if (!this.supabase || !this.supabase.isInitialized) {
                return false;
            }

            // Simple test query
            const result = await this.supabase.client
                .from('products')
                .select('id')
                .limit(1);

            return !result.error;
        } catch (error) {
            console.error('Database connection test failed:', error);
            return false;
        }
    }

    // Database sync methods
    async syncAllProductsToDatabase() {
        if (!this.supabase || !this.supabase.isInitialized) {
            this.showNotification('قاعدة البيانات غير متصلة', 'error');
            return;
        }

        try {
            this.showNotification('جاري مزامنة المنتجات...', 'info');

            for (const product of this.products) {
                await this.supabase.upsertProduct(product);
            }

            this.showNotification('تم مزامنة جميع المنتجات بنجاح', 'success');

        } catch (error) {
            console.error('Error syncing products:', error);
            this.showNotification('خطأ في مزامنة المنتجات', 'error');
        }
    }

    async refreshFromDatabase() {
        if (!this.supabase || !this.supabase.isInitialized) {
            this.showNotification('قاعدة البيانات غير متصلة', 'error');
            return;
        }

        try {
            this.showNotification('جاري تحديث البيانات...', 'info');
            await this.loadProductsFromDatabase();
            this.renderProducts();
            this.updateStatistics();
            this.showNotification('تم تحديث البيانات بنجاح', 'success');

        } catch (error) {
            console.error('Error refreshing from database:', error);
            this.showNotification('خطأ في تحديث البيانات', 'error');
        }
    }
}

// Initialize
const dashboardProductManager = new DashboardProductManager();
window.dashboardProductManager = dashboardProductManager;

console.log('✅ DashboardProductManager loaded');
