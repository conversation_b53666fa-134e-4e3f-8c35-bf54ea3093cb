// Menu Management System

class MenuManager {
    constructor() {
        this.currentEditingItem = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadMenuData();
    }

    bindEvents() {
        // Item form submission
        const itemForm = document.getElementById('itemForm');
        if (itemForm) {
            itemForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveMenuItem();
            });
        }

        // Modal close events
        document.addEventListener('click', (e) => {
            if (e.target.closest('.close-modal') || e.target.id === 'itemModal') {
                this.closeItemModal();
            }
        });

        // Escape key to close modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeItemModal();
            }
        });
    }

    loadMenuData() {
        // Load menu data from localStorage if available
        const savedMenu = localStorage.getItem('restaurant_menu_data');
        if (savedMenu) {
            try {
                const menuData = JSON.parse(savedMenu);
                // Update global menuData
                Object.assign(window.menuData, menuData);
            } catch (error) {
                console.error('Error loading menu data:', error);
            }
        }
    }

    saveMenuData() {
        try {
            localStorage.setItem('restaurant_menu_data', JSON.stringify(window.menuData));
        } catch (error) {
            console.error('Error saving menu data:', error);
        }
    }

    showAddItemModal() {
        this.currentEditingItem = null;
        this.resetForm();
        
        document.getElementById('modalTitle').textContent = 'إضافة عنصر جديد';
        document.getElementById('itemModal').classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    editItem(itemId) {
        const item = getItemById(itemId);
        if (!item) {
            this.showMessage('العنصر غير موجود', 'error');
            return;
        }

        this.currentEditingItem = item;
        this.populateForm(item);
        
        document.getElementById('modalTitle').textContent = 'تعديل العنصر';
        document.getElementById('itemModal').classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    closeItemModal() {
        document.getElementById('itemModal').classList.remove('show');
        document.body.style.overflow = '';
        this.resetForm();
        this.currentEditingItem = null;
    }

    resetForm() {
        const form = document.getElementById('itemForm');
        if (form) {
            form.reset();
            document.getElementById('itemAvailable').checked = true;
        }
    }

    populateForm(item) {
        document.getElementById('itemName').value = item.name;
        document.getElementById('itemDescription').value = item.description;
        document.getElementById('itemPrice').value = item.price;
        document.getElementById('itemCategory').value = item.category;
        document.getElementById('itemIcon').value = item.icon;
        document.getElementById('itemAvailable').checked = item.available;
    }

    saveMenuItem() {
        const formData = this.getFormData();
        
        if (!this.validateFormData(formData)) {
            return;
        }

        if (this.currentEditingItem) {
            // Update existing item
            this.updateMenuItem(formData);
        } else {
            // Add new item
            this.addMenuItem(formData);
        }

        this.closeItemModal();
        this.refreshMenuDisplay();
    }

    getFormData() {
        return {
            name: document.getElementById('itemName').value.trim(),
            description: document.getElementById('itemDescription').value.trim(),
            price: parseFloat(document.getElementById('itemPrice').value),
            category: document.getElementById('itemCategory').value,
            icon: document.getElementById('itemIcon').value,
            available: document.getElementById('itemAvailable').checked
        };
    }

    validateFormData(data) {
        if (!data.name) {
            this.showMessage('يرجى إدخال اسم العنصر', 'error');
            return false;
        }

        if (!data.description) {
            this.showMessage('يرجى إدخال وصف العنصر', 'error');
            return false;
        }

        if (!data.price || data.price <= 0) {
            this.showMessage('يرجى إدخال سعر صحيح', 'error');
            return false;
        }

        if (!data.category) {
            this.showMessage('يرجى اختيار القسم', 'error');
            return false;
        }

        if (!data.icon) {
            this.showMessage('يرجى اختيار الأيقونة', 'error');
            return false;
        }

        return true;
    }

    addMenuItem(data) {
        // Generate unique ID
        const id = this.generateItemId(data.category);
        
        const newItem = {
            id: id,
            name: data.name,
            description: data.description,
            price: data.price,
            category: data.category,
            icon: data.icon,
            available: data.available
        };

        // Add to menuData
        if (!window.menuData[data.category]) {
            window.menuData[data.category] = [];
        }
        
        window.menuData[data.category].push(newItem);
        
        this.saveMenuData();
        this.showMessage(`تم إضافة ${data.name} بنجاح`, 'success');
    }

    updateMenuItem(data) {
        const item = this.currentEditingItem;
        const category = item.category;
        
        // Update item properties
        item.name = data.name;
        item.description = data.description;
        item.price = data.price;
        item.icon = data.icon;
        item.available = data.available;
        
        // If category changed, move item to new category
        if (item.category !== data.category) {
            // Remove from old category
            const oldCategoryItems = window.menuData[category];
            const itemIndex = oldCategoryItems.findIndex(i => i.id === item.id);
            if (itemIndex > -1) {
                oldCategoryItems.splice(itemIndex, 1);
            }
            
            // Add to new category
            item.category = data.category;
            if (!window.menuData[data.category]) {
                window.menuData[data.category] = [];
            }
            window.menuData[data.category].push(item);
        }
        
        this.saveMenuData();
        this.showMessage(`تم تحديث ${data.name} بنجاح`, 'success');
    }

    generateItemId(category) {
        const prefix = {
            'appetizers': 'app',
            'main-dishes': 'main',
            'beverages': 'bev',
            'desserts': 'des'
        }[category] || 'item';
        
        const existingItems = window.menuData[category] || [];
        const maxId = existingItems.reduce((max, item) => {
            const num = parseInt(item.id.replace(prefix, ''));
            return isNaN(num) ? max : Math.max(max, num);
        }, 0);
        
        return `${prefix}${maxId + 1}`;
    }

    toggleAvailability(itemId) {
        const item = getItemById(itemId);
        if (!item) {
            this.showMessage('العنصر غير موجود', 'error');
            return;
        }

        item.available = !item.available;
        this.saveMenuData();
        this.refreshMenuDisplay();
        
        const status = item.available ? 'متوفر' : 'غير متوفر';
        this.showMessage(`تم تغيير حالة ${item.name} إلى ${status}`, 'success');
    }

    deleteItem(itemId) {
        const item = getItemById(itemId);
        if (!item) {
            this.showMessage('العنصر غير موجود', 'error');
            return;
        }

        if (!confirm(`هل أنت متأكد من حذف "${item.name}"؟\nلا يمكن التراجع عن هذا الإجراء.`)) {
            return;
        }

        // Remove from menuData
        const categoryItems = window.menuData[item.category];
        const itemIndex = categoryItems.findIndex(i => i.id === itemId);
        
        if (itemIndex > -1) {
            categoryItems.splice(itemIndex, 1);
            this.saveMenuData();
            this.refreshMenuDisplay();
            this.showMessage(`تم حذف ${item.name} بنجاح`, 'success');
        }
    }

    refreshMenuDisplay() {
        // Refresh the menu table in dashboard
        if (window.dashboard) {
            window.dashboard.renderMenuTable();
            window.dashboard.stats.totalMenuItems = getAllMenuItems().length;
            window.dashboard.updateStats();
        }

        // Refresh the customer menu if on the same page
        if (window.app) {
            window.app.renderMenu();
        }
    }

    // Bulk operations
    bulkToggleAvailability(itemIds, available) {
        let updatedCount = 0;
        
        itemIds.forEach(itemId => {
            const item = getItemById(itemId);
            if (item) {
                item.available = available;
                updatedCount++;
            }
        });
        
        if (updatedCount > 0) {
            this.saveMenuData();
            this.refreshMenuDisplay();
            const status = available ? 'متوفرة' : 'غير متوفرة';
            this.showMessage(`تم تحديث ${updatedCount} عنصر إلى ${status}`, 'success');
        }
    }

    bulkDelete(itemIds) {
        if (!confirm(`هل أنت متأكد من حذف ${itemIds.length} عنصر؟\nلا يمكن التراجع عن هذا الإجراء.`)) {
            return;
        }

        let deletedCount = 0;
        
        itemIds.forEach(itemId => {
            const item = getItemById(itemId);
            if (item) {
                const categoryItems = window.menuData[item.category];
                const itemIndex = categoryItems.findIndex(i => i.id === itemId);
                
                if (itemIndex > -1) {
                    categoryItems.splice(itemIndex, 1);
                    deletedCount++;
                }
            }
        });
        
        if (deletedCount > 0) {
            this.saveMenuData();
            this.refreshMenuDisplay();
            this.showMessage(`تم حذف ${deletedCount} عنصر بنجاح`, 'success');
        }
    }

    // Import/Export functionality
    exportMenu() {
        try {
            const menuJson = JSON.stringify(window.menuData, null, 2);
            const blob = new Blob([menuJson], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `menu-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.showMessage('تم تصدير القائمة بنجاح', 'success');
        } catch (error) {
            this.showMessage('حدث خطأ أثناء تصدير القائمة', 'error');
        }
    }

    importMenu(file) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            try {
                const importedData = JSON.parse(e.target.result);
                
                if (this.validateMenuData(importedData)) {
                    if (confirm('هل أنت متأكد من استيراد القائمة الجديدة؟\nسيتم استبدال القائمة الحالية.')) {
                        Object.assign(window.menuData, importedData);
                        this.saveMenuData();
                        this.refreshMenuDisplay();
                        this.showMessage('تم استيراد القائمة بنجاح', 'success');
                    }
                } else {
                    this.showMessage('ملف القائمة غير صحيح', 'error');
                }
            } catch (error) {
                this.showMessage('حدث خطأ أثناء قراءة الملف', 'error');
            }
        };
        
        reader.readAsText(file);
    }

    validateMenuData(data) {
        // Basic validation for menu data structure
        if (typeof data !== 'object' || data === null) {
            return false;
        }

        const requiredCategories = ['appetizers', 'main-dishes', 'beverages', 'desserts'];
        
        for (const category of requiredCategories) {
            if (!Array.isArray(data[category])) {
                continue; // Category can be missing
            }
            
            for (const item of data[category]) {
                if (!item.id || !item.name || !item.description || 
                    typeof item.price !== 'number' || !item.category || !item.icon) {
                    return false;
                }
            }
        }
        
        return true;
    }

    showMessage(message, type = 'success') {
        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message show`;
        messageDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            ${message}
        `;
        
        // Add to page
        document.body.appendChild(messageDiv);
        
        // Position message
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '20px';
        messageDiv.style.right = '20px';
        messageDiv.style.zIndex = '9999';
        messageDiv.style.maxWidth = '300px';
        messageDiv.style.padding = '15px';
        messageDiv.style.borderRadius = '10px';
        messageDiv.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
        
        // Remove after 3 seconds
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }
}

// Initialize menu manager
const menuManager = new MenuManager();

// Global functions for dashboard integration
function showAddItemModal() {
    menuManager.showAddItemModal();
}

function closeItemModal() {
    menuManager.closeItemModal();
}

// Make menuManager available globally
window.menuManager = menuManager;
