// Database Synchronization System
// نظام مزامنة قواعد البيانات

class DatabaseSyncManager {
    constructor() {
        this.supabase = null;
        this.firebase = null;
        this.isOnline = navigator.onLine;
        this.syncQueue = [];
        this.lastSyncTime = null;
        this.syncInProgress = false;
        
        this.init();
    }

    async init() {
        try {
            // Wait for database managers to be ready
            await this.waitForDatabases();
            
            // Setup online/offline listeners
            this.setupNetworkListeners();
            
            // Setup periodic sync
            this.setupPeriodicSync();
            
            // Load last sync time
            this.loadLastSyncTime();
            
            // Initial sync if online
            if (this.isOnline) {
                await this.performFullSync();
            }
            
            console.log('Database sync manager initialized');
            
        } catch (error) {
            console.error('Error initializing database sync manager:', error);
        }
    }

    async waitForDatabases() {
        return new Promise((resolve) => {
            const checkDatabases = () => {
                if (window.supabaseManager?.isInitialized && window.firebaseManager?.isInitialized) {
                    this.supabase = window.supabaseManager;
                    this.firebase = window.firebaseManager;
                    resolve();
                } else {
                    setTimeout(checkDatabases, 100);
                }
            };
            checkDatabases();
        });
    }

    setupNetworkListeners() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            console.log('Network: Online');
            this.performFullSync();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            console.log('Network: Offline');
        });
    }

    setupPeriodicSync() {
        // Sync every 5 minutes when online
        setInterval(() => {
            if (this.isOnline && !this.syncInProgress) {
                this.performIncrementalSync();
            }
        }, 5 * 60 * 1000);
    }

    loadLastSyncTime() {
        const stored = localStorage.getItem('last_sync_time');
        if (stored) {
            this.lastSyncTime = new Date(stored);
        }
    }

    saveLastSyncTime() {
        this.lastSyncTime = new Date();
        localStorage.setItem('last_sync_time', this.lastSyncTime.toISOString());
    }

    // Main sync methods
    async performFullSync() {
        if (this.syncInProgress) return;
        
        try {
            this.syncInProgress = true;
            console.log('Starting full sync...');
            
            // Sync categories
            await this.syncCategories();
            
            // Sync products
            await this.syncProducts();
            
            // Sync orders (from local to remote only)
            await this.syncOrders();
            
            // Process sync queue
            await this.processSyncQueue();
            
            this.saveLastSyncTime();
            console.log('Full sync completed');
            
        } catch (error) {
            console.error('Error during full sync:', error);
        } finally {
            this.syncInProgress = false;
        }
    }

    async performIncrementalSync() {
        if (this.syncInProgress || !this.lastSyncTime) {
            return this.performFullSync();
        }
        
        try {
            this.syncInProgress = true;
            console.log('Starting incremental sync...');
            
            // Sync only changed data since last sync
            await this.syncChangedData();
            
            // Process sync queue
            await this.processSyncQueue();
            
            this.saveLastSyncTime();
            console.log('Incremental sync completed');
            
        } catch (error) {
            console.error('Error during incremental sync:', error);
        } finally {
            this.syncInProgress = false;
        }
    }

    // Categories sync
    async syncCategories() {
        try {
            // Get categories from Supabase
            const supabaseResult = await this.supabase.getCategories();
            
            if (supabaseResult.success) {
                // Update local storage
                localStorage.setItem('restaurant_categories', JSON.stringify(supabaseResult.data));
                
                // Sync to Firebase if needed
                await this.syncCategoriesToFirebase(supabaseResult.data);
                
                console.log('Categories synced successfully');
            }
        } catch (error) {
            console.error('Error syncing categories:', error);
        }
    }

    async syncCategoriesToFirebase(categories) {
        try {
            const batch = this.firebase.createBatch();
            
            categories.forEach(category => {
                const docRef = this.firebase.firestore.collection('categories').doc(category.id);
                batch.set(docRef, category, { merge: true });
            });
            
            await this.firebase.commitBatch(batch);
        } catch (error) {
            console.error('Error syncing categories to Firebase:', error);
        }
    }

    // Products sync
    async syncProducts() {
        try {
            // Get products from Supabase
            const supabaseResult = await this.supabase.getProducts();
            
            if (supabaseResult.success) {
                // Update local storage
                localStorage.setItem('restaurant_products', JSON.stringify(supabaseResult.data));
                
                // Sync to Firebase if needed
                await this.syncProductsToFirebase(supabaseResult.data);
                
                console.log('Products synced successfully');
            }
        } catch (error) {
            console.error('Error syncing products:', error);
        }
    }

    async syncProductsToFirebase(products) {
        try {
            const batch = this.firebase.createBatch();
            
            products.forEach(product => {
                const docRef = this.firebase.firestore.collection('products').doc(product.id);
                batch.set(docRef, product, { merge: true });
            });
            
            await this.firebase.commitBatch(batch);
        } catch (error) {
            console.error('Error syncing products to Firebase:', error);
        }
    }

    // Orders sync
    async syncOrders() {
        try {
            // Get pending orders from local storage
            const localOrders = JSON.parse(localStorage.getItem('pending_orders') || '[]');
            
            for (const order of localOrders) {
                await this.syncOrderToRemote(order);
            }
            
            // Clear pending orders after successful sync
            localStorage.setItem('pending_orders', '[]');
            
            console.log('Orders synced successfully');
        } catch (error) {
            console.error('Error syncing orders:', error);
        }
    }

    async syncOrderToRemote(order) {
        try {
            // Save to Supabase
            const supabaseResult = await this.supabase.createOrder(order);
            
            if (supabaseResult.success) {
                // Save to Firebase for analytics
                await this.firebase.addDocument('orders', {
                    ...order,
                    supabase_id: supabaseResult.data.id
                });
                
                console.log('Order synced:', order.id);
            }
        } catch (error) {
            console.error('Error syncing order to remote:', error);
            // Add back to sync queue for retry
            this.addToSyncQueue('order', order);
        }
    }

    // Changed data sync
    async syncChangedData() {
        try {
            const since = this.lastSyncTime.toISOString();
            
            // Get changed products
            const productsResult = await this.supabase.getProducts({
                date_from: since
            });
            
            if (productsResult.success && productsResult.data.length > 0) {
                const existingProducts = JSON.parse(localStorage.getItem('restaurant_products') || '[]');
                
                // Merge changed products
                productsResult.data.forEach(changedProduct => {
                    const index = existingProducts.findIndex(p => p.id === changedProduct.id);
                    if (index > -1) {
                        existingProducts[index] = changedProduct;
                    } else {
                        existingProducts.push(changedProduct);
                    }
                });
                
                localStorage.setItem('restaurant_products', JSON.stringify(existingProducts));
            }
            
            // Get changed categories
            const categoriesResult = await this.supabase.getCategories();
            if (categoriesResult.success) {
                localStorage.setItem('restaurant_categories', JSON.stringify(categoriesResult.data));
            }
            
        } catch (error) {
            console.error('Error syncing changed data:', error);
        }
    }

    // Sync queue management
    addToSyncQueue(type, data) {
        this.syncQueue.push({
            id: Date.now().toString(),
            type: type,
            data: data,
            timestamp: new Date().toISOString(),
            retries: 0
        });
        
        // Save queue to localStorage
        localStorage.setItem('sync_queue', JSON.stringify(this.syncQueue));
    }

    async processSyncQueue() {
        if (this.syncQueue.length === 0) {
            // Load queue from localStorage
            this.syncQueue = JSON.parse(localStorage.getItem('sync_queue') || '[]');
        }
        
        const maxRetries = 3;
        const processedItems = [];
        
        for (const item of this.syncQueue) {
            try {
                let success = false;
                
                switch (item.type) {
                    case 'order':
                        const result = await this.syncOrderToRemote(item.data);
                        success = true;
                        break;
                    case 'product':
                        await this.syncProductUpdate(item.data);
                        success = true;
                        break;
                    case 'category':
                        await this.syncCategoryUpdate(item.data);
                        success = true;
                        break;
                }
                
                if (success) {
                    processedItems.push(item.id);
                }
                
            } catch (error) {
                console.error('Error processing sync queue item:', error);
                item.retries = (item.retries || 0) + 1;
                
                if (item.retries >= maxRetries) {
                    console.error('Max retries reached for sync item:', item.id);
                    processedItems.push(item.id);
                }
            }
        }
        
        // Remove processed items
        this.syncQueue = this.syncQueue.filter(item => !processedItems.includes(item.id));
        localStorage.setItem('sync_queue', JSON.stringify(this.syncQueue));
    }

    // Offline data management
    async saveOrderOffline(orderData) {
        try {
            const pendingOrders = JSON.parse(localStorage.getItem('pending_orders') || '[]');
            
            const order = {
                ...orderData,
                id: Date.now().toString(),
                offline_created: true,
                created_at: new Date().toISOString()
            };
            
            pendingOrders.push(order);
            localStorage.setItem('pending_orders', JSON.stringify(pendingOrders));
            
            // Add to sync queue
            this.addToSyncQueue('order', order);
            
            return { success: true, data: order };
        } catch (error) {
            console.error('Error saving order offline:', error);
            return { success: false, error: error.message };
        }
    }

    async saveProductOffline(productData) {
        try {
            const products = JSON.parse(localStorage.getItem('restaurant_products') || '[]');
            
            if (productData.id) {
                // Update existing product
                const index = products.findIndex(p => p.id === productData.id);
                if (index > -1) {
                    products[index] = { ...products[index], ...productData };
                }
            } else {
                // Create new product
                productData.id = Date.now().toString();
                productData.offline_created = true;
                products.unshift(productData);
            }
            
            localStorage.setItem('restaurant_products', JSON.stringify(products));
            
            // Add to sync queue
            this.addToSyncQueue('product', productData);
            
            return { success: true, data: productData };
        } catch (error) {
            console.error('Error saving product offline:', error);
            return { success: false, error: error.message };
        }
    }

    // Conflict resolution
    async resolveConflicts() {
        try {
            // Get local and remote data
            const localProducts = JSON.parse(localStorage.getItem('restaurant_products') || '[]');
            const remoteResult = await this.supabase.getProducts();
            
            if (!remoteResult.success) return;
            
            const remoteProducts = remoteResult.data;
            const conflicts = [];
            
            // Find conflicts
            localProducts.forEach(localProduct => {
                const remoteProduct = remoteProducts.find(p => p.id === localProduct.id);
                
                if (remoteProduct) {
                    const localTime = new Date(localProduct.updated_at || localProduct.created_at);
                    const remoteTime = new Date(remoteProduct.updated_at || remoteProduct.created_at);
                    
                    if (localTime.getTime() !== remoteTime.getTime()) {
                        conflicts.push({
                            id: localProduct.id,
                            local: localProduct,
                            remote: remoteProduct,
                            localTime: localTime,
                            remoteTime: remoteTime
                        });
                    }
                }
            });
            
            // Resolve conflicts (use most recent)
            conflicts.forEach(conflict => {
                const useRemote = conflict.remoteTime > conflict.localTime;
                
                if (useRemote) {
                    const index = localProducts.findIndex(p => p.id === conflict.id);
                    if (index > -1) {
                        localProducts[index] = conflict.remote;
                    }
                } else {
                    // Local is newer, add to sync queue
                    this.addToSyncQueue('product', conflict.local);
                }
            });
            
            localStorage.setItem('restaurant_products', JSON.stringify(localProducts));
            
        } catch (error) {
            console.error('Error resolving conflicts:', error);
        }
    }

    // Status and monitoring
    getSyncStatus() {
        return {
            isOnline: this.isOnline,
            syncInProgress: this.syncInProgress,
            lastSyncTime: this.lastSyncTime,
            queueLength: this.syncQueue.length,
            pendingOrders: JSON.parse(localStorage.getItem('pending_orders') || '[]').length
        };
    }

    async forceSyncNow() {
        if (this.isOnline) {
            await this.performFullSync();
            return { success: true, message: 'Sync completed' };
        } else {
            return { success: false, message: 'No internet connection' };
        }
    }

    clearSyncData() {
        this.syncQueue = [];
        localStorage.removeItem('sync_queue');
        localStorage.removeItem('pending_orders');
        localStorage.removeItem('last_sync_time');
        this.lastSyncTime = null;
    }
}

// Initialize Database Sync Manager
let databaseSyncManager;

// Wait for DOM to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        databaseSyncManager = new DatabaseSyncManager();
    });
} else {
    databaseSyncManager = new DatabaseSyncManager();
}

// Make available globally
window.databaseSyncManager = databaseSyncManager;

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DatabaseSyncManager;
}
