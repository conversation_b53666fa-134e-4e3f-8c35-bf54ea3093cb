// Performance Optimization System
// نظام تحسين الأداء

class PerformanceOptimizer {
    constructor() {
        this.metrics = {
            loadTime: 0,
            renderTime: 0,
            interactionTime: 0,
            memoryUsage: 0,
            networkRequests: 0
        };
        
        this.observers = {};
        this.cache = new Map();
        this.maxCacheSize = 50;
        
        this.init();
    }

    init() {
        this.measureLoadTime();
        this.setupPerformanceObservers();
        this.setupImageLazyLoading();
        this.setupResourceCaching();
        this.optimizeScrollPerformance();
        this.startPerformanceMonitoring();
    }

    // Measure page load time
    measureLoadTime() {
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            this.metrics.loadTime = loadTime;
            
            // Log performance metrics
            this.logPerformanceMetrics();
        });
    }

    // Setup performance observers
    setupPerformanceObservers() {
        // Observe paint timing
        if ('PerformanceObserver' in window) {
            try {
                const paintObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (entry.name === 'first-contentful-paint') {
                            this.metrics.renderTime = entry.startTime;
                        }
                    });
                });
                paintObserver.observe({ entryTypes: ['paint'] });
                this.observers.paint = paintObserver;
            } catch (error) {
                console.warn('Paint observer not supported:', error);
            }

            // Observe largest contentful paint
            try {
                const lcpObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    this.metrics.largestContentfulPaint = lastEntry.startTime;
                });
                lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
                this.observers.lcp = lcpObserver;
            } catch (error) {
                console.warn('LCP observer not supported:', error);
            }

            // Observe layout shifts
            try {
                const clsObserver = new PerformanceObserver((list) => {
                    let clsValue = 0;
                    list.getEntries().forEach(entry => {
                        if (!entry.hadRecentInput) {
                            clsValue += entry.value;
                        }
                    });
                    this.metrics.cumulativeLayoutShift = clsValue;
                });
                clsObserver.observe({ entryTypes: ['layout-shift'] });
                this.observers.cls = clsObserver;
            } catch (error) {
                console.warn('CLS observer not supported:', error);
            }
        }
    }

    // Setup image lazy loading
    setupImageLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                        }
                        
                        if (img.dataset.srcset) {
                            img.srcset = img.dataset.srcset;
                            img.removeAttribute('data-srcset');
                        }
                        
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // Observe all lazy images
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });

            this.observers.image = imageObserver;
        }
    }

    // Setup resource caching
    setupResourceCaching() {
        // Cache API responses
        const originalFetch = window.fetch;
        
        window.fetch = async (...args) => {
            const [url, options = {}] = args;
            
            // Only cache GET requests
            if (options.method && options.method !== 'GET') {
                return originalFetch(...args);
            }

            // Check cache first
            const cacheKey = this.getCacheKey(url, options);
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                
                // Check if cache is still valid (5 minutes)
                if (Date.now() - cached.timestamp < 5 * 60 * 1000) {
                    return Promise.resolve(new Response(JSON.stringify(cached.data), {
                        status: 200,
                        headers: { 'Content-Type': 'application/json' }
                    }));
                } else {
                    this.cache.delete(cacheKey);
                }
            }

            // Fetch and cache
            try {
                const response = await originalFetch(...args);
                
                if (response.ok && response.headers.get('content-type')?.includes('application/json')) {
                    const clonedResponse = response.clone();
                    const data = await clonedResponse.json();
                    
                    this.addToCache(cacheKey, data);
                }
                
                return response;
            } catch (error) {
                throw error;
            }
        };
    }

    // Optimize scroll performance
    optimizeScrollPerformance() {
        let ticking = false;
        
        const optimizedScrollHandler = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    // Handle scroll events here
                    this.handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        };

        window.addEventListener('scroll', optimizedScrollHandler, { passive: true });
    }

    // Handle scroll events
    handleScroll() {
        // Implement scroll-based optimizations
        const scrollTop = window.pageYOffset;
        
        // Show/hide scroll to top button
        const scrollToTopBtn = document.getElementById('scrollToTop');
        if (scrollToTopBtn) {
            if (scrollTop > 300) {
                scrollToTopBtn.classList.add('show');
            } else {
                scrollToTopBtn.classList.remove('show');
            }
        }

        // Lazy load images that come into view
        this.checkLazyImages();
    }

    // Check lazy images
    checkLazyImages() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        const windowHeight = window.innerHeight;
        const scrollTop = window.pageYOffset;

        lazyImages.forEach(img => {
            const rect = img.getBoundingClientRect();
            if (rect.top < windowHeight + 100) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                img.classList.remove('lazy');
            }
        });
    }

    // Start performance monitoring
    startPerformanceMonitoring() {
        // Monitor memory usage
        if ('memory' in performance) {
            setInterval(() => {
                this.metrics.memoryUsage = performance.memory.usedJSHeapSize;
            }, 30000); // Every 30 seconds
        }

        // Monitor network requests
        this.monitorNetworkRequests();
        
        // Report performance metrics periodically
        setInterval(() => {
            this.reportPerformanceMetrics();
        }, 60000); // Every minute
    }

    // Monitor network requests
    monitorNetworkRequests() {
        if ('PerformanceObserver' in window) {
            try {
                const resourceObserver = new PerformanceObserver((list) => {
                    this.metrics.networkRequests += list.getEntries().length;
                });
                resourceObserver.observe({ entryTypes: ['resource'] });
                this.observers.resource = resourceObserver;
            } catch (error) {
                console.warn('Resource observer not supported:', error);
            }
        }
    }

    // Cache management
    getCacheKey(url, options) {
        return `${url}_${JSON.stringify(options)}`;
    }

    addToCache(key, data) {
        // Remove oldest entries if cache is full
        if (this.cache.size >= this.maxCacheSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }

        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    clearCache() {
        this.cache.clear();
    }

    // Image optimization
    optimizeImage(img, options = {}) {
        const {
            quality = 0.8,
            maxWidth = 1200,
            maxHeight = 1200,
            format = 'webp'
        } = options;

        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            img.onload = () => {
                const { width, height } = img;
                let newWidth = width;
                let newHeight = height;

                // Calculate new dimensions
                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    newWidth = width * ratio;
                    newHeight = height * ratio;
                }

                canvas.width = newWidth;
                canvas.height = newHeight;

                // Draw and optimize
                ctx.drawImage(img, 0, 0, newWidth, newHeight);
                
                canvas.toBlob(resolve, `image/${format}`, quality);
            };
        });
    }

    // Debounce function for performance
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Throttle function for performance
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Log performance metrics
    logPerformanceMetrics() {
        const metrics = {
            ...this.metrics,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            connection: navigator.connection ? {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt
            } : null
        };

        // Store metrics locally
        try {
            const storedMetrics = JSON.parse(localStorage.getItem('performance_metrics') || '[]');
            storedMetrics.push(metrics);
            
            // Keep only last 20 metrics
            if (storedMetrics.length > 20) {
                storedMetrics.splice(0, storedMetrics.length - 20);
            }
            
            localStorage.setItem('performance_metrics', JSON.stringify(storedMetrics));
        } catch (error) {
            console.error('Failed to store performance metrics:', error);
        }

        console.log('Performance Metrics:', metrics);
    }

    // Report performance metrics
    reportPerformanceMetrics() {
        const metrics = this.getPerformanceReport();
        
        // In production, send to analytics service
        if (window.location.hostname !== 'localhost') {
            this.sendMetricsToService(metrics);
        }
    }

    // Get performance report
    getPerformanceReport() {
        return {
            loadTime: this.metrics.loadTime,
            renderTime: this.metrics.renderTime,
            memoryUsage: this.metrics.memoryUsage,
            networkRequests: this.metrics.networkRequests,
            cacheHitRate: this.getCacheHitRate(),
            timestamp: new Date().toISOString()
        };
    }

    // Calculate cache hit rate
    getCacheHitRate() {
        // This would be calculated based on cache hits vs misses
        return 0; // Placeholder
    }

    // Send metrics to service
    sendMetricsToService(metrics) {
        // Placeholder for sending metrics to analytics service
        console.log('Sending metrics to service:', metrics);
    }

    // Cleanup observers
    cleanup() {
        Object.values(this.observers).forEach(observer => {
            if (observer && observer.disconnect) {
                observer.disconnect();
            }
        });
    }

    // Get performance score
    getPerformanceScore() {
        let score = 100;
        
        // Deduct points based on metrics
        if (this.metrics.loadTime > 3000) score -= 20;
        if (this.metrics.renderTime > 2000) score -= 15;
        if (this.metrics.largestContentfulPaint > 2500) score -= 15;
        if (this.metrics.cumulativeLayoutShift > 0.1) score -= 10;
        if (this.metrics.memoryUsage > 50 * 1024 * 1024) score -= 10; // 50MB
        
        return Math.max(0, score);
    }
}

// Initialize performance optimizer
const performanceOptimizer = new PerformanceOptimizer();

// Global performance utilities
window.optimizePerformance = {
    debounce: performanceOptimizer.debounce.bind(performanceOptimizer),
    throttle: performanceOptimizer.throttle.bind(performanceOptimizer),
    optimizeImage: performanceOptimizer.optimizeImage.bind(performanceOptimizer),
    clearCache: performanceOptimizer.clearCache.bind(performanceOptimizer),
    getReport: performanceOptimizer.getPerformanceReport.bind(performanceOptimizer),
    getScore: performanceOptimizer.getPerformanceScore.bind(performanceOptimizer)
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
}

// Make available globally
window.performanceOptimizer = performanceOptimizer;
