// Firestore Security Rules for Mohamed <PERSON>
// قواعد الأمان لقاعدة بيانات Firestore

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             request.auth.token.admin == true;
    }
    
    function isManager() {
      return isAuthenticated() && 
             (request.auth.token.admin == true || 
              request.auth.token.manager == true);
    }
    
    function isOwner(userId) {
      return isAuthenticated() && 
             request.auth.uid == userId;
    }
    
    function isValidEmail(email) {
      return email.matches('.*@.*\\..*');
    }
    
    function isValidPhone(phone) {
      return phone.matches('^(\\+20|0)?1[0125]\\d{8}$');
    }
    
    // Products collection - public read, admin write
    match /products/{productId} {
      allow read: if true; // Public read access
      allow write: if isAdmin();
      
      // Validate product data
      allow create, update: if isAdmin() &&
        resource.data.keys().hasAll(['name', 'description', 'price', 'category']) &&
        resource.data.name is string &&
        resource.data.name.size() > 0 &&
        resource.data.description is string &&
        resource.data.description.size() > 0 &&
        resource.data.price is number &&
        resource.data.price > 0 &&
        resource.data.category is string;
    }
    
    // Categories collection - public read, admin write
    match /categories/{categoryId} {
      allow read: if true; // Public read access
      allow write: if isAdmin();
      
      // Validate category data
      allow create, update: if isAdmin() &&
        resource.data.keys().hasAll(['name']) &&
        resource.data.name is string &&
        resource.data.name.size() > 0;
    }
    
    // Orders collection - restricted access
    match /orders/{orderId} {
      // Customers can create orders
      allow create: if true;
      
      // Only admins and managers can read all orders
      allow read: if isManager();
      
      // Only admins can update/delete orders
      allow update, delete: if isAdmin();
      
      // Validate order data on creation
      allow create: if 
        resource.data.keys().hasAll(['customerName', 'customerPhone', 'customerAddress', 'items', 'total']) &&
        resource.data.customerName is string &&
        resource.data.customerName.size() > 1 &&
        resource.data.customerPhone is string &&
        isValidPhone(resource.data.customerPhone) &&
        resource.data.customerAddress is string &&
        resource.data.customerAddress.size() > 4 &&
        resource.data.items is list &&
        resource.data.items.size() > 0 &&
        resource.data.total is number &&
        resource.data.total > 0;
    }
    
    // Customers collection - restricted access
    match /customers/{customerId} {
      // Only managers can read customer data
      allow read: if isManager();
      
      // System can create/update customer records
      allow create, update: if isManager();
      
      // Only admins can delete customers
      allow delete: if isAdmin();
      
      // Validate customer data
      allow create, update: if isManager() &&
        resource.data.keys().hasAll(['name', 'phone']) &&
        resource.data.name is string &&
        resource.data.name.size() > 1 &&
        resource.data.phone is string &&
        isValidPhone(resource.data.phone);
    }
    
    // Settings collection - admin only
    match /settings/{settingId} {
      allow read: if isManager();
      allow write: if isAdmin();
      
      // Validate settings data
      allow create, update: if isAdmin() &&
        resource.data.keys().hasAll(['key', 'value']) &&
        resource.data.key is string &&
        resource.data.key.size() > 0;
    }
    
    // Analytics collection - admin only
    match /analytics/{analyticsId} {
      allow read: if isManager();
      allow create: if true; // Allow system to log analytics
      allow update, delete: if isAdmin();
      
      // Validate analytics data
      allow create: if 
        resource.data.keys().hasAll(['eventType', 'timestamp']) &&
        resource.data.eventType is string &&
        resource.data.eventType.size() > 0 &&
        resource.data.timestamp is timestamp;
    }
    
    // Promotions collection - public read, admin write
    match /promotions/{promotionId} {
      allow read: if true; // Public read for active promotions
      allow write: if isAdmin();
      
      // Validate promotion data
      allow create, update: if isAdmin() &&
        resource.data.keys().hasAll(['name', 'type', 'value', 'startDate', 'endDate']) &&
        resource.data.name is string &&
        resource.data.name.size() > 0 &&
        resource.data.type is string &&
        resource.data.type in ['percentage', 'fixed_amount', 'buy_x_get_y'] &&
        resource.data.value is number &&
        resource.data.value > 0 &&
        resource.data.startDate is timestamp &&
        resource.data.endDate is timestamp &&
        resource.data.endDate > resource.data.startDate;
    }
    
    // User profiles collection
    match /users/{userId} {
      // Users can read/write their own profile
      allow read, write: if isOwner(userId);
      
      // Admins can read all profiles
      allow read: if isAdmin();
      
      // Validate user data
      allow create, update: if isOwner(userId) &&
        resource.data.keys().hasAll(['email']) &&
        resource.data.email is string &&
        isValidEmail(resource.data.email);
    }
    
    // Admin logs collection - admin only
    match /admin_logs/{logId} {
      allow read: if isAdmin();
      allow create: if isManager();
      allow update, delete: if false; // Logs are immutable
      
      // Validate log data
      allow create: if isManager() &&
        resource.data.keys().hasAll(['action', 'userId', 'timestamp']) &&
        resource.data.action is string &&
        resource.data.action.size() > 0 &&
        resource.data.userId is string &&
        resource.data.timestamp is timestamp;
    }
    
    // Error logs collection - system only
    match /error_logs/{errorId} {
      allow read: if isAdmin();
      allow create: if true; // Allow system to log errors
      allow update, delete: if false; // Error logs are immutable
      
      // Validate error data
      allow create: if 
        resource.data.keys().hasAll(['type', 'message', 'timestamp']) &&
        resource.data.type is string &&
        resource.data.message is string &&
        resource.data.timestamp is timestamp;
    }
    
    // Feedback collection - public create, admin read
    match /feedback/{feedbackId} {
      allow create: if true; // Anyone can submit feedback
      allow read: if isManager();
      allow update, delete: if isAdmin();
      
      // Validate feedback data
      allow create: if 
        resource.data.keys().hasAll(['message', 'rating']) &&
        resource.data.message is string &&
        resource.data.message.size() > 0 &&
        resource.data.rating is number &&
        resource.data.rating >= 1 &&
        resource.data.rating <= 5;
    }
    
    // Default deny rule
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
