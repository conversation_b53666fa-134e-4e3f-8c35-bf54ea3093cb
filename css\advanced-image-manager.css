/* Advanced Image Manager Styles */
/* تنسيقات نظام إدارة الصور المتقدم */

/* Upload Modal Styles */
.upload-area {
    margin-bottom: 30px;
}

.upload-zone {
    border: 3px dashed #ddd;
    border-radius: 15px;
    padding: 60px 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.upload-zone:hover {
    border-color: #2c5aa0;
    background: #f0f4ff;
}

.upload-zone.dragover {
    border-color: #28a745;
    background: #f0fff4;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    color: #2c5aa0;
    margin-bottom: 20px;
}

.upload-zone h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.upload-zone p {
    color: #666;
    margin-bottom: 5px;
}

.upload-limits {
    font-size: 0.9rem;
    color: #999;
}

/* Upload Queue Styles */
.upload-queue {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    margin-top: 20px;
}

.upload-queue h4 {
    color: #2c5aa0;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.queue-items {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.queue-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: white;
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.queue-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.queue-item-preview {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.queue-item-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-loading {
    color: #999;
    font-size: 1.2rem;
}

.queue-item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-size {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
}

.item-status {
    font-size: 0.85rem;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-uploading {
    background: #d1ecf1;
    color: #0c5460;
}

.status-compressing {
    background: #e2e3e5;
    color: #383d41;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-error {
    background: #f8d7da;
    color: #721c24;
}

.queue-item-progress {
    width: 120px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.8rem;
    color: #666;
    text-align: center;
}

.queue-item-actions {
    display: flex;
    gap: 5px;
}

.btn-remove {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-remove:hover {
    background: #c82333;
    transform: scale(1.1);
}

.queue-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

/* Upload Progress Styles */
.upload-progress {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    margin-top: 20px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-info span {
    font-weight: 600;
    color: #2c5aa0;
}

.upload-progress .progress-bar {
    height: 12px;
    background: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.upload-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #2c5aa0, #007bff);
    border-radius: 6px;
    transition: width 0.3s ease;
}

/* Gallery Modal Styles */
.gallery-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
}

.gallery-filters {
    display: flex;
    gap: 15px;
    align-items: center;
}

.gallery-filters select {
    padding: 8px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    background: white;
}

.gallery-info {
    display: flex;
    gap: 20px;
    font-size: 0.9rem;
    color: #666;
}

/* Image Gallery Grid */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.gallery-image-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.gallery-image-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.image-wrapper {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
}

.image-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-image-item:hover .image-wrapper img {
    transform: scale(1.1);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-image-item:hover .image-overlay {
    opacity: 1;
}

.image-overlay button {
    background: rgba(255,255,255,0.9);
    border: none;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #333;
}

.image-overlay .btn-view:hover {
    background: #2c5aa0;
    color: white;
}

.image-overlay .btn-edit:hover {
    background: #ffc107;
    color: white;
}

.image-overlay .btn-delete:hover {
    background: #dc3545;
    color: white;
}

.primary-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #28a745;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.image-info {
    padding: 15px;
}

.image-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.image-size,
.image-date {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 3px;
}

/* No Images State */
.no-images {
    text-align: center;
    padding: 60px 30px;
    color: #666;
}

.no-images i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

.no-images h3 {
    color: #333;
    margin-bottom: 10px;
}

.no-images p {
    margin-bottom: 25px;
}

/* Gallery Pagination */
.gallery-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    padding: 20px;
}

/* Image Lightbox */
.image-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    background: rgba(0,0,0,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.lightbox-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    cursor: pointer;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    cursor: default;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
}

.lightbox-close {
    position: absolute;
    top: -50px;
    right: 0;
    background: rgba(255,255,255,0.9);
    border: none;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    color: #333;
    transition: all 0.3s ease;
}

.lightbox-close:hover {
    background: white;
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .upload-zone {
        padding: 40px 20px;
    }
    
    .upload-icon {
        font-size: 3rem;
    }
    
    .queue-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .queue-item-progress {
        width: 100%;
    }
    
    .gallery-toolbar {
        flex-direction: column;
        gap: 15px;
    }
    
    .gallery-filters {
        flex-direction: column;
        width: 100%;
    }
    
    .gallery-filters select {
        width: 100%;
    }
    
    .image-gallery {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .image-overlay {
        opacity: 1;
        background: rgba(0,0,0,0.5);
    }
    
    .image-overlay button {
        padding: 8px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .upload-zone {
        padding: 30px 15px;
    }
    
    .upload-zone h4 {
        font-size: 1.1rem;
    }
    
    .image-gallery {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }
    
    .queue-actions {
        flex-direction: column;
    }
    
    .lightbox-content {
        max-width: 95vw;
        max-height: 95vh;
    }
    
    .lightbox-close {
        top: -40px;
        padding: 8px;
        font-size: 1rem;
    }
}

/* Animation Keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.queue-item {
    animation: slideUp 0.3s ease;
}

.gallery-image-item {
    animation: slideUp 0.3s ease;
}
