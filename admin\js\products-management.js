// Advanced Products Management System
// نظام إدارة المنتجات المتقدم

class ProductsManager {
    constructor() {
        this.products = [];
        this.categories = [];
        this.currentProduct = null;
        this.selectedImages = [];
        this.isLoading = false;
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.totalItems = 0;
        this.currentView = 'grid';
        this.filters = {
            search: '',
            category: '',
            status: '',
            sortBy: 'newest'
        };

        this.init();
    }

    async init() {
        try {
            this.showLoading(true);
            await this.loadCategories();
            await this.loadProducts();
            this.setupEventListeners();
            this.populateCategoryFilters();
            this.showLoading(false);
        } catch (error) {
            console.error('Error initializing products manager:', error);
            this.showMessage('فشل في تحميل البيانات', 'error');
            this.showLoading(false);
        }
    }

    setupEventListeners() {
        // Search functionality
        document.getElementById('searchProducts').addEventListener('input', (e) => {
            this.filterProducts();
        });

        // Category filter
        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            this.filterProducts();
        });

        // Status filter
        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.filterProducts();
        });

        // Product form submission
        document.getElementById('productForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveProduct();
        });

        // Image upload handling
        document.getElementById('productImages').addEventListener('change', (e) => {
            this.handleImageSelection(e.target.files);
        });

        document.getElementById('newProductImages').addEventListener('change', (e) => {
            this.handleNewImageUpload(e.target.files);
        });

        // Drag and drop for images
        this.setupImageDragDrop();
    }

    async loadCategories() {
        try {
            // For now, use local data. Replace with API call later
            this.categories = [
                { id: '1', name: 'المقبلات', name_en: 'Appetizers' },
                { id: '2', name: 'الأطباق الرئيسية', name_en: 'Main Dishes' },
                { id: '3', name: 'المشروبات', name_en: 'Beverages' },
                { id: '4', name: 'الحلويات', name_en: 'Desserts' }
            ];
        } catch (error) {
            console.error('Error loading categories:', error);
            throw error;
        }
    }

    async loadProducts() {
        try {
            // For now, use sample data. Replace with API call later
            this.products = [
                {
                    id: '1',
                    name: 'برجر كلاسيك',
                    name_en: 'Classic Burger',
                    description: 'برجر لذيذ مع اللحم المشوي والخضروات الطازجة',
                    description_en: 'Delicious burger with grilled meat and fresh vegetables',
                    price: 45.00,
                    category_id: '2',
                    category: { name: 'الأطباق الرئيسية' },
                    is_available: true,
                    is_featured: true,
                    preparation_time: 15,
                    calories: 650,
                    ingredients: ['لحم بقري', 'خبز', 'خس', 'طماطم', 'جبن'],
                    allergens: ['جلوتين', 'ألبان'],
                    tags: ['مشوي', 'لذيذ'],
                    images: [
                        {
                            id: '1',
                            image_url: 'https://via.placeholder.com/400x300/2c5aa0/ffffff?text=برجر+كلاسيك',
                            thumbnail_url: 'https://via.placeholder.com/200x150/2c5aa0/ffffff?text=برجر+كلاسيك',
                            is_primary: true,
                            alt_text: 'برجر كلاسيك'
                        }
                    ],
                    created_at: new Date().toISOString()
                },
                {
                    id: '2',
                    name: 'سلطة سيزر',
                    name_en: 'Caesar Salad',
                    description: 'سلطة سيزر الكلاسيكية مع الدجاج المشوي',
                    description_en: 'Classic Caesar salad with grilled chicken',
                    price: 35.00,
                    category_id: '1',
                    category: { name: 'المقبلات' },
                    is_available: true,
                    is_featured: false,
                    preparation_time: 10,
                    calories: 320,
                    ingredients: ['خس', 'دجاج مشوي', 'جبن بارميزان', 'خبز محمص'],
                    allergens: ['جلوتين', 'ألبان'],
                    tags: ['صحي', 'خفيف'],
                    images: [
                        {
                            id: '2',
                            image_url: 'https://via.placeholder.com/400x300/28a745/ffffff?text=سلطة+سيزر',
                            thumbnail_url: 'https://via.placeholder.com/200x150/28a745/ffffff?text=سلطة+سيزر',
                            is_primary: true,
                            alt_text: 'سلطة سيزر'
                        }
                    ],
                    created_at: new Date().toISOString()
                }
            ];
            
            this.renderProducts();
        } catch (error) {
            console.error('Error loading products:', error);
            throw error;
        }
    }

    populateCategoryFilters() {
        const categoryFilter = document.getElementById('categoryFilter');
        const productCategory = document.getElementById('productCategory');
        
        // Clear existing options (except first one)
        categoryFilter.innerHTML = '<option value="">جميع الأقسام</option>';
        productCategory.innerHTML = '<option value="">اختر القسم</option>';
        
        this.categories.forEach(category => {
            const option1 = new Option(category.name, category.id);
            const option2 = new Option(category.name, category.id);
            
            categoryFilter.appendChild(option1);
            productCategory.appendChild(option2);
        });
    }

    renderProducts() {
        const grid = document.getElementById('productsGrid');
        
        if (this.products.length === 0) {
            grid.innerHTML = `
                <div class="no-products">
                    <i class="fas fa-box-open"></i>
                    <h3>لا توجد منتجات</h3>
                    <p>ابدأ بإضافة منتجات جديدة لقائمة المطعم</p>
                    <button class="btn-add-product" onclick="openProductModal()">
                        <i class="fas fa-plus"></i>
                        إضافة منتج جديد
                    </button>
                </div>
            `;
            return;
        }

        grid.innerHTML = this.products.map(product => this.createProductCard(product)).join('');
    }

    createProductCard(product) {
        const primaryImage = product.images?.find(img => img.is_primary) || product.images?.[0];
        const imageUrl = primaryImage?.thumbnail_url || primaryImage?.image_url || 'https://via.placeholder.com/300x200/f8f9fa/666?text=لا+توجد+صورة';
        const categoryName = product.category?.name || 'غير محدد';
        
        return `
            <div class="product-card" data-product-id="${product.id}">
                <div class="product-image">
                    <img src="${imageUrl}" alt="${product.name}" loading="lazy">
                    ${product.images?.length > 0 ? `
                        <div class="image-count">
                            <i class="fas fa-images"></i>
                            ${product.images.length}
                        </div>
                    ` : ''}
                    <div class="product-status ${product.is_available ? 'status-available' : 'status-unavailable'}">
                        ${product.is_available ? 'متوفر' : 'غير متوفر'}
                    </div>
                </div>
                
                <div class="product-content">
                    <div class="product-header">
                        <h3 class="product-name">${product.name}</h3>
                        <div class="product-price">${product.price} جنيه</div>
                    </div>
                    
                    <p class="product-description">${product.description}</p>
                    
                    <div class="product-meta">
                        <div class="product-category">
                            <i class="fas fa-tag"></i>
                            ${categoryName}
                        </div>
                        <div class="preparation-time">
                            <i class="fas fa-clock"></i>
                            ${product.preparation_time || 15} دقيقة
                        </div>
                    </div>
                    
                    ${product.is_featured ? '<div class="featured-badge">مميز</div>' : ''}
                    
                    <div class="product-actions">
                        <button class="btn-action btn-edit" onclick="productsManager.editProduct('${product.id}')">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </button>
                        <button class="btn-action btn-images" onclick="productsManager.manageImages('${product.id}')">
                            <i class="fas fa-images"></i>
                            الصور
                        </button>
                        <button class="btn-action btn-toggle ${product.is_available ? '' : 'unavailable'}" 
                                onclick="productsManager.toggleAvailability('${product.id}')">
                            <i class="fas fa-${product.is_available ? 'eye-slash' : 'eye'}"></i>
                            ${product.is_available ? 'إخفاء' : 'إظهار'}
                        </button>
                        <button class="btn-action btn-delete" onclick="productsManager.deleteProduct('${product.id}')">
                            <i class="fas fa-trash"></i>
                            حذف
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    filterProducts() {
        const searchTerm = document.getElementById('searchProducts').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;

        let filteredProducts = this.products;

        // Apply search filter
        if (searchTerm) {
            filteredProducts = filteredProducts.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm) ||
                (product.name_en && product.name_en.toLowerCase().includes(searchTerm))
            );
        }

        // Apply category filter
        if (categoryFilter) {
            filteredProducts = filteredProducts.filter(product =>
                product.category_id === categoryFilter
            );
        }

        // Apply status filter
        if (statusFilter) {
            const isAvailable = statusFilter === 'available';
            filteredProducts = filteredProducts.filter(product =>
                product.is_available === isAvailable
            );
        }

        // Temporarily store original products and render filtered
        const originalProducts = this.products;
        this.products = filteredProducts;
        this.renderProducts();
        this.products = originalProducts;
    }

    openProductModal(productId = null) {
        this.currentProduct = productId;
        this.selectedImages = [];
        
        const modal = document.getElementById('productModal');
        const title = document.getElementById('modalTitle');
        
        if (productId) {
            title.textContent = 'تعديل المنتج';
            this.populateProductForm(productId);
        } else {
            title.textContent = 'إضافة منتج جديد';
            this.resetProductForm();
        }
        
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    closeProductModal() {
        const modal = document.getElementById('productModal');
        modal.classList.remove('show');
        document.body.style.overflow = '';
        this.resetProductForm();
        this.currentProduct = null;
        this.selectedImages = [];
    }

    populateProductForm(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        document.getElementById('productName').value = product.name || '';
        document.getElementById('productNameEn').value = product.name_en || '';
        document.getElementById('productDescription').value = product.description || '';
        document.getElementById('productDescriptionEn').value = product.description_en || '';
        document.getElementById('productPrice').value = product.price || '';
        document.getElementById('productCategory').value = product.category_id || '';
        document.getElementById('preparationTime').value = product.preparation_time || 15;
        document.getElementById('calories').value = product.calories || '';
        document.getElementById('ingredients').value = product.ingredients?.join(', ') || '';
        document.getElementById('allergens').value = product.allergens?.join(', ') || '';
        document.getElementById('tags').value = product.tags?.join(', ') || '';
        document.getElementById('isAvailable').checked = product.is_available !== false;
        document.getElementById('isFeatured').checked = product.is_featured === true;

        // Display existing images
        this.displayExistingImages(product.images || []);
    }

    resetProductForm() {
        document.getElementById('productForm').reset();
        document.getElementById('isAvailable').checked = true;
        document.getElementById('isFeatured').checked = false;
        document.getElementById('imagePreview').innerHTML = '';
    }

    displayExistingImages(images) {
        const preview = document.getElementById('imagePreview');
        preview.innerHTML = '';

        if (images.length === 0) return;

        images.forEach((image, index) => {
            const imageItem = document.createElement('div');
            imageItem.className = 'gallery-item';
            imageItem.innerHTML = `
                <div class="image-wrapper">
                    <img src="${image.thumbnail_url || image.image_url}" alt="${image.alt_text || ''}">
                    <div class="image-overlay">
                        <button type="button" class="btn-view" onclick="imageManager.openLightbox('${image.image_url}', ${index})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn-delete" onclick="productsManager.removeExistingImage('${image.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    ${image.is_primary ? '<div class="primary-badge">رئيسية</div>' : ''}
                </div>
            `;
            preview.appendChild(imageItem);
        });
    }

    handleImageSelection(files) {
        this.selectedImages = Array.from(files);
        this.displaySelectedImages();
    }

    displaySelectedImages() {
        const preview = document.getElementById('imagePreview');
        preview.innerHTML = '';

        this.selectedImages.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const imageItem = document.createElement('div');
                imageItem.className = 'gallery-item';
                imageItem.innerHTML = `
                    <div class="image-wrapper">
                        <img src="${e.target.result}" alt="${file.name}">
                        <div class="image-overlay">
                            <button type="button" class="btn-delete" onclick="productsManager.removeSelectedImage(${index})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        ${index === 0 ? '<div class="primary-badge">رئيسية</div>' : ''}
                    </div>
                    <div class="image-info">
                        <p class="image-name">${file.name}</p>
                        <p class="image-size">${this.formatFileSize(file.size)}</p>
                    </div>
                `;
                preview.appendChild(imageItem);
            };
            reader.readAsDataURL(file);
        });
    }

    removeSelectedImage(index) {
        this.selectedImages.splice(index, 1);
        this.displaySelectedImages();
    }

    removeExistingImage(imageId) {
        if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
            // Here you would call the API to delete the image
            console.log('Deleting image:', imageId);
            this.showMessage('تم حذف الصورة بنجاح', 'success');
        }
    }

    async saveProduct() {
        try {
            this.showLoading(true);
            
            const formData = this.getFormData();
            
            // Validate form data
            if (!this.validateProductData(formData)) {
                this.showLoading(false);
                return;
            }

            if (this.currentProduct) {
                // Update existing product
                await this.updateProduct(this.currentProduct, formData);
            } else {
                // Create new product
                await this.createProduct(formData);
            }

            this.showLoading(false);
            this.closeProductModal();
            await this.loadProducts();
            
        } catch (error) {
            console.error('Error saving product:', error);
            this.showMessage('فشل في حفظ المنتج', 'error');
            this.showLoading(false);
        }
    }

    getFormData() {
        return {
            name: document.getElementById('productName').value.trim(),
            name_en: document.getElementById('productNameEn').value.trim(),
            description: document.getElementById('productDescription').value.trim(),
            description_en: document.getElementById('productDescriptionEn').value.trim(),
            price: parseFloat(document.getElementById('productPrice').value),
            category_id: document.getElementById('productCategory').value,
            preparation_time: parseInt(document.getElementById('preparationTime').value) || 15,
            calories: parseInt(document.getElementById('calories').value) || null,
            ingredients: document.getElementById('ingredients').value.split(',').map(s => s.trim()).filter(s => s),
            allergens: document.getElementById('allergens').value.split(',').map(s => s.trim()).filter(s => s),
            tags: document.getElementById('tags').value.split(',').map(s => s.trim()).filter(s => s),
            is_available: document.getElementById('isAvailable').checked,
            is_featured: document.getElementById('isFeatured').checked
        };
    }

    validateProductData(data) {
        if (!data.name) {
            this.showMessage('يرجى إدخال اسم المنتج', 'error');
            return false;
        }

        if (!data.description) {
            this.showMessage('يرجى إدخال وصف المنتج', 'error');
            return false;
        }

        if (!data.price || data.price <= 0) {
            this.showMessage('يرجى إدخال سعر صحيح', 'error');
            return false;
        }

        if (!data.category_id) {
            this.showMessage('يرجى اختيار قسم المنتج', 'error');
            return false;
        }

        return true;
    }

    async createProduct(productData) {
        // For now, simulate API call
        const newProduct = {
            id: Date.now().toString(),
            ...productData,
            images: [],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        // Add category info
        const category = this.categories.find(c => c.id === productData.category_id);
        if (category) {
            newProduct.category = { name: category.name };
        }

        this.products.unshift(newProduct);
        this.showMessage('تم إضافة المنتج بنجاح', 'success');
    }

    async updateProduct(productId, productData) {
        // For now, simulate API call
        const productIndex = this.products.findIndex(p => p.id === productId);
        if (productIndex > -1) {
            this.products[productIndex] = {
                ...this.products[productIndex],
                ...productData,
                updated_at: new Date().toISOString()
            };

            // Update category info
            const category = this.categories.find(c => c.id === productData.category_id);
            if (category) {
                this.products[productIndex].category = { name: category.name };
            }

            this.showMessage('تم تحديث المنتج بنجاح', 'success');
        }
    }

    editProduct(productId) {
        this.openProductModal(productId);
    }

    async toggleAvailability(productId) {
        try {
            const product = this.products.find(p => p.id === productId);
            if (!product) return;

            product.is_available = !product.is_available;
            
            // Here you would call the API to update the product
            // await ProductsAPI.update(productId, { is_available: product.is_available });
            
            this.renderProducts();
            
            const status = product.is_available ? 'متوفر' : 'غير متوفر';
            this.showMessage(`تم تغيير حالة المنتج إلى ${status}`, 'success');
            
        } catch (error) {
            console.error('Error toggling availability:', error);
            this.showMessage('فشل في تحديث حالة المنتج', 'error');
        }
    }

    async deleteProduct(productId) {
        if (!confirm('هل أنت متأكد من حذف هذا المنتج؟\nلا يمكن التراجع عن هذا الإجراء.')) {
            return;
        }

        try {
            this.showLoading(true);
            
            // Here you would call the API to delete the product
            // await ProductsAPI.delete(productId);
            
            this.products = this.products.filter(p => p.id !== productId);
            this.renderProducts();
            
            this.showMessage('تم حذف المنتج بنجاح', 'success');
            this.showLoading(false);
            
        } catch (error) {
            console.error('Error deleting product:', error);
            this.showMessage('فشل في حذف المنتج', 'error');
            this.showLoading(false);
        }
    }

    manageImages(productId) {
        this.currentProduct = productId;
        const product = this.products.find(p => p.id === productId);
        
        if (product) {
            this.displayProductImages(product.images || []);
            document.getElementById('imageModal').classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }

    closeImageModal() {
        document.getElementById('imageModal').classList.remove('show');
        document.body.style.overflow = '';
        this.currentProduct = null;
    }

    displayProductImages(images) {
        const gallery = document.getElementById('productImagesGallery');
        
        if (images.length === 0) {
            gallery.innerHTML = `
                <div class="no-images">
                    <i class="fas fa-images"></i>
                    <p>لا توجد صور لهذا المنتج</p>
                </div>
            `;
            return;
        }

        gallery.innerHTML = images.map((image, index) => `
            <div class="gallery-item">
                <div class="image-wrapper">
                    <img src="${image.thumbnail_url || image.image_url}" alt="${image.alt_text || ''}">
                    <div class="image-overlay">
                        <button class="btn-view" onclick="imageManager.openLightbox('${image.image_url}', ${index})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-delete" onclick="productsManager.removeExistingImage('${image.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    ${image.is_primary ? '<div class="primary-badge">رئيسية</div>' : ''}
                </div>
                <div class="image-info">
                    <p class="image-name">${image.alt_text || 'صورة المنتج'}</p>
                </div>
            </div>
        `).join('');
    }

    handleNewImageUpload(files) {
        if (files.length === 0) return;

        // Here you would upload the new images
        console.log('Uploading new images:', files);
        this.showMessage(`جاري رفع ${files.length} صورة...`, 'info');
        
        // Simulate upload process
        setTimeout(() => {
            this.showMessage('تم رفع الصور بنجاح', 'success');
            // Refresh the images display
            // this.manageImages(this.currentProduct);
        }, 2000);
    }

    setupImageDragDrop() {
        const uploadAreas = document.querySelectorAll('.image-upload-area');
        
        uploadAreas.forEach(area => {
            area.addEventListener('dragover', (e) => {
                e.preventDefault();
                area.classList.add('dragover');
            });

            area.addEventListener('dragleave', (e) => {
                e.preventDefault();
                area.classList.remove('dragover');
            });

            area.addEventListener('drop', (e) => {
                e.preventDefault();
                area.classList.remove('dragover');
                
                const files = Array.from(e.dataTransfer.files).filter(file => 
                    file.type.startsWith('image/')
                );
                
                if (files.length > 0) {
                    if (area.closest('#productModal')) {
                        this.handleImageSelection(files);
                    } else if (area.closest('#imageModal')) {
                        this.handleNewImageUpload(files);
                    }
                }
            });
        });
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (show) {
            overlay.classList.add('show');
        } else {
            overlay.classList.remove('show');
        }
    }

    showMessage(message, type = 'info') {
        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message show`;
        messageDiv.innerHTML = `
            <i class="fas fa-${this.getMessageIcon(type)}"></i>
            <span>${message}</span>
        `;
        
        // Add to page
        document.body.appendChild(messageDiv);
        
        // Position message
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '20px';
        messageDiv.style.right = '20px';
        messageDiv.style.zIndex = '10001';
        messageDiv.style.maxWidth = '400px';
        messageDiv.style.padding = '15px 20px';
        messageDiv.style.borderRadius = '10px';
        messageDiv.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
        messageDiv.style.display = 'flex';
        messageDiv.style.alignItems = 'center';
        messageDiv.style.gap = '10px';
        
        // Style based on type
        const styles = {
            success: { background: '#d4edda', color: '#155724', border: '1px solid #c3e6cb' },
            error: { background: '#f8d7da', color: '#721c24', border: '1px solid #f5c6cb' },
            warning: { background: '#fff3cd', color: '#856404', border: '1px solid #ffeaa7' },
            info: { background: '#d1ecf1', color: '#0c5460', border: '1px solid #bee5eb' }
        };
        
        Object.assign(messageDiv.style, styles[type] || styles.info);
        
        // Remove after 4 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 4000);
    }

    getMessageIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// Initialize products manager
const productsManager = new ProductsManager();

// Global functions for HTML onclick events
function openProductModal() {
    productsManager.openProductModal();
}

function closeProductModal() {
    productsManager.closeProductModal();
}

function closeImageModal() {
    productsManager.closeImageModal();
}
