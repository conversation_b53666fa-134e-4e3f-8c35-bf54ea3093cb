<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قواعد البيانات - مطعم محمد الاشرافي</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .setup-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .setup-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .setup-step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        
        .setup-step.completed {
            border-left-color: #27ae60;
            background: #d5f4e6;
        }
        
        .setup-step.error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        
        .step-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .step-content {
            margin-bottom: 15px;
        }
        
        .config-box {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn.success {
            background: #27ae60;
        }
        
        .btn.danger {
            background: #e74c3c;
        }
        
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-pending {
            background: #f39c12;
        }
        
        .status-success {
            background: #27ae60;
        }
        
        .status-error {
            background: #e74c3c;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-database"></i> إعداد قواعد البيانات</h1>
            <p>إعداد Firebase و Supabase لنظام مطعم محمد الاشرافي</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- Firebase Setup -->
        <div class="setup-step" id="firebaseStep">
            <div class="step-title">
                <i class="fab fa-google"></i>
                إعداد Firebase
                <span class="status-indicator status-pending" id="firebaseStatus"></span>
            </div>
            <div class="step-content">
                <p>إعداد Firebase للمصادقة وتخزين الصور</p>
                <ol>
                    <li>اذهب إلى <a href="https://console.firebase.google.com/project/al-ishrafi-accounting-2025" target="_blank">Firebase Console</a></li>
                    <li>أنشئ تطبيق ويب جديد باسم "alashrafi-restaurant"</li>
                    <li>فعّل Firestore Database و Storage</li>
                    <li>انسخ التكوين وضعه أدناه:</li>
                </ol>
                
                <div class="config-box" id="firebaseConfig">
                    <div>// Firebase Configuration</div>
                    <div>const firebaseConfig = {</div>
                    <div>&nbsp;&nbsp;apiKey: "<span id="apiKey">YOUR_API_KEY</span>",</div>
                    <div>&nbsp;&nbsp;authDomain: "al-ishrafi-accounting-2025.firebaseapp.com",</div>
                    <div>&nbsp;&nbsp;projectId: "al-ishrafi-accounting-2025",</div>
                    <div>&nbsp;&nbsp;storageBucket: "al-ishrafi-accounting-2025.appspot.com",</div>
                    <div>&nbsp;&nbsp;messagingSenderId: "************",</div>
                    <div>&nbsp;&nbsp;appId: "<span id="appId">YOUR_APP_ID</span>"</div>
                    <div>};</div>
                </div>
                
                <button class="btn" onclick="testFirebase()">اختبار Firebase</button>
                <button class="btn success" onclick="setupFirebaseData()">إعداد البيانات</button>
            </div>
        </div>

        <!-- Supabase Setup -->
        <div class="setup-step" id="supabaseStep">
            <div class="step-title">
                <i class="fas fa-database"></i>
                إعداد Supabase
                <span class="status-indicator status-pending" id="supabaseStatus"></span>
            </div>
            <div class="step-content">
                <p>إعداد Supabase لقاعدة البيانات الرئيسية</p>
                <ol>
                    <li>اذهب إلى <a href="https://supabase.com/dashboard/new" target="_blank">Supabase Dashboard</a></li>
                    <li>أنشئ مشروع جديد باسم "alashrafi-restaurant"</li>
                    <li>انسخ URL و anon key من Settings → API</li>
                    <li>شغّل SQL Schema في SQL Editor</li>
                </ol>
                
                <div class="config-box" id="supabaseConfig">
                    <div>// Supabase Configuration</div>
                    <div>const supabaseConfig = {</div>
                    <div>&nbsp;&nbsp;url: '<span id="supabaseUrl">YOUR_SUPABASE_URL</span>',</div>
                    <div>&nbsp;&nbsp;anonKey: '<span id="supabaseKey">YOUR_ANON_KEY</span>'</div>
                    <div>};</div>
                </div>
                
                <button class="btn" onclick="testSupabase()">اختبار Supabase</button>
                <button class="btn success" onclick="setupSupabaseData()">إعداد البيانات</button>
                <button class="btn" onclick="downloadSchema()">تحميل SQL Schema</button>
            </div>
        </div>

        <!-- Final Setup -->
        <div class="setup-step" id="finalStep">
            <div class="step-title">
                <i class="fas fa-check-circle"></i>
                الإعداد النهائي
                <span class="status-indicator status-pending" id="finalStatus"></span>
            </div>
            <div class="step-content">
                <p>إنهاء إعداد النظام</p>
                <button class="btn success" onclick="finalizeSetup()">إنهاء الإعداد</button>
                <button class="btn" onclick="goToAdmin()">الذهاب للإدارة</button>
                <button class="btn" onclick="goToWebsite()">الذهاب للموقع</button>
            </div>
        </div>

        <!-- Status Messages -->
        <div id="statusMessages" style="margin-top: 30px;"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-storage-compat.js"></script>
    
    <!-- Supabase SDK -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Configuration -->
    <script src="js/firebase-config.js"></script>
    <script src="js/supabase-config.js"></script>
    
    <!-- Setup Script -->
    <script src="setup-real-database.js"></script>
    
    <script>
        let setupProgress = 0;
        
        function updateProgress(step) {
            setupProgress = step;
            document.getElementById('progressFill').style.width = (step * 33.33) + '%';
        }
        
        function showMessage(message, type = 'info') {
            const messagesDiv = document.getElementById('statusMessages');
            const messageEl = document.createElement('div');
            messageEl.className = `alert alert-${type}`;
            messageEl.style.cssText = `
                padding: 15px;
                margin: 10px 0;
                border-radius: 8px;
                background: ${type === 'success' ? '#d5f4e6' : type === 'error' ? '#fdf2f2' : '#e3f2fd'};
                border: 1px solid ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
                color: ${type === 'success' ? '#1e7e34' : type === 'error' ? '#721c24' : '#1565c0'};
            `;
            messageEl.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i> ${message}`;
            messagesDiv.appendChild(messageEl);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 5000);
        }
        
        function updateStepStatus(stepId, status) {
            const step = document.getElementById(stepId);
            const statusIndicator = step.querySelector('.status-indicator');
            
            statusIndicator.className = `status-indicator status-${status}`;
            
            if (status === 'success') {
                step.classList.add('completed');
            } else if (status === 'error') {
                step.classList.add('error');
            }
        }
        
        async function testFirebase() {
            try {
                showMessage('جاري اختبار Firebase...', 'info');
                
                if (typeof firebase === 'undefined') {
                    throw new Error('Firebase SDK غير محمل');
                }
                
                if (!firebase.apps.length) {
                    firebase.initializeApp(firebaseConfig);
                }
                
                const db = firebase.firestore();
                await db.collection('test').doc('test').set({test: true});
                await db.collection('test').doc('test').delete();
                
                updateStepStatus('firebaseStep', 'success');
                updateProgress(1);
                showMessage('تم اختبار Firebase بنجاح!', 'success');
                
            } catch (error) {
                updateStepStatus('firebaseStep', 'error');
                showMessage('خطأ في Firebase: ' + error.message, 'error');
            }
        }
        
        async function testSupabase() {
            try {
                showMessage('جاري اختبار Supabase...', 'info');
                
                if (typeof supabase === 'undefined') {
                    throw new Error('Supabase SDK غير محمل');
                }
                
                const client = supabase.createClient(supabaseConfig.url, supabaseConfig.anonKey);
                const { data, error } = await client.from('categories').select('count');
                
                if (error && error.code !== 'PGRST116') {
                    throw error;
                }
                
                updateStepStatus('supabaseStep', 'success');
                updateProgress(2);
                showMessage('تم اختبار Supabase بنجاح!', 'success');
                
            } catch (error) {
                updateStepStatus('supabaseStep', 'error');
                showMessage('خطأ في Supabase: ' + error.message, 'error');
            }
        }
        
        async function setupFirebaseData() {
            const success = await setupFirebase();
            if (success) {
                showMessage('تم إعداد بيانات Firebase بنجاح!', 'success');
            }
        }
        
        async function setupSupabaseData() {
            const success = await setupSupabase();
            if (success) {
                showMessage('تم إعداد بيانات Supabase بنجاح!', 'success');
            }
        }
        
        function downloadSchema() {
            const link = document.createElement('a');
            link.href = 'database-schema.sql';
            link.download = 'database-schema.sql';
            link.click();
        }
        
        async function finalizeSetup() {
            updateStepStatus('finalStep', 'success');
            updateProgress(3);
            showMessage('تم إنهاء الإعداد بنجاح! النظام جاهز للاستخدام.', 'success');
        }
        
        function goToAdmin() {
            window.open('admin/login.html', '_blank');
        }
        
        function goToWebsite() {
            window.open('index.html', '_blank');
        }
        
        // Auto-test connections on page load
        setTimeout(() => {
            testFirebase();
            setTimeout(() => {
                testSupabase();
            }, 2000);
        }, 1000);
    </script>
</body>
</html>
