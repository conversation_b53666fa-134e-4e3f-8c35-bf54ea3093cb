<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - محمد الاشرافي</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="../css/image-gallery.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .products-management {
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .management-header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .header-title i {
            font-size: 2rem;
            color: #ffd700;
        }
        
        .header-title h1 {
            font-size: 1.8rem;
            margin: 0;
        }
        
        .header-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .btn-back {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-back:hover {
            background: rgba(255,255,255,0.3);
            color: white;
        }
        
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .products-toolbar {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .toolbar-left {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-filter {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .search-input {
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            width: 250px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #2c5aa0;
            box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
        }
        
        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-select:focus {
            outline: none;
            border-color: #2c5aa0;
        }
        
        .btn-add-product {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-add-product:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .product-image {
            width: 100%;
            height: 200px;
            background: #f8f9fa;
            position: relative;
            overflow: hidden;
        }
        
        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .product-card:hover .product-image img {
            transform: scale(1.05);
        }
        
        .image-count {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .product-status {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-available {
            background: #d4edda;
            color: #155724;
        }
        
        .status-unavailable {
            background: #f8d7da;
            color: #721c24;
        }
        
        .product-content {
            padding: 20px;
        }
        
        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        
        .product-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin: 0;
            line-height: 1.3;
        }
        
        .product-price {
            font-size: 1.1rem;
            font-weight: 700;
            color: #2c5aa0;
            background: #e3f2fd;
            padding: 4px 12px;
            border-radius: 15px;
        }
        
        .product-description {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
            margin: 10px 0;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .product-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            font-size: 0.85rem;
            color: #666;
        }
        
        .product-category {
            background: #f8f9fa;
            padding: 4px 10px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .product-actions {
            display: flex;
            gap: 8px;
            justify-content: center;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        
        .btn-action {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }
        
        .btn-edit {
            background: #e3f2fd;
            color: #2c5aa0;
        }
        
        .btn-edit:hover {
            background: #2c5aa0;
            color: white;
        }
        
        .btn-images {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .btn-images:hover {
            background: #f57c00;
            color: white;
        }
        
        .btn-toggle {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .btn-toggle:hover {
            background: #2e7d32;
            color: white;
        }
        
        .btn-toggle.unavailable {
            background: #ffebee;
            color: #c62828;
        }
        
        .btn-toggle.unavailable:hover {
            background: #c62828;
            color: white;
        }
        
        .btn-delete {
            background: #ffebee;
            color: #c62828;
        }
        
        .btn-delete:hover {
            background: #c62828;
            color: white;
        }
        
        .no-products {
            text-align: center;
            padding: 60px 20px;
            color: #666;
            grid-column: 1 / -1;
        }
        
        .no-products i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .no-products h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #999;
        }
        
        .no-products p {
            font-size: 1rem;
            margin-bottom: 20px;
        }
        
        /* Loading State */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-overlay.show {
            display: flex;
        }
        
        .loading-spinner {
            text-align: center;
        }
        
        .loading-spinner i {
            font-size: 3rem;
            color: #2c5aa0;
            animation: spin 1s linear infinite;
        }
        
        .loading-spinner p {
            margin-top: 15px;
            color: #666;
            font-size: 1.1rem;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .products-toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .toolbar-left {
                justify-content: center;
            }
            
            .search-input {
                width: 100%;
                max-width: 300px;
            }
            
            .products-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .product-actions {
                flex-wrap: wrap;
            }
            
            .btn-action {
                min-width: 80px;
            }
        }
    </style>
</head>
<body class="products-management">
    <!-- Header -->
    <header class="management-header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-boxes"></i>
                <h1>إدارة المنتجات</h1>
            </div>
            <div class="header-actions">
                <a href="dashboard.html" class="btn-back">
                    <i class="fas fa-arrow-right"></i>
                    العودة للوحة التحكم
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Toolbar -->
        <div class="products-toolbar">
            <div class="toolbar-left">
                <div class="search-filter">
                    <input type="text" id="searchProducts" class="search-input" placeholder="البحث في المنتجات...">
                    <select id="categoryFilter" class="filter-select">
                        <option value="">جميع الأقسام</option>
                    </select>
                    <select id="statusFilter" class="filter-select">
                        <option value="">جميع الحالات</option>
                        <option value="available">متوفر</option>
                        <option value="unavailable">غير متوفر</option>
                    </select>
                </div>
            </div>
            <button class="btn-add-product" onclick="openProductModal()">
                <i class="fas fa-plus"></i>
                إضافة منتج جديد
            </button>
        </div>

        <!-- Products Grid -->
        <div class="products-grid" id="productsGrid">
            <!-- Products will be loaded here -->
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner"></i>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- Product Modal -->
    <div id="productModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة منتج جديد</h3>
                <button class="close-modal" onclick="closeProductModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="productForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم المنتج *</label>
                            <input type="text" id="productName" required>
                        </div>
                        <div class="form-group">
                            <label>اسم المنتج بالإنجليزية</label>
                            <input type="text" id="productNameEn">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>وصف المنتج *</label>
                        <textarea id="productDescription" rows="3" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>وصف المنتج بالإنجليزية</label>
                        <textarea id="productDescriptionEn" rows="3"></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>السعر (جنيه) *</label>
                            <input type="number" id="productPrice" min="0" step="0.5" required>
                        </div>
                        <div class="form-group">
                            <label>القسم *</label>
                            <select id="productCategory" required>
                                <option value="">اختر القسم</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>وقت التحضير (دقيقة)</label>
                            <input type="number" id="preparationTime" min="1" value="15">
                        </div>
                        <div class="form-group">
                            <label>السعرات الحرارية</label>
                            <input type="number" id="calories" min="0">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>المكونات (مفصولة بفاصلة)</label>
                        <input type="text" id="ingredients" placeholder="مثال: دقيق، سكر، بيض">
                    </div>
                    
                    <div class="form-group">
                        <label>مسببات الحساسية (مفصولة بفاصلة)</label>
                        <input type="text" id="allergens" placeholder="مثال: جلوتين، ألبان، مكسرات">
                    </div>
                    
                    <div class="form-group">
                        <label>العلامات (مفصولة بفاصلة)</label>
                        <input type="text" id="tags" placeholder="مثال: حار، نباتي، خالي من الجلوتين">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="isAvailable" checked>
                                <span class="checkmark"></span>
                                متوفر
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="isFeatured">
                                <span class="checkmark"></span>
                                منتج مميز
                            </label>
                        </div>
                    </div>
                    
                    <!-- Image Upload Section -->
                    <div class="form-group">
                        <label>صور المنتج</label>
                        <div class="image-upload-area" onclick="document.getElementById('productImages').click()">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="upload-text">اختر صور المنتج</div>
                            <div class="upload-hint">يمكنك اختيار عدة صور. الحد الأقصى 5 ميجابايت لكل صورة</div>
                            <input type="file" id="productImages" multiple accept="image/*" style="display: none;">
                        </div>
                        <div id="imagePreview" class="image-gallery" style="margin-top: 20px;"></div>
                    </div>
                    
                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" onclick="closeProductModal()">إلغاء</button>
                        <button type="submit" class="btn-primary">حفظ المنتج</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Image Management Modal -->
    <div id="imageModal" class="modal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3>إدارة صور المنتج</h3>
                <button class="close-modal" onclick="closeImageModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="image-upload-area" onclick="document.getElementById('newProductImages').click()">
                    <div class="upload-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="upload-text">إضافة صور جديدة</div>
                    <input type="file" id="newProductImages" multiple accept="image/*" style="display: none;">
                </div>
                
                <div id="productImagesGallery" class="image-gallery" style="margin-top: 30px;">
                    <!-- Product images will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Simple Auth Check
        function checkAuth() {
            const session = localStorage.getItem('admin_logged_in');
            if (!session) {
                window.location.replace('login.html');
                return false;
            }
            return true;
        }

        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
        });
    </script>
    <script src="../js/supabase-config.js"></script>
    <script src="../js/supabase-storage.js"></script>
    <script src="js/supabase-image-manager.js"></script>
    <script src="js/products-management.js"></script>
</body>
</html>
