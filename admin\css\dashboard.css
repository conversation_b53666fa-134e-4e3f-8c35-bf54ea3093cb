/* Dashboard Specific Styles */

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c5aa0;
    margin: 0 0 5px 0;
}

.stat-content p {
    color: #666;
    margin: 0;
    font-size: 0.95rem;
}

/* Dashboard Cards */
.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    color: #2c5aa0;
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0;
}

.card-content {
    padding: 25px;
}

/* Tables */
.table-container {
    overflow-x: auto;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}

.admin-table th,
.admin-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e1e5e9;
}

.admin-table th {
    background: #f8f9fa;
    color: #2c5aa0;
    font-weight: 600;
    position: sticky;
    top: 0;
}

.admin-table tr:hover {
    background: #f8f9fa;
}

/* Menu Tabs */
.menu-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.menu-tab {
    background: #f8f9fa;
    border: 2px solid #e1e5e9;
    color: #666;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.menu-tab:hover {
    background: #e9ecef;
}

.menu-tab.active {
    background: #2c5aa0;
    color: white;
    border-color: #2c5aa0;
}

/* Status Badges */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-block;
}

.status-available {
    background: #d4edda;
    color: #155724;
}

.status-unavailable {
    background: #f8d7da;
    color: #721c24;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-processing {
    background: #cce5ff;
    color: #004085;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.85rem;
    border-radius: 6px;
}

/* Orders List */
.orders-list {
    max-height: 400px;
    overflow-y: auto;
}

.order-item {
    padding: 15px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.order-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.order-id {
    font-weight: 600;
    color: #2c5aa0;
}

.order-time {
    color: #666;
    font-size: 0.9rem;
}

.order-details {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
}

.order-total {
    font-weight: 600;
    color: #2c5aa0;
    margin-top: 10px;
}

/* Popular Items */
.popular-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.popular-item {
    padding: 15px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    text-align: center;
}

.popular-item-icon {
    font-size: 2rem;
    color: #2c5aa0;
    margin-bottom: 10px;
}

.popular-item-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.popular-item-count {
    color: #666;
    font-size: 0.9rem;
}

/* Forms */
.settings-form .form-group {
    margin-bottom: 20px;
}

.settings-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c5aa0;
}

.settings-form input,
.settings-form textarea,
.settings-form select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.settings-form input:focus,
.settings-form textarea:focus,
.settings-form select:focus {
    outline: none;
    border-color: #2c5aa0;
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
}

.settings-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.setting-item {
    padding: 15px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
}

.setting-label {
    display: flex !important;
    align-items: center;
    cursor: pointer;
    font-weight: 500 !important;
    color: #333 !important;
    margin: 0 !important;
}

.setting-label input[type="checkbox"] {
    margin-left: 12px;
    width: auto !important;
}

/* Analytics */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
}

.chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
    color: #666;
    font-style: italic;
}

/* Filter Select */
.filter-select {
    padding: 8px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    background: white;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #2c5aa0;
}

/* Modal Actions */
.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-container {
        flex-direction: column;
    }

    .dashboard-sidebar {
        width: 100%;
        order: 2;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    }

    .dashboard-main {
        order: 1;
        padding: 20px;
    }

    .sidebar-nav {
        padding: 15px 0;
    }

    .sidebar-nav ul {
        display: flex;
        overflow-x: auto;
        padding: 0 20px;
        scrollbar-width: thin;
        scrollbar-color: #2c5aa0 #f1f1f1;
    }

    .sidebar-nav ul::-webkit-scrollbar {
        height: 4px;
    }

    .sidebar-nav ul::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    .sidebar-nav ul::-webkit-scrollbar-thumb {
        background: #2c5aa0;
        border-radius: 2px;
    }

    .nav-item {
        flex-shrink: 0;
        margin-bottom: 0;
        margin-left: 10px;
    }

    .nav-link {
        padding: 10px 20px;
        border-right: none;
        border-bottom: 3px solid transparent;
        white-space: nowrap;
        font-size: 0.9rem;
    }

    .nav-item.active .nav-link {
        border-right: none;
        border-bottom-color: #2c5aa0;
        background: #e3f2fd;
    }

    .sidebar-footer {
        padding: 15px 20px;
        text-align: center;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .section-header h2 {
        font-size: 1.7rem;
    }

    .section-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .dashboard-main {
        padding: 15px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 20px;
    }

    .section-header h2 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stat-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .stat-content h3 {
        font-size: 1.8rem;
    }

    .dashboard-card {
        margin-bottom: 20px;
    }

    .card-header {
        padding: 15px 20px;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .card-header h3 {
        font-size: 1.2rem;
    }

    .card-content {
        padding: 20px;
    }

    .table-container {
        font-size: 0.85rem;
    }

    .admin-table th,
    .admin-table td {
        padding: 8px 10px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .btn-sm {
        width: 100%;
        justify-content: center;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .settings-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .menu-tabs {
        overflow-x: auto;
        padding-bottom: 10px;
        scrollbar-width: thin;
        scrollbar-color: #2c5aa0 #f1f1f1;
    }

    .menu-tabs::-webkit-scrollbar {
        height: 4px;
    }

    .menu-tabs::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    .menu-tabs::-webkit-scrollbar-thumb {
        background: #2c5aa0;
        border-radius: 2px;
    }

    .menu-tab {
        flex-shrink: 0;
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    .order-item {
        padding: 12px;
        margin-bottom: 12px;
    }

    .order-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .order-details {
        font-size: 0.85rem;
        margin: 8px 0;
    }

    .popular-items {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }

    .popular-item {
        padding: 12px;
        font-size: 0.9rem;
    }

    .popular-item-icon {
        font-size: 1.5rem;
        margin-bottom: 8px;
    }

    .modal-actions {
        flex-direction: column;
        gap: 10px;
    }

    .modal-actions .btn-primary,
    .modal-actions .btn-secondary {
        width: 100%;
        justify-content: center;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .dashboard-main {
        padding: 10px;
    }

    .section-header h2 {
        font-size: 1.3rem;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-content h3 {
        font-size: 1.5rem;
    }

    .card-header,
    .card-content {
        padding: 15px;
    }

    .admin-table {
        font-size: 0.8rem;
    }

    .admin-table th,
    .admin-table td {
        padding: 6px 8px;
    }

    .menu-tab {
        padding: 6px 12px;
        font-size: 0.85rem;
    }

    .popular-items {
        grid-template-columns: 1fr;
    }

    .btn-primary,
    .btn-secondary {
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .settings-form input,
    .settings-form textarea,
    .settings-form select {
        padding: 10px 12px;
        font-size: 0.9rem;
    }
}
