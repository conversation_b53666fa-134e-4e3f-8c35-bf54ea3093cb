// Real Database Setup Script
// سكريپت إعداد قواعد البيانات الحقيقية

console.log('🔥 إعداد قواعد البيانات الحقيقية...');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

// Firebase Setup
async function setupFirebase() {
    console.log('1️⃣ إعداد Firebase...');

    try {
        // Wait for Firebase to be available
        if (typeof firebase === 'undefined') {
            console.error('❌ Firebase SDK غير محمل');
            return false;
        }

        // Initialize Firebase if not already done
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }

        const db = firebase.firestore();
        const storage = firebase.storage();

        console.log('✅ Firebase متصل');

        // Setup initial categories
        const categoriesRef = db.collection('categories');
        const categories = [
            {
                id: '1',
                name: 'المقبلات',
                name_en: 'Appetizers',
                description: 'مقبلات شهية ومتنوعة',
                sort_order: 1,
                is_active: true,
                created_at: firebase.firestore.FieldValue.serverTimestamp()
            },
            {
                id: '2',
                name: 'الأطباق الرئيسية',
                name_en: 'Main Dishes',
                description: 'أطباق رئيسية مميزة',
                sort_order: 2,
                is_active: true,
                created_at: firebase.firestore.FieldValue.serverTimestamp()
            },
            {
                id: '3',
                name: 'المشروبات',
                name_en: 'Beverages',
                description: 'مشروبات ساخنة وباردة',
                sort_order: 3,
                is_active: true,
                created_at: firebase.firestore.FieldValue.serverTimestamp()
            },
            {
                id: '4',
                name: 'الحلويات',
                name_en: 'Desserts',
                description: 'حلويات لذيذة ومتنوعة',
                sort_order: 4,
                is_active: true,
                created_at: firebase.firestore.FieldValue.serverTimestamp()
            }
        ];

        // Add categories to Firestore
        for (const category of categories) {
            await categoriesRef.doc(category.id).set(category);
        }

        console.log('✅ تم إنشاء الأقسام في Firebase');

        // Setup initial products
        const productsRef = db.collection('products');
        const products = [
            {
                id: '1',
                name: 'حمص بالطحينة',
                name_en: 'Hummus with Tahini',
                description: 'حمص طازج مع الطحينة والزيت والبقدونس',
                price: 25.00,
                category_id: '1',
                is_available: true,
                is_featured: false,
                preparation_time: 5,
                calories: 180,
                ingredients: ['حمص', 'طحينة', 'زيت زيتون', 'ليمون', 'ثوم', 'بقدونس'],
                allergens: ['سمسم'],
                tags: ['نباتي', 'صحي'],
                images: [],
                created_at: firebase.firestore.FieldValue.serverTimestamp(),
                updated_at: firebase.firestore.FieldValue.serverTimestamp()
            },
            {
                id: '2',
                name: 'متبل باذنجان',
                name_en: 'Baba Ganoush',
                description: 'باذنجان مشوي مع الطحينة والثوم والليمون',
                price: 30.00,
                category_id: '1',
                is_available: true,
                is_featured: false,
                preparation_time: 10,
                calories: 150,
                ingredients: ['باذنجان', 'طحينة', 'ثوم', 'ليمون', 'زيت زيتون'],
                allergens: ['سمسم'],
                tags: ['نباتي', 'مشوي'],
                images: [],
                created_at: firebase.firestore.FieldValue.serverTimestamp(),
                updated_at: firebase.firestore.FieldValue.serverTimestamp()
            },
            {
                id: '3',
                name: 'كباب لحم مشوي',
                name_en: 'Grilled Meat Kebab',
                description: 'كباب لحم بقري مشوي على الفحم مع الأرز والسلطة',
                price: 85.00,
                category_id: '2',
                is_available: true,
                is_featured: true,
                preparation_time: 20,
                calories: 450,
                ingredients: ['لحم بقري', 'أرز', 'خضروات', 'بصل', 'بهارات'],
                allergens: [],
                tags: ['مشوي', 'بروتين'],
                images: [],
                created_at: firebase.firestore.FieldValue.serverTimestamp(),
                updated_at: firebase.firestore.FieldValue.serverTimestamp()
            }
        ];

        // Add products to Firestore
        for (const product of products) {
            await productsRef.doc(product.id).set(product);
        }

        console.log('✅ تم إنشاء المنتجات في Firebase');

        // Setup restaurant settings
        const settingsRef = db.collection('settings').doc('restaurant');
        await settingsRef.set({
            name: 'مطعم محمد الاشرافي',
            name_en: 'Mohamed Al-Ashrafi Restaurant',
            phone: '+201014840269',
            whatsapp: '+201014840269',
            address: 'القاهرة، مصر',
            working_hours: {
                open: '10:00',
                close: '24:00'
            },
            delivery_fee: 15,
            min_order: 50,
            currency: 'جنيه',
            updated_at: firebase.firestore.FieldValue.serverTimestamp()
        });

        console.log('✅ تم إنشاء إعدادات المطعم في Firebase');

        return true;

    } catch (error) {
        console.error('❌ خطأ في إعداد Firebase:', error);
        return false;
    }
}

// Supabase Setup
async function setupSupabase() {
    console.log('2️⃣ إعداد Supabase...');

    try {
        // Check if Supabase is available
        if (typeof supabase === 'undefined') {
            console.error('❌ Supabase SDK غير محمل');
            return false;
        }

        const client = supabase.createClient(supabaseConfig.url, supabaseConfig.anonKey);

        console.log('✅ Supabase متصل');

        // Test connection
        const { data, error } = await client.from('categories').select('count');

        if (error && error.code === 'PGRST116') {
            console.log('ℹ️ جداول Supabase غير موجودة - يجب إنشاؤها يدوياً');
            console.log('📋 استخدم ملف database-schema.sql لإنشاء الجداول');
        } else if (error) {
            console.error('❌ خطأ في الاتصال بـ Supabase:', error);
            return false;
        } else {
            console.log('✅ جداول Supabase موجودة');
        }

        return true;

    } catch (error) {
        console.error('❌ خطأ في إعداد Supabase:', error);
        return false;
    }
}

// Main setup function
async function setupRealDatabases() {
    console.log('🚀 بدء إعداد قواعد البيانات الحقيقية...');

    const firebaseSuccess = await setupFirebase();
    const supabaseSuccess = await setupSupabase();

    if (firebaseSuccess && supabaseSuccess) {
        console.log('🎉 تم إعداد قواعد البيانات بنجاح!');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        console.log('✅ Firebase: متصل ومُعد');
        console.log('✅ Supabase: متصل ومُعد');
        console.log('📱 النظام جاهز للاستخدام مع قواعد البيانات الحقيقية');

        // Update UI to show success
        if (typeof showDatabaseStatus === 'function') {
            showDatabaseStatus('success', 'قواعد البيانات متصلة ومُعدة بنجاح');
        }

        return true;
    } else {
        console.log('⚠️ فشل في إعداد بعض قواعد البيانات');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        console.log(`${firebaseSuccess ? '✅' : '❌'} Firebase`);
        console.log(`${supabaseSuccess ? '✅' : '❌'} Supabase`);

        // Update UI to show partial success
        if (typeof showDatabaseStatus === 'function') {
            showDatabaseStatus('warning', 'بعض قواعد البيانات غير متصلة');
        }

        return false;
    }
}

// Auto-run setup when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for other scripts to load
    setTimeout(setupRealDatabases, 2000);
});

// Make functions available globally
window.setupRealDatabases = setupRealDatabases;
window.setupFirebase = setupFirebase;
window.setupSupabase = setupSupabase;