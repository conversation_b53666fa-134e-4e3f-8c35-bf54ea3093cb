// Database API for Mohamed <PERSON>
// واجهة برمجة التطبيقات لقاعدة البيانات - مطعم محمد الاشرافي

import { supabase, tables, handleSupabaseError, utils } from '../supabase-config.js';
import { storage, handleFirebaseError } from '../firebase-config.js';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';

// Products API
export class ProductsAPI {
    // Get all products with images and categories
    static async getAll(filters = {}) {
        try {
            let query = supabase
                .from(tables.PRODUCTS)
                .select(`
                    *,
                    category:categories(*),
                    images:product_images(*)
                `)
                .order('sort_order', { ascending: true });

            // Apply filters
            if (filters.categoryId) {
                query = query.eq('category_id', filters.categoryId);
            }
            
            if (filters.isAvailable !== undefined) {
                query = query.eq('is_available', filters.isAvailable);
            }
            
            if (filters.isFeatured !== undefined) {
                query = query.eq('is_featured', filters.isFeatured);
            }
            
            if (filters.search) {
                query = query.ilike('name', `%${filters.search}%`);
            }

            const { data, error } = await query;
            
            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Get single product by ID
    static async getById(id) {
        try {
            const { data, error } = await supabase
                .from(tables.PRODUCTS)
                .select(`
                    *,
                    category:categories(*),
                    images:product_images(*)
                `)
                .eq('id', id)
                .single();

            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Create new product
    static async create(productData, imageFiles = []) {
        try {
            // Insert product
            const { data: product, error: productError } = await supabase
                .from(tables.PRODUCTS)
                .insert(productData)
                .select()
                .single();

            if (productError) throw productError;

            // Upload images if provided
            if (imageFiles.length > 0) {
                const imageResults = await this.uploadImages(product.id, imageFiles);
                if (!imageResults.success) {
                    // Rollback product creation if image upload fails
                    await this.delete(product.id);
                    throw new Error(imageResults.error);
                }
            }

            return { success: true, data: product };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Update product
    static async update(id, productData, newImageFiles = []) {
        try {
            const { data: product, error: productError } = await supabase
                .from(tables.PRODUCTS)
                .update({
                    ...productData,
                    updated_at: new Date().toISOString()
                })
                .eq('id', id)
                .select()
                .single();

            if (productError) throw productError;

            // Upload new images if provided
            if (newImageFiles.length > 0) {
                await this.uploadImages(id, newImageFiles);
            }

            return { success: true, data: product };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Delete product
    static async delete(id) {
        try {
            // Delete associated images from storage
            await this.deleteAllImages(id);

            // Delete product (cascade will handle product_images)
            const { error } = await supabase
                .from(tables.PRODUCTS)
                .delete()
                .eq('id', id);

            if (error) throw error;

            return { success: true };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Upload product images
    static async uploadImages(productId, imageFiles) {
        try {
            const uploadPromises = imageFiles.map(async (file, index) => {
                // Generate unique filename
                const filename = `${productId}_${Date.now()}_${index}.${file.type.split('/')[1]}`;
                const imagePath = `products/${filename}`;
                
                // Upload to Firebase Storage
                const storageRef = ref(storage, imagePath);
                const snapshot = await uploadBytes(storageRef, file);
                const downloadURL = await getDownloadURL(snapshot.ref);
                
                // Create thumbnail URL (you might want to generate actual thumbnails)
                const thumbnailURL = downloadURL; // For now, same as original
                
                // Save image info to database
                const imageData = {
                    product_id: productId,
                    image_url: downloadURL,
                    thumbnail_url: thumbnailURL,
                    alt_text: file.name,
                    is_primary: index === 0, // First image is primary
                    sort_order: index,
                    file_size: file.size,
                    file_type: file.type,
                    width: null, // You can get this from image metadata
                    height: null
                };

                const { data, error } = await supabase
                    .from(tables.PRODUCT_IMAGES)
                    .insert(imageData)
                    .select()
                    .single();

                if (error) throw error;
                return data;
            });

            const results = await Promise.all(uploadPromises);
            return { success: true, data: results };
        } catch (error) {
            return { success: false, error: handleFirebaseError(error) };
        }
    }

    // Delete product image
    static async deleteImage(imageId) {
        try {
            // Get image info first
            const { data: image, error: getError } = await supabase
                .from(tables.PRODUCT_IMAGES)
                .select('image_url')
                .eq('id', imageId)
                .single();

            if (getError) throw getError;

            // Delete from Firebase Storage
            const imageRef = ref(storage, image.image_url);
            await deleteObject(imageRef);

            // Delete from database
            const { error: deleteError } = await supabase
                .from(tables.PRODUCT_IMAGES)
                .delete()
                .eq('id', imageId);

            if (deleteError) throw deleteError;

            return { success: true };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Delete all images for a product
    static async deleteAllImages(productId) {
        try {
            // Get all images for the product
            const { data: images, error: getError } = await supabase
                .from(tables.PRODUCT_IMAGES)
                .select('id, image_url')
                .eq('product_id', productId);

            if (getError) throw getError;

            // Delete each image
            const deletePromises = images.map(image => this.deleteImage(image.id));
            await Promise.all(deletePromises);

            return { success: true };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }
}

// Categories API
export class CategoriesAPI {
    // Get all categories
    static async getAll(activeOnly = true) {
        try {
            let query = supabase
                .from(tables.CATEGORIES)
                .select('*')
                .order('sort_order', { ascending: true });

            if (activeOnly) {
                query = query.eq('is_active', true);
            }

            const { data, error } = await query;
            
            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Create category
    static async create(categoryData) {
        try {
            const { data, error } = await supabase
                .from(tables.CATEGORIES)
                .insert(categoryData)
                .select()
                .single();

            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Update category
    static async update(id, categoryData) {
        try {
            const { data, error } = await supabase
                .from(tables.CATEGORIES)
                .update({
                    ...categoryData,
                    updated_at: new Date().toISOString()
                })
                .eq('id', id)
                .select()
                .single();

            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Delete category
    static async delete(id) {
        try {
            // Check if category has products
            const { data: products, error: checkError } = await supabase
                .from(tables.PRODUCTS)
                .select('id')
                .eq('category_id', id)
                .limit(1);

            if (checkError) throw checkError;

            if (products.length > 0) {
                throw new Error('لا يمكن حذف القسم لأنه يحتوي على منتجات');
            }

            const { error } = await supabase
                .from(tables.CATEGORIES)
                .delete()
                .eq('id', id);

            if (error) throw error;

            return { success: true };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }
}

// Orders API
export class OrdersAPI {
    // Create new order
    static async create(orderData, orderItems) {
        try {
            // Generate order number
            orderData.order_number = utils.generateOrderNumber();
            
            // Calculate totals
            const subtotal = orderItems.reduce((sum, item) => 
                sum + (item.product_price * item.quantity), 0);
            
            orderData.subtotal = subtotal;
            orderData.total_amount = subtotal + (orderData.delivery_fee || 0) + 
                                   (orderData.tax_amount || 0) - (orderData.discount_amount || 0);

            // Create order
            const { data: order, error: orderError } = await supabase
                .from(tables.ORDERS)
                .insert(orderData)
                .select()
                .single();

            if (orderError) throw orderError;

            // Add order items
            const itemsWithOrderId = orderItems.map(item => ({
                ...item,
                order_id: order.id
            }));

            const { data: items, error: itemsError } = await supabase
                .from(tables.ORDER_ITEMS)
                .insert(itemsWithOrderId)
                .select();

            if (itemsError) throw itemsError;

            // Create or update customer
            await this.upsertCustomer({
                name: orderData.customer_name,
                phone: orderData.customer_phone,
                address: orderData.customer_address
            });

            return { success: true, data: { order, items } };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Get orders with filters
    static async getAll(filters = {}) {
        try {
            let query = supabase
                .from(tables.ORDERS)
                .select(`
                    *,
                    items:order_items(*),
                    customer:customers(*)
                `)
                .order('created_at', { ascending: false });

            // Apply filters
            if (filters.status) {
                query = query.eq('status', filters.status);
            }
            
            if (filters.dateFrom) {
                query = query.gte('created_at', filters.dateFrom);
            }
            
            if (filters.dateTo) {
                query = query.lte('created_at', filters.dateTo);
            }
            
            if (filters.customerPhone) {
                query = query.eq('customer_phone', filters.customerPhone);
            }

            const { data, error } = await query;
            
            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Update order status
    static async updateStatus(orderId, status) {
        try {
            const { data, error } = await supabase
                .from(tables.ORDERS)
                .update({ 
                    status,
                    updated_at: new Date().toISOString()
                })
                .eq('id', orderId)
                .select()
                .single();

            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Upsert customer
    static async upsertCustomer(customerData) {
        try {
            const { data, error } = await supabase
                .from(tables.CUSTOMERS)
                .upsert(customerData, { 
                    onConflict: 'phone',
                    ignoreDuplicates: false 
                })
                .select()
                .single();

            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }
}

// Analytics API
export class AnalyticsAPI {
    // Track event
    static async trackEvent(eventType, eventData, userInfo = {}) {
        try {
            const analyticsData = {
                event_type: eventType,
                event_data: eventData,
                user_id: userInfo.userId || null,
                session_id: userInfo.sessionId || null,
                ip_address: userInfo.ipAddress || null,
                user_agent: userInfo.userAgent || navigator.userAgent
            };

            const { data, error } = await supabase
                .from(tables.ANALYTICS)
                .insert(analyticsData)
                .select()
                .single();

            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Get analytics data
    static async getAnalytics(filters = {}) {
        try {
            let query = supabase
                .from(tables.ANALYTICS)
                .select('*')
                .order('created_at', { ascending: false });

            if (filters.eventType) {
                query = query.eq('event_type', filters.eventType);
            }
            
            if (filters.dateFrom) {
                query = query.gte('created_at', filters.dateFrom);
            }
            
            if (filters.dateTo) {
                query = query.lte('created_at', filters.dateTo);
            }

            const { data, error } = await query;
            
            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }
}

// Settings API
export class SettingsAPI {
    // Get all settings
    static async getAll(publicOnly = false) {
        try {
            let query = supabase
                .from(tables.SETTINGS)
                .select('*')
                .order('category', { ascending: true });

            if (publicOnly) {
                query = query.eq('is_public', true);
            }

            const { data, error } = await query;
            
            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }

    // Update setting
    static async update(key, value) {
        try {
            const { data, error } = await supabase
                .from(tables.SETTINGS)
                .update({ 
                    value,
                    updated_at: new Date().toISOString()
                })
                .eq('key', key)
                .select()
                .single();

            if (error) throw error;
            
            return { success: true, data };
        } catch (error) {
            return { success: false, error: handleSupabaseError(error) };
        }
    }
}

// Export all APIs
export default {
    ProductsAPI,
    CategoriesAPI,
    OrdersAPI,
    AnalyticsAPI,
    SettingsAPI
};
