// Supabase Configuration and Database Manager
// تكوين وإدارة قاعدة بيانات Supabase

// Supabase configuration
const supabaseConfig = {
    url: 'https://srnyumtbsyxiqkvwkcpi.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNybnl1bXRic3l4aXFrdndrY3BpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNjYzMjIsImV4cCI6MjA2Nzk0MjMyMn0.ROA5cGM5AQCvIBB-BGOLZPgEzR9rEoBkjLPboJJ0qJk'
};

class SupabaseManager {
    constructor() {
        this.client = null;
        this.isInitialized = false;
        
        this.init();
    }

    async init() {
        try {
            // Check if we have valid Supabase configuration
            if (supabaseConfig.url.includes('demo') || supabaseConfig.anonKey.includes('demo')) {
                console.log('Using fallback mode - no external database');
                this.isInitialized = false;
                return;
            }

            // Wait for Supabase SDK to load
            await this.waitForSupabase();

            // Initialize Supabase client
            this.client = supabase.createClient(supabaseConfig.url, supabaseConfig.anonKey);

            this.isInitialized = true;
            console.log('Supabase initialized successfully');

        } catch (error) {
            console.error('Error initializing Supabase:', error);
            this.isInitialized = false;
        }
    }

    async waitForSupabase() {
        return new Promise((resolve) => {
            const checkSupabase = () => {
                if (typeof supabase !== 'undefined') {
                    resolve();
                } else {
                    setTimeout(checkSupabase, 100);
                }
            };
            checkSupabase();
        });
    }

    // Products management
    async getProducts(filters = {}) {
        try {
            let query = this.client.from('products').select(`
                *,
                categories (
                    id,
                    name,
                    name_en
                ),
                product_images (
                    id,
                    image_url,
                    thumbnail_url,
                    is_primary,
                    sort_order
                )
            `);

            // Apply filters
            if (filters.category_id) {
                query = query.eq('category_id', filters.category_id);
            }

            if (filters.is_available !== undefined) {
                query = query.eq('is_available', filters.is_available);
            }

            if (filters.is_featured !== undefined) {
                query = query.eq('is_featured', filters.is_featured);
            }

            // Apply search
            if (filters.search) {
                query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
            }

            // Apply ordering
            const orderBy = filters.orderBy || 'created_at';
            const orderDirection = filters.orderDirection || 'desc';
            query = query.order(orderBy, { ascending: orderDirection === 'asc' });

            // Apply pagination
            if (filters.limit) {
                query = query.limit(filters.limit);
            }

            if (filters.offset) {
                query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
            }

            const { data, error } = await query;

            if (error) throw error;

            return { success: true, data: data || [] };
        } catch (error) {
            console.error('Error fetching products:', error);
            return { success: false, error: error.message };
        }
    }

    async getProduct(id) {
        try {
            const { data, error } = await this.client
                .from('products')
                .select(`
                    *,
                    categories (
                        id,
                        name,
                        name_en
                    ),
                    product_images (
                        id,
                        image_url,
                        thumbnail_url,
                        is_primary,
                        sort_order,
                        alt_text
                    )
                `)
                .eq('id', id)
                .single();

            if (error) throw error;

            return { success: true, data: data };
        } catch (error) {
            console.error('Error fetching product:', error);
            return { success: false, error: error.message };
        }
    }

    async createProduct(productData) {
        try {
            const { data, error } = await this.client
                .from('products')
                .insert([productData])
                .select()
                .single();

            if (error) throw error;

            return { success: true, data: data };
        } catch (error) {
            console.error('Error creating product:', error);
            return { success: false, error: error.message };
        }
    }

    async updateProduct(id, productData) {
        try {
            const { data, error } = await this.client
                .from('products')
                .update(productData)
                .eq('id', id)
                .select()
                .single();

            if (error) throw error;

            return { success: true, data: data };
        } catch (error) {
            console.error('Error updating product:', error);
            return { success: false, error: error.message };
        }
    }

    async deleteProduct(id) {
        try {
            const { error } = await this.client
                .from('products')
                .delete()
                .eq('id', id);

            if (error) throw error;

            return { success: true };
        } catch (error) {
            console.error('Error deleting product:', error);
            return { success: false, error: error.message };
        }
    }

    // Categories management
    async getCategories() {
        try {
            if (!this.isInitialized) {
                console.log('Supabase not initialized, using fallback');
                return {
                    success: false,
                    error: 'Supabase not initialized',
                    data: []
                };
            }

            console.log('Fetching categories from Supabase...');
            const { data, error } = await this.client
                .from('categories')
                .select('*')
                .eq('is_active', true)
                .order('sort_order', { ascending: true });

            if (error) {
                console.error('Supabase categories error:', error);
                throw error;
            }

            console.log('Categories fetched successfully:', data?.length || 0);
            return { success: true, data: data || [] };
        } catch (error) {
            console.error('Error fetching categories:', error);
            return { success: false, error: error.message };
        }
    }

    async createCategory(categoryData) {
        try {
            const { data, error } = await this.client
                .from('categories')
                .insert([categoryData])
                .select()
                .single();

            if (error) throw error;

            return { success: true, data: data };
        } catch (error) {
            console.error('Error creating category:', error);
            return { success: false, error: error.message };
        }
    }

    async updateCategory(id, categoryData) {
        try {
            const { data, error } = await this.client
                .from('categories')
                .update(categoryData)
                .eq('id', id)
                .select()
                .single();

            if (error) throw error;

            return { success: true, data: data };
        } catch (error) {
            console.error('Error updating category:', error);
            return { success: false, error: error.message };
        }
    }

    async deleteCategory(id) {
        try {
            const { error } = await this.client
                .from('categories')
                .delete()
                .eq('id', id);

            if (error) throw error;

            return { success: true };
        } catch (error) {
            console.error('Error deleting category:', error);
            return { success: false, error: error.message };
        }
    }

    // Orders management
    async getOrders(filters = {}) {
        try {
            let query = this.client.from('orders').select(`
                *,
                order_items (
                    id,
                    product_id,
                    quantity,
                    price,
                    products (
                        name,
                        name_en
                    )
                )
            `);

            // Apply filters
            if (filters.status) {
                query = query.eq('status', filters.status);
            }

            if (filters.customer_phone) {
                query = query.eq('customer_phone', filters.customer_phone);
            }

            if (filters.date_from) {
                query = query.gte('created_at', filters.date_from);
            }

            if (filters.date_to) {
                query = query.lte('created_at', filters.date_to);
            }

            // Apply ordering
            const orderBy = filters.orderBy || 'created_at';
            const orderDirection = filters.orderDirection || 'desc';
            query = query.order(orderBy, { ascending: orderDirection === 'asc' });

            // Apply pagination
            if (filters.limit) {
                query = query.limit(filters.limit);
            }

            if (filters.offset) {
                query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
            }

            const { data, error } = await query;

            if (error) throw error;

            return { success: true, data: data || [] };
        } catch (error) {
            console.error('Error fetching orders:', error);
            return { success: false, error: error.message };
        }
    }

    async createOrder(orderData) {
        try {
            // Start a transaction
            const { data: order, error: orderError } = await this.client
                .from('orders')
                .insert([{
                    customer_name: orderData.customer_name,
                    customer_phone: orderData.customer_phone,
                    customer_address: orderData.customer_address,
                    notes: orderData.notes,
                    subtotal: orderData.subtotal,
                    delivery_fee: orderData.delivery_fee,
                    tax: orderData.tax,
                    discount: orderData.discount,
                    total: orderData.total,
                    status: 'pending'
                }])
                .select()
                .single();

            if (orderError) throw orderError;

            // Insert order items
            const orderItems = orderData.items.map(item => ({
                order_id: order.id,
                product_id: item.product_id,
                quantity: item.quantity,
                price: item.price
            }));

            const { error: itemsError } = await this.client
                .from('order_items')
                .insert(orderItems);

            if (itemsError) throw itemsError;

            return { success: true, data: order };
        } catch (error) {
            console.error('Error creating order:', error);
            return { success: false, error: error.message };
        }
    }

    async updateOrderStatus(id, status) {
        try {
            const { data, error } = await this.client
                .from('orders')
                .update({ status: status })
                .eq('id', id)
                .select()
                .single();

            if (error) throw error;

            return { success: true, data: data };
        } catch (error) {
            console.error('Error updating order status:', error);
            return { success: false, error: error.message };
        }
    }

    // Product Images management
    async getProductImages(productId) {
        try {
            const { data, error } = await this.client
                .from('product_images')
                .select('*')
                .eq('product_id', productId)
                .order('sort_order', { ascending: true });

            if (error) throw error;

            return { success: true, data: data || [] };
        } catch (error) {
            console.error('Error fetching product images:', error);
            return { success: false, error: error.message };
        }
    }

    async createProductImage(imageData) {
        try {
            const { data, error } = await this.client
                .from('product_images')
                .insert([imageData])
                .select()
                .single();

            if (error) throw error;

            return { success: true, data: data };
        } catch (error) {
            console.error('Error creating product image:', error);
            return { success: false, error: error.message };
        }
    }

    async deleteProductImage(id) {
        try {
            const { error } = await this.client
                .from('product_images')
                .delete()
                .eq('id', id);

            if (error) throw error;

            return { success: true };
        } catch (error) {
            console.error('Error deleting product image:', error);
            return { success: false, error: error.message };
        }
    }

    // Analytics and Statistics
    async getOrdersStats(dateFrom, dateTo) {
        try {
            const { data, error } = await this.client
                .rpc('get_orders_stats', {
                    date_from: dateFrom,
                    date_to: dateTo
                });

            if (error) throw error;

            return { success: true, data: data };
        } catch (error) {
            console.error('Error fetching orders stats:', error);
            return { success: false, error: error.message };
        }
    }

    async getTopProducts(limit = 10) {
        try {
            const { data, error } = await this.client
                .rpc('get_top_products', { limit_count: limit });

            if (error) throw error;

            return { success: true, data: data || [] };
        } catch (error) {
            console.error('Error fetching top products:', error);
            return { success: false, error: error.message };
        }
    }

    // Real-time subscriptions
    subscribeToOrders(callback) {
        return this.client
            .channel('orders')
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'orders'
            }, callback)
            .subscribe();
    }

    subscribeToProducts(callback) {
        return this.client
            .channel('products')
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'products'
            }, callback)
            .subscribe();
    }

    // Utility methods
    async testConnection() {
        try {
            const { data, error } = await this.client
                .from('categories')
                .select('count')
                .limit(1);

            if (error) throw error;

            return { success: true, message: 'Connection successful' };
        } catch (error) {
            console.error('Connection test failed:', error);
            return { success: false, error: error.message };
        }
    }
}

// Initialize Supabase Manager
let supabaseManager;

// Wait for DOM to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        supabaseManager = new SupabaseManager();
    });
} else {
    supabaseManager = new SupabaseManager();
}

// Make Supabase available globally
window.supabaseManager = supabaseManager;
window.supabase = () => supabaseManager?.client;

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SupabaseManager;
}
