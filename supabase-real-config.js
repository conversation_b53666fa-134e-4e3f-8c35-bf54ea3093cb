// Real Supabase Configuration
// تكوين Supabase الحقيقي

// ⚠️ يجب تحديث هذه المعلومات بالمعلومات الحقيقية من Supabase Dashboard
const realSupabaseConfig = {
    // احصل على هذه المعلومات من:
    // https://app.supabase.com/project/YOUR_PROJECT_ID/settings/api
    
    url: 'https://YOUR_PROJECT_ID.supabase.co',
    anonKey: 'YOUR_ANON_KEY_HERE'
};

// Instructions for setting up Supabase:
console.log(`
🟢 لإعداد Supabase الحقيقي:

1️⃣ إنشاء مشروع جديد:
   - اذهب إلى: https://supabase.com/dashboard
   - انقر "New project" ➕
   - اسم المشروع: "alashrafi-restaurant"
   - كلمة مرور قاعدة البيانات: (احفظها بأمان) 🔐
   - المنطقة: اختر الأقرب لك
   - انقر "Create new project"

2️⃣ الحصول على معلومات الاتصال:
   - اذهب إلى Settings → API
   - انسخ "Project URL" 🔗
   - انسخ "anon public" key 🔑

3️⃣ إنشاء الجداول:
   - اذهب إلى SQL Editor
   - انسخ محتوى ملف "database-schema.sql"
   - شغّل الكود لإنشاء الجداول 📊

4️⃣ تحديث التكوين:
   - استبدل URL و anonKey في ملف supabase-config.js
   - احفظ الملف 💾

📋 الجداول المطلوبة:
✅ categories (الأقسام)
✅ products (المنتجات)  
✅ orders (الطلبات)
✅ order_items (عناصر الطلبات)
✅ customers (العملاء)
✅ admin_users (مستخدمي الإدارة)
✅ restaurant_settings (إعدادات المطعم)

🔒 Row Level Security (RLS):
- سيتم تفعيل RLS تلقائياً
- القواعد محددة في ملف database-schema.sql

🧪 اختبار الاتصال:
- بعد التحديث، افتح Developer Tools (F12)
- ابحث عن رسائل "Supabase connected" في Console
- إذا ظهرت أخطاء، تحقق من URL و API Key

⚡ نصائح مهمة:
- لا تشارك anon key في أماكن عامة
- استخدم Environment Variables في الإنتاج
- راجع حدود الاستخدام في الخطة المجانية
- فعّل النسخ الاحتياطية التلقائية

🆘 في حالة المشاكل:
1. تحقق من صحة URL (يجب أن ينتهي بـ .supabase.co)
2. تحقق من صحة anon key (يبدأ بـ eyJ...)
3. تأكد من تشغيل SQL schema بنجاح
4. تحقق من إعدادات RLS
`);

// Export config
if (typeof module !== 'undefined' && module.exports) {
    module.exports = realSupabaseConfig;
}
