/* Menu Management Styles */
/* تنسيقات إدارة القائمة */

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #2c5aa0, #007bff);
    border-radius: 15px;
    color: white;
}

.page-title h2 {
    margin: 0 0 5px 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.page-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 1rem;
}

.page-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.page-actions .btn-primary,
.page-actions .btn-secondary {
    white-space: nowrap;
}

/* Filters Section */
.filters-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    flex-wrap: wrap;
    gap: 20px;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #2c5aa0;
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.filter-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-controls select {
    padding: 10px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    background: white;
    min-width: 120px;
}

.view-controls {
    display: flex;
    gap: 5px;
}

.view-btn {
    padding: 10px 12px;
    border: 2px solid #e1e5e9;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #666;
}

.view-btn.active,
.view-btn:hover {
    background: #2c5aa0;
    border-color: #2c5aa0;
    color: white;
}

/* Products Container */
.products-container {
    margin-bottom: 30px;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
}

.products-grid.list-view {
    grid-template-columns: 1fr;
}

/* Product Card */
.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.image-count {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.product-status {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-available {
    background: #d4edda;
    color: #155724;
}

.status-unavailable {
    background: #f8d7da;
    color: #721c24;
}

.product-content {
    padding: 20px;
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.product-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin: 0;
    flex: 1;
}

.product-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c5aa0;
    white-space: nowrap;
    margin-right: 10px;
}

.product-description {
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
}

.product-category,
.preparation-time {
    display: flex;
    align-items: center;
    gap: 5px;
}

.featured-badge {
    position: absolute;
    top: -5px;
    right: 15px;
    background: #ffc107;
    color: #333;
    padding: 5px 15px;
    border-radius: 0 0 10px 10px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.product-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn-action {
    flex: 1;
    min-width: 0;
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.btn-edit {
    background: #17a2b8;
    color: white;
}

.btn-edit:hover {
    background: #138496;
    transform: translateY(-2px);
}

.btn-images {
    background: #6f42c1;
    color: white;
}

.btn-images:hover {
    background: #5a32a3;
    transform: translateY(-2px);
}

.btn-toggle {
    background: #28a745;
    color: white;
}

.btn-toggle.unavailable {
    background: #6c757d;
}

.btn-toggle:hover {
    background: #218838;
    transform: translateY(-2px);
}

.btn-toggle.unavailable:hover {
    background: #5a6268;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-delete:hover {
    background: #c82333;
    transform: translateY(-2px);
}

/* List View Styles */
.products-grid.list-view .product-card {
    display: flex;
    align-items: center;
    padding: 20px;
}

.products-grid.list-view .product-image {
    width: 120px;
    height: 120px;
    border-radius: 10px;
    margin-left: 20px;
    flex-shrink: 0;
}

.products-grid.list-view .product-content {
    flex: 1;
    padding: 0;
}

.products-grid.list-view .product-actions {
    margin-right: 20px;
    flex-direction: column;
    width: 120px;
    flex-shrink: 0;
}

.products-grid.list-view .btn-action {
    flex: none;
    width: 100%;
}

/* Product Form Styles */
.product-form {
    max-width: none;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-section {
    margin: 30px 0;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 15px;
}

.form-section h4 {
    margin: 0 0 20px 0;
    color: #2c5aa0;
    font-size: 1.2rem;
}

.images-management {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.current-images {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 15px;
}

.current-image {
    position: relative;
    aspect-ratio: 1;
    border-radius: 10px;
    overflow: hidden;
    background: #e9ecef;
}

.current-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.current-image .remove-btn {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    cursor: pointer;
    font-size: 0.8rem;
}

.image-actions {
    display: flex;
    gap: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #2c5aa0;
    border-color: #2c5aa0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.8rem;
}

/* Category Management */
.category-form {
    margin-bottom: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 15px;
}

.category-form h4 {
    margin: 0 0 20px 0;
    color: #2c5aa0;
}

.categories-list h4 {
    margin-bottom: 20px;
    color: #333;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.category-item {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.category-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.category-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.category-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.category-actions {
    display: flex;
    gap: 10px;
}

.category-actions button {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.btn-edit-category {
    background: #17a2b8;
    color: white;
}

.btn-delete-category {
    background: #dc3545;
    color: white;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.pagination {
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-btn {
    padding: 10px 15px;
    border: 2px solid #e1e5e9;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-btn:hover:not(:disabled) {
    background: #2c5aa0;
    border-color: #2c5aa0;
    color: white;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 5px;
}

.page-number {
    width: 40px;
    height: 40px;
    border: 2px solid #e1e5e9;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.page-number.active,
.page-number:hover {
    background: #2c5aa0;
    border-color: #2c5aa0;
    color: white;
}

.pagination-info {
    color: #666;
    font-size: 0.9rem;
}

/* No Products State */
.no-products {
    text-align: center;
    padding: 60px 30px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.no-products i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

.no-products h3 {
    color: #333;
    margin-bottom: 10px;
}

.no-products p {
    color: #666;
    margin-bottom: 25px;
}

/* Large Modal */
.large-modal {
    max-width: 900px;
    width: 90vw;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .page-actions {
        justify-content: center;
    }

    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-controls {
        justify-content: center;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .pagination-container {
        flex-direction: column;
        gap: 15px;
    }

    .large-modal {
        width: 95vw;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .product-actions {
        flex-direction: column;
    }

    .btn-action {
        flex: none;
    }

    .image-actions {
        flex-direction: column;
    }

    .page-numbers {
        flex-wrap: wrap;
        justify-content: center;
    }
}
