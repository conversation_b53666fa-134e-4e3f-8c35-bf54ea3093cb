<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص شامل للنظام - مطعم محمد الاشرافي</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .check-container {
            max-width: 1200px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .check-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        
        .check-section.success {
            border-left-color: #27ae60;
            background: #d5f4e6;
        }
        
        .check-section.error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        
        .check-section.warning {
            border-left-color: #f39c12;
            background: #fef9e7;
        }
        
        .check-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .check-item:last-child {
            border-bottom: none;
        }
        
        .check-icon {
            width: 30px;
            text-align: center;
            margin-left: 15px;
        }
        
        .check-icon.success {
            color: #27ae60;
        }
        
        .check-icon.error {
            color: #e74c3c;
        }
        
        .check-icon.warning {
            color: #f39c12;
        }
        
        .check-icon.pending {
            color: #3498db;
        }
        
        .check-details {
            flex: 1;
        }
        
        .check-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .check-description {
            font-size: 14px;
            color: #666;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn.success {
            background: #27ae60;
        }
        
        .btn.danger {
            background: #e74c3c;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.5s ease;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .summary-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .summary-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .summary-number.success {
            color: #27ae60;
        }
        
        .summary-number.error {
            color: #e74c3c;
        }
        
        .summary-number.warning {
            color: #f39c12;
        }
    </style>
</head>
<body>
    <div class="check-container">
        <div class="check-header">
            <h1><i class="fas fa-clipboard-check"></i> فحص شامل للنظام</h1>
            <p>فحص جميع وظائف نظام مطعم محمد الاشرافي</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">جاري التحضير...</div>
        </div>

        <!-- Summary -->
        <div class="summary-grid" id="summaryGrid">
            <div class="summary-card">
                <div class="summary-number success" id="passedCount">0</div>
                <div>اختبارات نجحت</div>
            </div>
            <div class="summary-card">
                <div class="summary-number error" id="failedCount">0</div>
                <div>اختبارات فشلت</div>
            </div>
            <div class="summary-card">
                <div class="summary-number warning" id="warningCount">0</div>
                <div>تحذيرات</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="totalCount">0</div>
                <div>إجمالي الاختبارات</div>
            </div>
        </div>

        <!-- Control Buttons -->
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn" onclick="runAllTests()">تشغيل جميع الاختبارات</button>
            <button class="btn success" onclick="runQuickTest()">فحص سريع</button>
            <button class="btn" onclick="exportReport()">تصدير التقرير</button>
            <button class="btn" onclick="fixIssues()">إصلاح المشاكل</button>
        </div>

        <!-- Frontend Tests -->
        <div class="check-section" id="frontendSection">
            <h2><i class="fas fa-desktop"></i> اختبارات الواجهة الأمامية</h2>
            <div id="frontendTests"></div>
        </div>

        <!-- Database Tests -->
        <div class="check-section" id="databaseSection">
            <h2><i class="fas fa-database"></i> اختبارات قواعد البيانات</h2>
            <div id="databaseTests"></div>
        </div>

        <!-- Admin Panel Tests -->
        <div class="check-section" id="adminSection">
            <h2><i class="fas fa-user-shield"></i> اختبارات لوحة الإدارة</h2>
            <div id="adminTests"></div>
        </div>

        <!-- WhatsApp Tests -->
        <div class="check-section" id="whatsappSection">
            <h2><i class="fab fa-whatsapp"></i> اختبارات الواتساب</h2>
            <div id="whatsappTests"></div>
        </div>

        <!-- Performance Tests -->
        <div class="check-section" id="performanceSection">
            <h2><i class="fas fa-tachometer-alt"></i> اختبارات الأداء</h2>
            <div id="performanceTests"></div>
        </div>

        <!-- Security Tests -->
        <div class="check-section" id="securitySection">
            <h2><i class="fas fa-shield-alt"></i> اختبارات الأمان</h2>
            <div id="securityTests"></div>
        </div>

        <!-- Results -->
        <div id="testResults" style="margin-top: 30px;"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-storage-compat.js"></script>
    
    <!-- Supabase SDK -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- Configuration -->
    <script src="js/firebase-config.js"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/supabase-storage.js"></script>
    <script src="js/menu-data-manager.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/whatsapp-enhanced.js"></script>
    
    <!-- System Check Script -->
    <script src="js/system-check.js"></script>
</body>
</html>
