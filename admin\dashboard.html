<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - محمد الاشرافي</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/products-dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="admin-dashboard">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-header-content">
            <div class="admin-logo">
                <i class="fas fa-utensils"></i>
                <h1>محمد الاشرافي - لوحة التحكم</h1>
            </div>
            <div class="admin-nav">
                <div class="admin-user">
                    <i class="fas fa-user-circle"></i>
                    <span id="adminUsername">المدير</span>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </header>

    <!-- Dashboard Content -->
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar">
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active">
                        <a href="#overview" class="nav-link" data-section="overview">
                            <i class="fas fa-chart-line"></i>
                            نظرة عامة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#products-management" class="nav-link" data-section="products-management">
                            <i class="fas fa-utensils"></i>
                            إدارة المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#orders" class="nav-link" data-section="orders">
                            <i class="fas fa-shopping-cart"></i>
                            الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#analytics" class="nav-link" data-section="analytics">
                            <i class="fas fa-chart-bar"></i>
                            التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#settings" class="nav-link" data-section="settings">
                            <i class="fas fa-cog"></i>
                            الإعدادات
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <a href="../index.html" class="view-site-btn" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    عرض الموقع
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Overview Section -->
            <section id="overview" class="dashboard-section active">
                <div class="section-header">
                    <h2>نظرة عامة</h2>
                    <p>ملخص سريع عن أداء المطعم</p>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalOrders">0</h3>
                            <p>إجمالي الطلبات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalRevenue">0</h3>
                            <p>إجمالي المبيعات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalMenuItems">0</h3>
                            <p>عناصر القائمة</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalCustomers">0</h3>
                            <p>العملاء</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>الطلبات الأخيرة</h3>
                        <button class="btn-secondary" onclick="showSection('orders')">
                            عرض الكل
                        </button>
                    </div>
                    <div class="card-content">
                        <div id="recentOrdersList" class="orders-list">
                            <!-- Recent orders will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Popular Items -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>الأطباق الأكثر طلباً</h3>
                    </div>
                    <div class="card-content">
                        <div id="popularItemsList" class="popular-items">
                            <!-- Popular items will be loaded here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Products Management Section -->
            <section id="products-management" class="dashboard-section">
                <div class="section-header">
                    <h2><i class="fas fa-utensils"></i> إدارة المنتجات</h2>
                    <div class="header-actions">
                        <div class="database-status" id="databaseStatus">
                            <div class="status-indicator" id="statusIndicator">
                                <i class="fas fa-circle"></i>
                            </div>
                            <span id="statusText">جاري الاتصال...</span>
                        </div>
                        <button class="btn-primary" onclick="dashboardProductManager.openProductModal()">
                            <i class="fas fa-plus"></i>
                            إضافة منتج جديد
                        </button>
                    </div>
                </div>

                <!-- Statistics Row -->
                <div class="products-stats">
                    <div class="stat-card">
                        <div class="stat-icon products">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="dashboardTotalProducts">0</h3>
                            <p>إجمالي المنتجات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon featured">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="dashboardFeaturedProducts">0</h3>
                            <p>منتجات مميزة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon available">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="dashboardAvailableProducts">0</h3>
                            <p>منتجات متوفرة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon revenue">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="dashboardAveragePrice">0</h3>
                            <p>متوسط السعر</p>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="dashboard-card">
                    <div class="card-content">
                        <div class="products-filters">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" id="dashboardProductSearch" placeholder="البحث في المنتجات...">
                            </div>

                            <select id="dashboardCategoryFilter" class="filter-select">
                                <option value="all">جميع الفئات</option>
                                <option value="1">المقبلات</option>
                                <option value="2">الأطباق الرئيسية</option>
                                <option value="3">المشروبات</option>
                                <option value="4">الحلويات</option>
                            </select>

                            <select id="dashboardAvailabilityFilter" class="filter-select">
                                <option value="all">جميع الحالات</option>
                                <option value="available">متوفر</option>
                                <option value="unavailable">غير متوفر</option>
                            </select>

                            <div class="bulk-actions">
                                <input type="checkbox" id="dashboardSelectAll">
                                <label for="dashboardSelectAll">تحديد الكل</label>
                                <button class="btn-danger btn-sm" onclick="dashboardProductManager.bulkDelete()">
                                    <i class="fas fa-trash"></i> حذف المحدد
                                </button>
                                <button class="btn-info btn-sm" onclick="dashboardProductManager.refreshFromDatabase()">
                                    <i class="fas fa-sync"></i> تحديث من قاعدة البيانات
                                </button>
                                <button class="btn-warning btn-sm" onclick="dashboardProductManager.syncAllProductsToDatabase()">
                                    <i class="fas fa-cloud-upload-alt"></i> مزامنة مع قاعدة البيانات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Grid -->
                <div class="dashboard-card">
                    <div class="card-content">
                        <div class="products-grid" id="dashboardProductsGrid">
                            <!-- Products will be loaded here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Orders Section -->
            <section id="orders" class="dashboard-section">
                <div class="section-header">
                    <h2>إدارة الطلبات</h2>
                    <div class="section-actions">
                        <select id="orderStatusFilter" class="filter-select">
                            <option value="all">جميع الطلبات</option>
                            <option value="pending">قيد الانتظار</option>
                            <option value="processing">قيد التحضير</option>
                            <option value="completed">مكتملة</option>
                            <option value="cancelled">ملغية</option>
                        </select>
                        <button class="btn-secondary" onclick="refreshOrders()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-content">
                        <div id="ordersContainer" class="orders-container">
                            <!-- Orders will be loaded here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics" class="dashboard-section">
                <div class="section-header">
                    <h2>التقارير والإحصائيات</h2>
                </div>

                <div class="analytics-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>مبيعات الشهر</h3>
                        </div>
                        <div class="card-content">
                            <div id="monthlyChart" class="chart-container">
                                <!-- Chart will be rendered here -->
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>الأطباق الأكثر مبيعاً</h3>
                        </div>
                        <div class="card-content">
                            <div id="topItemsChart" class="chart-container">
                                <!-- Chart will be rendered here -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings" class="dashboard-section">
                <div class="section-header">
                    <h2>إعدادات النظام</h2>
                </div>

                <div class="settings-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>معلومات المطعم</h3>
                        </div>
                        <div class="card-content">
                            <form id="restaurantInfoForm" class="settings-form">
                                <div class="form-group">
                                    <label>اسم المطعم</label>
                                    <input type="text" id="restaurantName" value="محمد الاشرافي">
                                </div>
                                <div class="form-group">
                                    <label>رقم الواتساب</label>
                                    <input type="tel" id="whatsappNumber" value="+201014840269">
                                </div>
                                <div class="form-group">
                                    <label>العنوان</label>
                                    <textarea id="restaurantAddress" rows="3">القاهرة، مصر</textarea>
                                </div>
                                <button type="submit" class="btn-primary">حفظ التغييرات</button>
                            </form>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>إعدادات النظام</h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-options">
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" id="enableNotifications" checked>
                                        <span>تفعيل الإشعارات</span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" id="autoAcceptOrders">
                                        <span>قبول الطلبات تلقائياً</span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" id="showPrices" checked>
                                        <span>عرض الأسعار للعملاء</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Add/Edit Item Modal -->
    <div id="itemModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة عنصر جديد</h3>
                <button class="close-modal" onclick="closeItemModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="itemForm">
                    <div class="form-group">
                        <label>اسم العنصر</label>
                        <input type="text" id="itemName" required>
                    </div>
                    <div class="form-group">
                        <label>الوصف</label>
                        <textarea id="itemDescription" rows="3" required></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>السعر (جنيه)</label>
                            <input type="number" id="itemPrice" min="0" step="0.5" required>
                        </div>
                        <div class="form-group">
                            <label>القسم</label>
                            <select id="itemCategory" required>
                                <option value="">اختر القسم</option>
                                <option value="appetizers">المقبلات</option>
                                <option value="main-dishes">الأطباق الرئيسية</option>
                                <option value="beverages">المشروبات</option>
                                <option value="desserts">الحلويات</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>الأيقونة</label>
                        <select id="itemIcon" required>
                            <option value="">اختر الأيقونة</option>
                            <option value="fas fa-seedling">🌱 نباتي</option>
                            <option value="fas fa-drumstick-bite">🍗 لحوم</option>
                            <option value="fas fa-coffee">☕ مشروبات ساخنة</option>
                            <option value="fas fa-glass-water">🥤 مشروبات باردة</option>
                            <option value="fas fa-ice-cream">🍨 حلويات</option>
                            <option value="fas fa-pizza-slice">🍕 بيتزا</option>
                            <option value="fas fa-hamburger">🍔 برجر</option>
                            <option value="fas fa-utensils">🍽️ عام</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="itemAvailable" checked>
                            <span class="checkmark"></span>
                            متوفر
                        </label>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" onclick="closeItemModal()">إلغاء</button>
                        <button type="submit" class="btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Simple Authentication Check - No Refresh Loop
        // فحص مصادقة بسيط - بدون حلقة ريفريش

        function checkAuth() {
            const session = localStorage.getItem('admin_logged_in');

            if (!session) {
                // No session, redirect to login
                window.location.replace('login.html');
                return false;
            }

            try {
                const sessionData = JSON.parse(session);
                const loginTime = new Date(sessionData.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

                if (hoursDiff > 24) {
                    // Session expired
                    localStorage.removeItem('admin_logged_in');
                    window.location.replace('login.html');
                    return false;
                }

                // Valid session, update UI
                updateUserInfo(sessionData);
                return true;

            } catch (error) {
                // Invalid session
                localStorage.removeItem('admin_logged_in');
                window.location.replace('login.html');
                return false;
            }
        }

        function updateUserInfo(sessionData) {
            const userNameElement = document.getElementById('userName');
            if (userNameElement) {
                userNameElement.textContent = sessionData.username;
            }

            const userRoleElement = document.getElementById('userRole');
            if (userRoleElement) {
                userRoleElement.textContent = sessionData.role === 'owner' ? 'مالك' : 'مدير';
            }
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('admin_logged_in');
                window.location.replace('login.html');
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Check auth immediately
            if (!checkAuth()) {
                return; // Will redirect to login
            }

            // Setup logout button
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    logout();
                });
            }

            console.log('✅ Dashboard loaded successfully');
        });
    </script>

    <!-- Supabase SDK -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>

    <!-- Dashboard Scripts -->
    <script src="../js/supabase-config.js"></script>
    <script src="../js/supabase-storage.js"></script>
    <script src="js/supabase-image-manager.js"></script>
    <script src="js/dashboard-product-manager.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>
