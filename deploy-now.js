#!/usr/bin/env node

// Quick Deploy Script for Mohamed <PERSON>ra<PERSON> Restaurant System
// سكريپت النشر السريع لنظام مطعم محمد الاشرافي

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 بدء نشر نظام مطعم محمد الاشرافي...');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

// Step 1: Check Firebase CLI
console.log('1️⃣ فحص Firebase CLI...');
try {
    execSync('firebase --version', { stdio: 'ignore' });
    console.log('✅ Firebase CLI متوفر');
} catch (error) {
    console.log('❌ Firebase CLI غير مثبت');
    console.log('📥 جاري تثبيت Firebase CLI...');
    try {
        execSync('npm install -g firebase-tools', { stdio: 'inherit' });
        console.log('✅ تم تثبيت Firebase CLI');
    } catch (installError) {
        console.error('❌ فشل في تثبيت Firebase CLI');
        process.exit(1);
    }
}

// Step 2: Login to Firebase
console.log('\n2️⃣ تسجيل الدخول إلى Firebase...');
try {
    execSync('firebase projects:list', { stdio: 'ignore' });
    console.log('✅ تم تسجيل الدخول مسبقاً');
} catch (error) {
    console.log('🔐 يرجى تسجيل الدخول إلى Firebase...');
    try {
        execSync('firebase login', { stdio: 'inherit' });
        console.log('✅ تم تسجيل الدخول بنجاح');
    } catch (loginError) {
        console.error('❌ فشل في تسجيل الدخول');
        process.exit(1);
    }
}

// Step 3: Initialize Firebase project
console.log('\n3️⃣ تهيئة مشروع Firebase...');

// Create firebase.json if it doesn't exist
const firebaseConfigPath = path.join(__dirname, 'firebase.json');
if (!fs.existsSync(firebaseConfigPath)) {
    const firebaseConfig = {
        "hosting": {
            "public": ".",
            "ignore": [
                "firebase.json",
                "**/.*",
                "**/node_modules/**",
                "package*.json",
                "README.md",
                "DEPLOYMENT-GUIDE.md",
                "scripts/**",
                "reports/**",
                "database-schema.sql",
                ".env*",
                "*.log",
                "deploy-now.js"
            ],
            "rewrites": [
                {
                    "source": "/admin/**",
                    "destination": "/admin/login.html"
                }
            ],
            "headers": [
                {
                    "source": "**/*.@(jpg|jpeg|gif|png|webp|svg|ico)",
                    "headers": [
                        {
                            "key": "Cache-Control",
                            "value": "max-age=31536000, immutable"
                        }
                    ]
                },
                {
                    "source": "**/*.@(js|css)",
                    "headers": [
                        {
                            "key": "Cache-Control",
                            "value": "max-age=86400"
                        }
                    ]
                },
                {
                    "source": "**/*.html",
                    "headers": [
                        {
                            "key": "Cache-Control",
                            "value": "max-age=3600"
                        },
                        {
                            "key": "X-Content-Type-Options",
                            "value": "nosniff"
                        },
                        {
                            "key": "X-Frame-Options",
                            "value": "DENY"
                        },
                        {
                            "key": "X-XSS-Protection",
                            "value": "1; mode=block"
                        }
                    ]
                }
            ],
            "cleanUrls": true,
            "trailingSlash": false
        },
        "firestore": {
            "rules": "firestore.rules",
            "indexes": "firestore.indexes.json"
        },
        "storage": {
            "rules": "storage.rules"
        }
    };
    
    fs.writeFileSync(firebaseConfigPath, JSON.stringify(firebaseConfig, null, 2));
    console.log('✅ تم إنشاء firebase.json');
}

// Step 4: Create or use existing Firebase project
console.log('\n4️⃣ إعداد مشروع Firebase...');
try {
    // Try to use existing project
    execSync('firebase use --add', { stdio: 'inherit' });
    console.log('✅ تم ربط المشروع');
} catch (error) {
    console.log('ℹ️ سيتم استخدام المشروع الافتراضي');
}

// Step 5: Deploy Firestore rules
console.log('\n5️⃣ نشر قواعد قاعدة البيانات...');
try {
    if (fs.existsSync('firestore.rules')) {
        execSync('firebase deploy --only firestore:rules', { stdio: 'inherit' });
        console.log('✅ تم نشر قواعد Firestore');
    }
} catch (error) {
    console.log('⚠️ تخطي نشر قواعد Firestore');
}

// Step 6: Deploy Storage rules
console.log('\n6️⃣ نشر قواعد التخزين...');
try {
    if (fs.existsSync('storage.rules')) {
        execSync('firebase deploy --only storage:rules', { stdio: 'inherit' });
        console.log('✅ تم نشر قواعد Storage');
    }
} catch (error) {
    console.log('⚠️ تخطي نشر قواعد Storage');
}

// Step 7: Deploy hosting
console.log('\n7️⃣ نشر الموقع...');
try {
    execSync('firebase deploy --only hosting', { stdio: 'inherit' });
    console.log('✅ تم نشر الموقع بنجاح!');
} catch (error) {
    console.error('❌ فشل في نشر الموقع');
    process.exit(1);
}

// Step 8: Get project info
console.log('\n8️⃣ الحصول على معلومات المشروع...');
try {
    const projectInfo = execSync('firebase projects:list --json', { encoding: 'utf8' });
    const projects = JSON.parse(projectInfo);
    const currentProject = projects.find(p => p.state === 'ACTIVE');
    
    if (currentProject) {
        console.log('\n🎉 تم النشر بنجاح!');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        console.log(`📱 الموقع الرئيسي: https://${currentProject.projectId}.web.app`);
        console.log(`🔧 لوحة الإدارة: https://${currentProject.projectId}.web.app/admin`);
        console.log(`🔥 Firebase Console: https://console.firebase.google.com/project/${currentProject.projectId}`);
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        
        // Save deployment info
        const deploymentInfo = {
            timestamp: new Date().toISOString(),
            projectId: currentProject.projectId,
            websiteUrl: `https://${currentProject.projectId}.web.app`,
            adminUrl: `https://${currentProject.projectId}.web.app/admin`,
            consoleUrl: `https://console.firebase.google.com/project/${currentProject.projectId}`,
            status: 'success'
        };
        
        fs.writeFileSync('deployment-info.json', JSON.stringify(deploymentInfo, null, 2));
        console.log('💾 تم حفظ معلومات النشر في deployment-info.json');
        
        // Show admin credentials
        console.log('\n🔐 بيانات تسجيل الدخول للإدارة:');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        console.log('👤 المدير العام:');
        console.log('   البريد: <EMAIL>');
        console.log('   كلمة المرور: admin123');
        console.log('');
        console.log('👤 صاحب المطعم:');
        console.log('   البريد: محمد');
        console.log('   كلمة المرور: alashrafi2024');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        
        console.log('\n📋 الخطوات التالية:');
        console.log('1. اذهب إلى لوحة الإدارة وسجل الدخول');
        console.log('2. أضف منتجات جديدة مع الصور');
        console.log('3. اختبر نظام الطلبات');
        console.log('4. تأكد من عمل الواتساب');
        console.log('5. شارك الرابط مع العملاء');
        
    }
} catch (error) {
    console.log('⚠️ لا يمكن الحصول على معلومات المشروع');
    console.log('✅ ولكن النشر تم بنجاح!');
}

console.log('\n🎊 مبروك! نظام مطعم محمد الاشرافي أصبح متاحاً الآن!');
console.log('🙏 شكراً لاستخدام النظام');
