// Supabase Storage Manager
// مدير تخزين Supabase

class SupabaseStorageManager {
    constructor() {
        this.client = null;
        this.bucketName = 'restaurant-images';
        this.isInitialized = false;
        this.maxFileSize = 5 * 1024 * 1024; // 5MB
        this.allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
        
        this.init();
    }

    async init() {
        try {
            // Wait for Supabase to be available
            await this.waitForSupabase();
            
            if (window.supabaseManager && window.supabaseManager.client) {
                this.client = window.supabaseManager.client;
                this.isInitialized = true;
                console.log('✅ Supabase Storage Manager initialized');
                
                // Test bucket access
                await this.testBucketAccess();
            } else {
                throw new Error('Supabase client not available');
            }
        } catch (error) {
            console.error('❌ Error initializing Supabase Storage:', error);
            this.isInitialized = false;
        }
    }

    async waitForSupabase() {
        return new Promise((resolve) => {
            const checkSupabase = () => {
                if (window.supabaseManager && window.supabaseManager.isInitialized) {
                    resolve();
                } else {
                    setTimeout(checkSupabase, 100);
                }
            };
            checkSupabase();
        });
    }

    async testBucketAccess() {
        try {
            const { data, error } = await this.client.storage.from(this.bucketName).list('', {
                limit: 1
            });
            
            if (error) {
                console.warn('⚠️ Bucket access test failed:', error.message);
            } else {
                console.log('✅ Bucket access test successful');
            }
        } catch (error) {
            console.warn('⚠️ Bucket test error:', error.message);
        }
    }

    // Upload single image
    async uploadImage(file, path = null) {
        try {
            if (!this.isInitialized) {
                throw new Error('Storage manager not initialized');
            }

            // Validate file
            const validation = this.validateFile(file);
            if (!validation.valid) {
                throw new Error(validation.error);
            }

            // Generate unique filename if path not provided
            if (!path) {
                const timestamp = Date.now();
                const randomId = Math.random().toString(36).substring(2, 15);
                const extension = file.name.split('.').pop().toLowerCase();
                path = `products/${timestamp}_${randomId}.${extension}`;
            }

            console.log('📤 Uploading image:', path);

            // Upload file
            const { data, error } = await this.client.storage
                .from(this.bucketName)
                .upload(path, file, {
                    cacheControl: '3600',
                    upsert: false
                });

            if (error) {
                throw error;
            }

            // Get public URL
            const { data: urlData } = this.client.storage
                .from(this.bucketName)
                .getPublicUrl(path);

            console.log('✅ Image uploaded successfully:', urlData.publicUrl);

            return {
                success: true,
                data: {
                    path: path,
                    url: urlData.publicUrl,
                    size: file.size,
                    type: file.type,
                    name: file.name
                }
            };

        } catch (error) {
            console.error('❌ Upload error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Upload multiple images
    async uploadMultipleImages(files) {
        const results = [];
        
        for (const file of files) {
            const result = await this.uploadImage(file);
            results.push(result);
        }

        const successful = results.filter(r => r.success);
        const failed = results.filter(r => !r.success);

        return {
            success: failed.length === 0,
            results: results,
            successful: successful.length,
            failed: failed.length,
            data: successful.map(r => r.data)
        };
    }

    // Delete image
    async deleteImage(path) {
        try {
            if (!this.isInitialized) {
                throw new Error('Storage manager not initialized');
            }

            console.log('🗑️ Deleting image:', path);

            const { data, error } = await this.client.storage
                .from(this.bucketName)
                .remove([path]);

            if (error) {
                throw error;
            }

            console.log('✅ Image deleted successfully');

            return {
                success: true,
                data: data
            };

        } catch (error) {
            console.error('❌ Delete error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Get image URL
    getImageUrl(path) {
        if (!this.isInitialized || !path) {
            return null;
        }

        const { data } = this.client.storage
            .from(this.bucketName)
            .getPublicUrl(path);

        return data.publicUrl;
    }

    // List images in a folder
    async listImages(folder = '') {
        try {
            if (!this.isInitialized) {
                throw new Error('Storage manager not initialized');
            }

            const { data, error } = await this.client.storage
                .from(this.bucketName)
                .list(folder, {
                    limit: 100,
                    sortBy: { column: 'created_at', order: 'desc' }
                });

            if (error) {
                throw error;
            }

            // Add public URLs to each file
            const filesWithUrls = data.map(file => ({
                ...file,
                url: this.getImageUrl(folder ? `${folder}/${file.name}` : file.name),
                path: folder ? `${folder}/${file.name}` : file.name
            }));

            return {
                success: true,
                data: filesWithUrls
            };

        } catch (error) {
            console.error('❌ List images error:', error);
            return {
                success: false,
                error: error.message,
                data: []
            };
        }
    }

    // Validate file before upload
    validateFile(file) {
        // Check file size
        if (file.size > this.maxFileSize) {
            return {
                valid: false,
                error: `حجم الملف كبير جداً. الحد الأقصى ${this.maxFileSize / 1024 / 1024}MB`
            };
        }

        // Check file type
        if (!this.allowedTypes.includes(file.type)) {
            return {
                valid: false,
                error: 'نوع الملف غير مدعوم. يُسمح فقط بـ: ' + this.allowedTypes.join(', ')
            };
        }

        return { valid: true };
    }

    // Compress image before upload
    async compressImage(file, quality = 0.8) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // Calculate new dimensions (max 1200px width)
                const maxWidth = 1200;
                const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
                
                canvas.width = img.width * ratio;
                canvas.height = img.height * ratio;

                // Draw and compress
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                canvas.toBlob((blob) => {
                    // Create new file with compressed data
                    const compressedFile = new File([blob], file.name, {
                        type: file.type,
                        lastModified: Date.now()
                    });
                    resolve(compressedFile);
                }, file.type, quality);
            };

            img.src = URL.createObjectURL(file);
        });
    }

    // Get storage usage statistics
    async getStorageStats() {
        try {
            const listResult = await this.listImages();
            if (!listResult.success) {
                throw new Error(listResult.error);
            }

            const totalFiles = listResult.data.length;
            const totalSize = listResult.data.reduce((sum, file) => sum + (file.metadata?.size || 0), 0);

            return {
                success: true,
                data: {
                    totalFiles,
                    totalSize,
                    totalSizeMB: (totalSize / 1024 / 1024).toFixed(2),
                    bucketName: this.bucketName
                }
            };

        } catch (error) {
            console.error('❌ Storage stats error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Initialize storage manager
const supabaseStorageManager = new SupabaseStorageManager();

// Make available globally
window.supabaseStorageManager = supabaseStorageManager;

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SupabaseStorageManager;
}

console.log('✅ Supabase Storage Manager loaded');
