{"hosting": {"public": ".", "ignore": ["firebase.json", "**/.*", "**/node_modules/**", "package*.json", "README.md", "DEPLOYMENT-GUIDE.md", "scripts/**", "reports/**", "database-schema.sql", ".env*", "*.log"], "rewrites": [{"source": "/admin/**", "destination": "/admin/login.html"}, {"source": "/api/**", "function": "api"}], "redirects": [{"source": "/dashboard", "destination": "/admin/dashboard.html", "type": 301}, {"source": "/menu", "destination": "/index.html#menu", "type": 301}], "headers": [{"source": "**/*.@(jpg|jpeg|gif|png|webp|svg|ico)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000, immutable"}, {"key": "Vary", "value": "Accept-Encoding"}]}, {"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=86400"}, {"key": "Vary", "value": "Accept-Encoding"}]}, {"source": "**/*.@(html|json)", "headers": [{"key": "Cache-Control", "value": "max-age=3600"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "geolocation=(), microphone=(), camera=()"}]}, {"source": "/admin/**", "headers": [{"key": "X-Robots-Tag", "value": "noindex, nofollow"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}], "cleanUrls": true, "trailingSlash": false}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "functions": [{"source": "functions", "codebase": "default", "runtime": "nodejs18", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}], "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "storage": {"port": 9199}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}, "database": {"rules": "database.rules.json"}}