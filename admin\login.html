<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة إدارة محمد الاشرافي</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="admin-body">
    <div class="admin-login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-user-shield"></i>
                    <h1>محمد الاشرافي</h1>
                    <p>لوحة الإدارة</p>
                </div>
            </div>
            
            <div class="login-form-container">
                <form id="loginForm" class="login-form">
                    <div class="form-group">
                        <label for="username">
                            <i class="fas fa-user"></i>
                            اسم المستخدم
                        </label>
                        <input type="text" id="username" name="username" required autocomplete="username">
                    </div>
                    
                    <div class="form-group">
                        <label for="password">
                            <i class="fas fa-lock"></i>
                            كلمة المرور
                        </label>
                        <div class="password-input">
                            <input type="password" id="password" name="password" required autocomplete="current-password">
                            <button type="button" class="toggle-password" onclick="togglePassword()">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="rememberMe" name="rememberMe">
                            <span class="checkmark"></span>
                            تذكرني
                        </label>
                    </div>
                    
                    <button type="submit" class="login-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </button>
                </form>
                
                <div class="login-footer">
                    <a href="../index.html" class="back-to-site">
                        <i class="fas fa-arrow-right"></i>
                        العودة للموقع
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Error Message -->
        <div id="errorMessage" class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="errorText"></span>
        </div>
        
        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>جاري التحقق...</p>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.toggle-password i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleBtn.className = 'fas fa-eye';
            }
        }

        // Handle form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            if (!username || !password) {
                showError('يرجى ملء جميع الحقول');
                return;
            }
            
            // Show loading
            showLoading(true);
            
            // Simulate authentication (replace with real authentication)
            setTimeout(() => {
                if (authenticateUser(username, password)) {
                    // Store session
                    const sessionData = {
                        username: username,
                        loginTime: new Date().toISOString(),
                        rememberMe: rememberMe
                    };
                    
                    if (rememberMe) {
                        localStorage.setItem('admin_session', JSON.stringify(sessionData));
                    } else {
                        sessionStorage.setItem('admin_session', JSON.stringify(sessionData));
                    }
                    
                    // Redirect to dashboard
                    window.location.href = 'dashboard.html';
                } else {
                    showLoading(false);
                    showError('اسم المستخدم أو كلمة المرور غير صحيحة');
                }
            }, 1500);
        });

        // Simple authentication function (replace with real authentication)
        function authenticateUser(username, password) {
            // Default credentials (change these in production)
            const validCredentials = [
                { username: 'admin', password: 'admin123' },
                { username: 'محمد', password: 'alashrafi2024' },
                { username: 'manager', password: 'restaurant123' }
            ];
            
            return validCredentials.some(cred => 
                cred.username === username && cred.password === password
            );
        }

        // Show error message
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            errorText.textContent = message;
            errorDiv.classList.add('show');
            
            setTimeout(() => {
                errorDiv.classList.remove('show');
            }, 5000);
        }

        // Show/hide loading overlay
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            if (show) {
                overlay.classList.add('show');
            } else {
                overlay.classList.remove('show');
            }
        }

        // Check if already logged in
        window.addEventListener('load', () => {
            const session = localStorage.getItem('admin_session') || sessionStorage.getItem('admin_session');
            if (session) {
                try {
                    const sessionData = JSON.parse(session);
                    // Check if session is still valid (less than 24 hours old)
                    const loginTime = new Date(sessionData.loginTime);
                    const now = new Date();
                    const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
                    
                    if (hoursDiff < 24) {
                        window.location.href = 'dashboard.html';
                    }
                } catch (e) {
                    // Invalid session data, clear it
                    localStorage.removeItem('admin_session');
                    sessionStorage.removeItem('admin_session');
                }
            }
        });
    </script>
</body>
</html>
