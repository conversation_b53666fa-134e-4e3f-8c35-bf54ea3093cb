// Authentication Management for Admin Panel

class AdminAuth {
    constructor() {
        this.sessionKey = 'admin_session';
        this.sessionDuration = 24 * 60 * 60 * 1000; // 24 hours
        this.isRedirecting = false;
        this.init();
    }

    init() {
        // Prevent infinite redirects
        if (this.isRedirecting) {
            return;
        }

        // Check if we're on login page and already authenticated
        if (window.location.pathname.includes('login.html')) {
            this.checkExistingSession();
        } else {
            // Check authentication for other admin pages
            this.requireAuth();
        }
    }

    // Check if user is already logged in
    checkExistingSession() {
        const session = this.getSession();
        if (session && this.isSessionValid(session)) {
            // Redirect to dashboard if already logged in
            this.isRedirecting = true;
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 100);
        }
    }

    // Require authentication for admin pages
    requireAuth() {
        const session = this.getSession();
        if (!session || !this.isSessionValid(session)) {
            // Redirect to login if not authenticated
            this.isRedirecting = true;
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 100);
            return false;
        }

        // Update UI with user info
        this.updateUserInfo(session);
        return true;
    }

    // Get session from storage
    getSession() {
        try {
            // Check both localStorage and sessionStorage
            let session = localStorage.getItem(this.sessionKey);
            if (!session) {
                session = sessionStorage.getItem(this.sessionKey);
            }
            return session ? JSON.parse(session) : null;
        } catch (error) {
            console.error('Error getting session:', error);
            return null;
        }
    }

    // Check if session is valid
    isSessionValid(session) {
        if (!session || !session.loginTime) {
            return false;
        }

        const loginTime = new Date(session.loginTime);
        const now = new Date();
        const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

        // Session expires after 24 hours
        return hoursDiff < 24;
    }

    // Update user info in UI
    updateUserInfo(session) {
        const usernameElement = document.getElementById('adminUsername');
        if (usernameElement && session.username) {
            usernameElement.textContent = session.username;
        }
    }

    // Login function
    async login(username, password, rememberMe = false) {
        try {
            // Validate credentials
            if (!this.validateCredentials(username, password)) {
                throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
            }

            // Create session data
            const sessionData = {
                username: username,
                loginTime: new Date().toISOString(),
                rememberMe: rememberMe,
                permissions: this.getUserPermissions(username)
            };

            // Store session
            const storage = rememberMe ? localStorage : sessionStorage;
            storage.setItem(this.sessionKey, JSON.stringify(sessionData));

            // Log the login
            this.logActivity('login', username);

            return { success: true, message: 'تم تسجيل الدخول بنجاح' };
        } catch (error) {
            return { success: false, message: error.message };
        }
    }

    // Validate user credentials
    validateCredentials(username, password) {
        // Default admin credentials (في الإنتاج، يجب استخدام قاعدة بيانات آمنة)
        const validCredentials = [
            { 
                username: 'admin', 
                password: 'admin123',
                role: 'super_admin'
            },
            { 
                username: 'محمد', 
                password: 'alashrafi2024',
                role: 'owner'
            },
            { 
                username: 'manager', 
                password: 'restaurant123',
                role: 'manager'
            },
            {
                username: 'staff',
                password: 'staff2024',
                role: 'staff'
            }
        ];

        return validCredentials.some(cred => 
            cred.username === username && cred.password === password
        );
    }

    // Get user permissions based on username
    getUserPermissions(username) {
        const permissions = {
            'admin': ['all'],
            'محمد': ['all'],
            'manager': ['menu', 'orders', 'analytics'],
            'staff': ['orders']
        };

        return permissions[username] || ['orders'];
    }

    // Logout function
    logout() {
        try {
            const session = this.getSession();
            if (session) {
                this.logActivity('logout', session.username);
            }

            // Clear session from both storages
            localStorage.removeItem(this.sessionKey);
            sessionStorage.removeItem(this.sessionKey);

            // Redirect to login
            window.location.href = 'login.html';
        } catch (error) {
            console.error('Error during logout:', error);
            // Force redirect even if there's an error
            window.location.href = 'login.html';
        }
    }

    // Check if user has specific permission
    hasPermission(permission) {
        const session = this.getSession();
        if (!session || !session.permissions) {
            return false;
        }

        return session.permissions.includes('all') || 
               session.permissions.includes(permission);
    }

    // Get current user info
    getCurrentUser() {
        const session = this.getSession();
        return session ? {
            username: session.username,
            loginTime: session.loginTime,
            permissions: session.permissions
        } : null;
    }

    // Log user activity
    logActivity(action, username) {
        try {
            const activities = JSON.parse(localStorage.getItem('admin_activities') || '[]');
            activities.push({
                action: action,
                username: username,
                timestamp: new Date().toISOString(),
                ip: 'localhost' // في الإنتاج، احصل على IP الحقيقي
            });

            // Keep only last 100 activities
            if (activities.length > 100) {
                activities.splice(0, activities.length - 100);
            }

            localStorage.setItem('admin_activities', JSON.stringify(activities));
        } catch (error) {
            console.error('Error logging activity:', error);
        }
    }

    // Get recent activities
    getRecentActivities(limit = 10) {
        try {
            const activities = JSON.parse(localStorage.getItem('admin_activities') || '[]');
            return activities.slice(-limit).reverse();
        } catch (error) {
            console.error('Error getting activities:', error);
            return [];
        }
    }

    // Change password (placeholder for future implementation)
    async changePassword(currentPassword, newPassword) {
        // This would typically involve server-side validation
        // For now, just return a placeholder response
        return { 
            success: false, 
            message: 'تغيير كلمة المرور غير متاح حالياً. يرجى الاتصال بالمطور.' 
        };
    }

    // Session refresh
    refreshSession() {
        const session = this.getSession();
        if (session && this.isSessionValid(session)) {
            // Update login time to extend session
            session.loginTime = new Date().toISOString();
            
            const storage = session.rememberMe ? localStorage : sessionStorage;
            storage.setItem(this.sessionKey, JSON.stringify(session));
            
            return true;
        }
        return false;
    }

    // Auto-refresh session every 30 minutes
    startSessionRefresh() {
        setInterval(() => {
            if (!this.refreshSession()) {
                // Session expired, logout
                this.logout();
            }
        }, 30 * 60 * 1000); // 30 minutes
    }
}

// Initialize authentication
const adminAuth = new AdminAuth();

// Global logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        adminAuth.logout();
    }
}

// Auto-refresh session for authenticated pages
if (!window.location.pathname.includes('login.html')) {
    adminAuth.startSessionRefresh();
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminAuth;
}
