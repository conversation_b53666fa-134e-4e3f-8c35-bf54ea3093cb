// Real Firebase Configuration
// تكوين Firebase الحقيقي

// ⚠️ يجب تحديث هذه المعلومات بالمعلومات الحقيقية من Firebase Console
const realFirebaseConfig = {
    // احصل على هذه المعلومات من:
    // https://console.firebase.google.com/project/al-ishrafi-accounting-2025/settings/general
    
    apiKey: "YOUR_REAL_API_KEY_HERE",
    authDomain: "al-ishrafi-accounting-2025.firebaseapp.com",
    projectId: "al-ishrafi-accounting-2025",
    storageBucket: "al-ishrafi-accounting-2025.appspot.com",
    messagingSenderId: "************",
    appId: "YOUR_REAL_APP_ID_HERE",
    measurementId: "YOUR_MEASUREMENT_ID_HERE"
};

// Instructions for getting real config:
console.log(`
🔥 للحصول على تكوين Firebase الحقيقي:

1. اذهب إلى: https://console.firebase.google.com/project/al-ishrafi-accounting-2025
2. انقر على أيقونة الترس ⚙️ → "Project settings"
3. انتقل إلى تبويب "General"
4. في قسم "Your apps"، انقر "Add app" → "Web app" 🌐
5. اسم التطبيق: "alashrafi-restaurant"
6. فعّل "Firebase Hosting" ✅
7. انقر "Register app"
8. انسخ التكوين واستبدل المعلومات في هذا الملف

📋 خطوات إضافية مطلوبة:

🔥 Firestore Database:
1. اذهب إلى "Firestore Database"
2. انقر "Create database"
3. اختر "Start in test mode"
4. اختر الموقع الجغرافي (مثل: europe-west3)

📦 Storage:
1. اذهب إلى "Storage"
2. انقر "Get started"
3. اختر "Start in test mode"
4. اختر نفس الموقع الجغرافي

🔐 Authentication:
1. اذهب إلى "Authentication"
2. انقر "Get started"
3. في تبويب "Sign-in method"
4. فعّل "Email/Password" ✅

⚙️ بعد الانتهاء:
1. استبدل المعلومات في ملف firebase-config.js
2. شغّل: firebase deploy --only firestore:rules,storage:rules
3. اختبر النظام
`);

// Export config
if (typeof module !== 'undefined' && module.exports) {
    module.exports = realFirebaseConfig;
}
