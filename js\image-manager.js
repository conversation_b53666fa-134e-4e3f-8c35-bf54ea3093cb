// Advanced Image Management System
// نظام إدارة الصور المتقدم

// Import Firebase functions (will be loaded globally)
// import { storage, storageConfig, utils as firebaseUtils } from '../firebase-config.js';
// import { ref, uploadBytes, getDownloadURL, deleteObject, listAll } from 'firebase/storage';

class ImageManager {
    constructor() {
        this.maxFileSize = storageConfig.maxFileSize;
        this.allowedTypes = storageConfig.allowedTypes;
        this.compressionQuality = storageConfig.compressionQuality;
        this.maxWidth = storageConfig.maxWidth;
        this.maxHeight = storageConfig.maxHeight;
        this.thumbnailSize = storageConfig.thumbnailSize;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.createImagePreviewContainer();
    }

    setupEventListeners() {
        // Handle drag and drop
        document.addEventListener('dragover', this.handleDragOver.bind(this));
        document.addEventListener('drop', this.handleDrop.bind(this));
        
        // Handle paste events for images
        document.addEventListener('paste', this.handlePaste.bind(this));
    }

    createImagePreviewContainer() {
        if (!document.getElementById('imagePreviewContainer')) {
            const container = document.createElement('div');
            container.id = 'imagePreviewContainer';
            container.className = 'image-preview-container';
            container.style.display = 'none';
            document.body.appendChild(container);
        }
    }

    // Validate image file
    validateImage(file) {
        const errors = [];

        if (!file) {
            errors.push('لم يتم اختيار ملف');
            return { valid: false, errors };
        }

        if (!this.allowedTypes.includes(file.type)) {
            errors.push('نوع الملف غير مدعوم. الأنواع المدعومة: JPEG, PNG, WebP, GIF');
        }

        if (file.size > this.maxFileSize) {
            const maxSizeMB = (this.maxFileSize / (1024 * 1024)).toFixed(1);
            errors.push(`حجم الملف كبير جداً. الحد الأقصى: ${maxSizeMB} ميجابايت`);
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    // Compress image
    async compressImage(file, quality = this.compressionQuality) {
        return new Promise((resolve, reject) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                try {
                    const { width, height } = img;
                    let newWidth = width;
                    let newHeight = height;

                    // Calculate new dimensions
                    if (width > this.maxWidth || height > this.maxHeight) {
                        const ratio = Math.min(this.maxWidth / width, this.maxHeight / height);
                        newWidth = Math.round(width * ratio);
                        newHeight = Math.round(height * ratio);
                    }

                    canvas.width = newWidth;
                    canvas.height = newHeight;

                    // Draw and compress
                    ctx.drawImage(img, 0, 0, newWidth, newHeight);
                    
                    canvas.toBlob((blob) => {
                        if (blob) {
                            // Create new file with compressed data
                            const compressedFile = new File([blob], file.name, {
                                type: file.type,
                                lastModified: Date.now()
                            });
                            resolve(compressedFile);
                        } else {
                            reject(new Error('فشل في ضغط الصورة'));
                        }
                    }, file.type, quality);
                } catch (error) {
                    reject(error);
                }
            };

            img.onerror = () => reject(new Error('فشل في تحميل الصورة'));
            img.src = URL.createObjectURL(file);
        });
    }

    // Generate thumbnail
    async generateThumbnail(file, size = this.thumbnailSize) {
        return new Promise((resolve, reject) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                try {
                    const { width, height } = img;
                    
                    // Calculate square thumbnail dimensions
                    const minDimension = Math.min(width, height);
                    const scale = size / minDimension;
                    
                    canvas.width = size;
                    canvas.height = size;

                    // Center crop
                    const sourceX = (width - minDimension) / 2;
                    const sourceY = (height - minDimension) / 2;

                    ctx.drawImage(
                        img,
                        sourceX, sourceY, minDimension, minDimension,
                        0, 0, size, size
                    );

                    canvas.toBlob((blob) => {
                        if (blob) {
                            const thumbnailFile = new File([blob], `thumb_${file.name}`, {
                                type: file.type,
                                lastModified: Date.now()
                            });
                            resolve(thumbnailFile);
                        } else {
                            reject(new Error('فشل في إنشاء الصورة المصغرة'));
                        }
                    }, file.type, 0.8);
                } catch (error) {
                    reject(error);
                }
            };

            img.onerror = () => reject(new Error('فشل في تحميل الصورة'));
            img.src = URL.createObjectURL(file);
        });
    }

    // Upload single image
    async uploadImage(file, path, options = {}) {
        try {
            // Validate image
            const validation = this.validateImage(file);
            if (!validation.valid) {
                throw new Error(validation.errors.join(', '));
            }

            // Show upload progress
            const progressCallback = options.onProgress || (() => {});
            
            // Compress image if needed
            let processedFile = file;
            if (options.compress !== false) {
                progressCallback(10, 'جاري ضغط الصورة...');
                processedFile = await this.compressImage(file, options.quality);
            }

            // Generate thumbnail if needed
            let thumbnailFile = null;
            if (options.generateThumbnail !== false) {
                progressCallback(30, 'جاري إنشاء الصورة المصغرة...');
                thumbnailFile = await this.generateThumbnail(processedFile);
            }

            // Upload main image
            progressCallback(50, 'جاري رفع الصورة...');
            const mainImageRef = ref(storage, `${path}/${Date.now()}_${processedFile.name}`);
            const mainSnapshot = await uploadBytes(mainImageRef, processedFile);
            const mainImageURL = await getDownloadURL(mainSnapshot.ref);

            // Upload thumbnail
            let thumbnailURL = null;
            if (thumbnailFile) {
                progressCallback(70, 'جاري رفع الصورة المصغرة...');
                const thumbPath = `${path}/thumbnails/${Date.now()}_thumb_${thumbnailFile.name}`;
                const thumbRef = ref(storage, thumbPath);
                const thumbSnapshot = await uploadBytes(thumbRef, thumbnailFile);
                thumbnailURL = await getDownloadURL(thumbSnapshot.ref);
            }

            progressCallback(100, 'تم الرفع بنجاح');

            return {
                success: true,
                data: {
                    imageUrl: mainImageURL,
                    thumbnailUrl: thumbnailURL,
                    fileName: processedFile.name,
                    fileSize: processedFile.size,
                    fileType: processedFile.type,
                    path: mainImageRef.fullPath
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Upload multiple images
    async uploadMultipleImages(files, path, options = {}) {
        const results = [];
        const totalFiles = files.length;

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const fileOptions = {
                ...options,
                onProgress: (progress, message) => {
                    const overallProgress = ((i / totalFiles) * 100) + (progress / totalFiles);
                    if (options.onProgress) {
                        options.onProgress(overallProgress, `${message} (${i + 1}/${totalFiles})`);
                    }
                }
            };

            const result = await this.uploadImage(file, path, fileOptions);
            results.push({
                file: file.name,
                ...result
            });
        }

        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;

        return {
            success: failureCount === 0,
            results,
            summary: {
                total: totalFiles,
                success: successCount,
                failed: failureCount
            }
        };
    }

    // Delete image
    async deleteImage(imagePath) {
        try {
            const imageRef = ref(storage, imagePath);
            await deleteObject(imageRef);
            
            // Try to delete thumbnail as well
            try {
                const thumbPath = imagePath.replace(/([^/]+)$/, 'thumbnails/thumb_$1');
                const thumbRef = ref(storage, thumbPath);
                await deleteObject(thumbRef);
            } catch (thumbError) {
                // Thumbnail might not exist, ignore error
                console.log('Thumbnail not found or already deleted');
            }

            return { success: true };
        } catch (error) {
            return { 
                success: false, 
                error: error.message 
            };
        }
    }

    // Create image gallery
    createImageGallery(images, containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`Container with ID ${containerId} not found`);
            return;
        }

        container.innerHTML = '';
        container.className = 'image-gallery';

        if (images.length === 0) {
            container.innerHTML = `
                <div class="no-images">
                    <i class="fas fa-images"></i>
                    <p>لا توجد صور</p>
                </div>
            `;
            return;
        }

        images.forEach((image, index) => {
            const imageItem = document.createElement('div');
            imageItem.className = 'gallery-item';
            imageItem.innerHTML = `
                <div class="image-wrapper">
                    <img src="${image.thumbnailUrl || image.imageUrl}" 
                         alt="${image.altText || ''}"
                         loading="lazy"
                         onclick="imageManager.openLightbox('${image.imageUrl}', ${index})">
                    <div class="image-overlay">
                        <button class="btn-view" onclick="imageManager.openLightbox('${image.imageUrl}', ${index})">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${options.allowDelete ? `
                            <button class="btn-delete" onclick="imageManager.confirmDelete('${image.id}', '${image.path}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                        ${options.allowEdit ? `
                            <button class="btn-edit" onclick="imageManager.editImage('${image.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                        ` : ''}
                    </div>
                    ${image.isPrimary ? '<div class="primary-badge">رئيسية</div>' : ''}
                </div>
                <div class="image-info">
                    <p class="image-name">${image.fileName || 'صورة'}</p>
                    <p class="image-size">${this.formatFileSize(image.fileSize)}</p>
                </div>
            `;
            container.appendChild(imageItem);
        });
    }

    // Open lightbox for image viewing
    openLightbox(imageUrl, index = 0) {
        const lightbox = document.createElement('div');
        lightbox.className = 'image-lightbox';
        lightbox.innerHTML = `
            <div class="lightbox-overlay" onclick="this.parentElement.remove()">
                <div class="lightbox-content" onclick="event.stopPropagation()">
                    <button class="lightbox-close" onclick="this.closest('.image-lightbox').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                    <img src="${imageUrl}" alt="صورة مكبرة">
                    <div class="lightbox-controls">
                        <button class="btn-download" onclick="imageManager.downloadImage('${imageUrl}')">
                            <i class="fas fa-download"></i>
                            تحميل
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(lightbox);
        
        // Close on escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                lightbox.remove();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
    }

    // Download image
    async downloadImage(imageUrl) {
        try {
            const response = await fetch(imageUrl);
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `image_${Date.now()}.jpg`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error downloading image:', error);
            this.showMessage('فشل في تحميل الصورة', 'error');
        }
    }

    // Confirm delete image
    confirmDelete(imageId, imagePath) {
        if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
            this.deleteImageById(imageId, imagePath);
        }
    }

    // Delete image by ID
    async deleteImageById(imageId, imagePath) {
        try {
            // Delete from storage
            const deleteResult = await this.deleteImage(imagePath);
            if (!deleteResult.success) {
                throw new Error(deleteResult.error);
            }

            // Delete from database (you'll need to implement this based on your API)
            // await ProductsAPI.deleteImage(imageId);

            this.showMessage('تم حذف الصورة بنجاح', 'success');
            
            // Refresh gallery
            this.refreshGallery();
        } catch (error) {
            console.error('Error deleting image:', error);
            this.showMessage('فشل في حذف الصورة', 'error');
        }
    }

    // Handle drag over
    handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'copy';
    }

    // Handle drop
    handleDrop(e) {
        e.preventDefault();
        const files = Array.from(e.dataTransfer.files).filter(file => 
            file.type.startsWith('image/')
        );
        
        if (files.length > 0) {
            this.handleImageFiles(files);
        }
    }

    // Handle paste
    handlePaste(e) {
        const items = Array.from(e.clipboardData.items);
        const imageItems = items.filter(item => item.type.startsWith('image/'));
        
        if (imageItems.length > 0) {
            const files = imageItems.map(item => item.getAsFile());
            this.handleImageFiles(files);
        }
    }

    // Handle image files (from drop or paste)
    handleImageFiles(files) {
        // This method should be overridden or customized based on context
        console.log('Image files received:', files);
        this.showMessage(`تم استلام ${files.length} صورة`, 'info');
    }

    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Show message
    showMessage(message, type = 'info') {
        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `image-message ${type}`;
        messageDiv.innerHTML = `
            <i class="fas fa-${this.getMessageIcon(type)}"></i>
            <span>${message}</span>
        `;
        
        // Add to page
        document.body.appendChild(messageDiv);
        
        // Position message
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '20px';
        messageDiv.style.right = '20px';
        messageDiv.style.zIndex = '10000';
        messageDiv.style.padding = '15px 20px';
        messageDiv.style.borderRadius = '8px';
        messageDiv.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        
        // Style based on type
        const styles = {
            success: { background: '#d4edda', color: '#155724', border: '1px solid #c3e6cb' },
            error: { background: '#f8d7da', color: '#721c24', border: '1px solid #f5c6cb' },
            warning: { background: '#fff3cd', color: '#856404', border: '1px solid #ffeaa7' },
            info: { background: '#d1ecf1', color: '#0c5460', border: '1px solid #bee5eb' }
        };
        
        Object.assign(messageDiv.style, styles[type] || styles.info);
        
        // Remove after 4 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 4000);
    }

    getMessageIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // Refresh gallery (to be implemented based on context)
    refreshGallery() {
        // This should be overridden to refresh the current gallery
        console.log('Gallery refresh requested');
    }
}

// Create global instance
const imageManager = new ImageManager();

// Export for use in other modules
export default imageManager;
export { ImageManager };
