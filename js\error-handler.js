// Advanced Error Handling and Security System
// نظام إدارة الأخطاء والأمان المتقدم

class ErrorHandler {
    constructor() {
        this.errors = [];
        this.maxErrors = 100;
        this.isProduction = window.location.hostname !== 'localhost';
        this.init();
    }

    init() {
        this.setupGlobalErrorHandling();
        this.setupUnhandledRejectionHandling();
        this.setupSecurityHeaders();
        this.startErrorReporting();
    }

    // Setup global error handling
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'javascript_error',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href
            });
        });
    }

    // Setup unhandled promise rejection handling
    setupUnhandledRejectionHandling() {
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'unhandled_promise_rejection',
                message: event.reason?.message || 'Unhandled Promise Rejection',
                stack: event.reason?.stack,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href
            });
        });
    }

    // Setup basic security headers check
    setupSecurityHeaders() {
        // Check for HTTPS in production
        if (this.isProduction && window.location.protocol !== 'https:') {
            this.logWarning('Site is not using HTTPS in production');
        }

        // Check for mixed content
        if (window.location.protocol === 'https:') {
            this.checkMixedContent();
        }
    }

    // Check for mixed content issues
    checkMixedContent() {
        const images = document.querySelectorAll('img[src^="http:"]');
        const scripts = document.querySelectorAll('script[src^="http:"]');
        const links = document.querySelectorAll('link[href^="http:"]');

        if (images.length > 0 || scripts.length > 0 || links.length > 0) {
            this.logWarning('Mixed content detected - some resources are loaded over HTTP');
        }
    }

    // Handle different types of errors
    handleError(errorInfo) {
        // Add to errors array
        this.errors.push(errorInfo);
        
        // Keep only recent errors
        if (this.errors.length > this.maxErrors) {
            this.errors = this.errors.slice(-this.maxErrors);
        }

        // Log error based on environment
        if (this.isProduction) {
            this.logErrorToService(errorInfo);
        } else {
            console.error('Error caught:', errorInfo);
        }

        // Show user-friendly message for critical errors
        if (this.isCriticalError(errorInfo)) {
            this.showUserErrorMessage(errorInfo);
        }

        // Store error locally
        this.storeErrorLocally(errorInfo);
    }

    // Check if error is critical
    isCriticalError(errorInfo) {
        const criticalPatterns = [
            /network/i,
            /fetch/i,
            /database/i,
            /payment/i,
            /order/i,
            /authentication/i
        ];

        return criticalPatterns.some(pattern => 
            pattern.test(errorInfo.message) || 
            pattern.test(errorInfo.type)
        );
    }

    // Show user-friendly error message
    showUserErrorMessage(errorInfo) {
        const userMessage = this.getUserFriendlyMessage(errorInfo);
        
        // Create error notification
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification';
        errorDiv.innerHTML = `
            <div class="error-content">
                <i class="fas fa-exclamation-triangle"></i>
                <div class="error-text">
                    <h4>حدث خطأ</h4>
                    <p>${userMessage}</p>
                </div>
                <button class="error-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Style the error notification
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 0;
            max-width: 400px;
            z-index: 10000;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            animation: slideInRight 0.3s ease;
        `;

        const errorContent = errorDiv.querySelector('.error-content');
        errorContent.style.cssText = `
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 20px;
        `;

        const errorText = errorDiv.querySelector('.error-text');
        errorText.style.cssText = `
            flex: 1;
        `;

        const errorClose = errorDiv.querySelector('.error-close');
        errorClose.style.cssText = `
            background: none;
            border: none;
            color: #721c24;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: background 0.3s ease;
        `;

        document.body.appendChild(errorDiv);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 10000);
    }

    // Get user-friendly error message
    getUserFriendlyMessage(errorInfo) {
        const messageMap = {
            'network': 'مشكلة في الاتصال بالإنترنت. يرجى التحقق من الاتصال والمحاولة مرة أخرى.',
            'fetch': 'فشل في تحميل البيانات. يرجى إعادة تحميل الصفحة.',
            'database': 'مشكلة في قاعدة البيانات. يرجى المحاولة لاحقاً.',
            'payment': 'مشكلة في معالجة الدفع. يرجى المحاولة مرة أخرى.',
            'order': 'مشكلة في معالجة الطلب. يرجى المحاولة مرة أخرى.',
            'authentication': 'مشكلة في تسجيل الدخول. يرجى تسجيل الدخول مرة أخرى.'
        };

        for (const [key, message] of Object.entries(messageMap)) {
            if (errorInfo.message.toLowerCase().includes(key) || 
                errorInfo.type.toLowerCase().includes(key)) {
                return message;
            }
        }

        return 'حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة أو المحاولة لاحقاً.';
    }

    // Log error to external service (placeholder)
    logErrorToService(errorInfo) {
        // In production, you would send this to an error tracking service
        // like Sentry, LogRocket, or your own logging endpoint
        
        try {
            // Example: Send to your logging endpoint
            // fetch('/api/log-error', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify(errorInfo)
            // });
            
            console.log('Error logged to service:', errorInfo);
        } catch (loggingError) {
            console.error('Failed to log error to service:', loggingError);
        }
    }

    // Store error locally for debugging
    storeErrorLocally(errorInfo) {
        try {
            const storedErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
            storedErrors.push(errorInfo);
            
            // Keep only last 50 errors
            if (storedErrors.length > 50) {
                storedErrors.splice(0, storedErrors.length - 50);
            }
            
            localStorage.setItem('app_errors', JSON.stringify(storedErrors));
        } catch (storageError) {
            console.error('Failed to store error locally:', storageError);
        }
    }

    // Log warning
    logWarning(message, details = {}) {
        const warning = {
            type: 'warning',
            message: message,
            details: details,
            timestamp: new Date().toISOString(),
            url: window.location.href
        };

        this.handleError(warning);
    }

    // Log info
    logInfo(message, details = {}) {
        const info = {
            type: 'info',
            message: message,
            details: details,
            timestamp: new Date().toISOString(),
            url: window.location.href
        };

        if (!this.isProduction) {
            console.log('Info:', info);
        }
    }

    // Start periodic error reporting
    startErrorReporting() {
        // Report errors every 5 minutes in production
        if (this.isProduction) {
            setInterval(() => {
                this.reportStoredErrors();
            }, 5 * 60 * 1000);
        }
    }

    // Report stored errors
    reportStoredErrors() {
        try {
            const storedErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
            
            if (storedErrors.length > 0) {
                // Send batch of errors to service
                this.sendErrorBatch(storedErrors);
                
                // Clear stored errors after sending
                localStorage.removeItem('app_errors');
            }
        } catch (error) {
            console.error('Failed to report stored errors:', error);
        }
    }

    // Send error batch to service
    sendErrorBatch(errors) {
        // Placeholder for batch error reporting
        console.log('Sending error batch:', errors);
    }

    // Get error statistics
    getErrorStats() {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        const recentErrors = this.errors.filter(error => 
            new Date(error.timestamp) > oneHourAgo
        );

        const dailyErrors = this.errors.filter(error => 
            new Date(error.timestamp) > oneDayAgo
        );

        return {
            total: this.errors.length,
            lastHour: recentErrors.length,
            lastDay: dailyErrors.length,
            criticalErrors: this.errors.filter(error => this.isCriticalError(error)).length,
            errorTypes: this.getErrorTypeStats()
        };
    }

    // Get error type statistics
    getErrorTypeStats() {
        const typeStats = {};
        
        this.errors.forEach(error => {
            typeStats[error.type] = (typeStats[error.type] || 0) + 1;
        });

        return typeStats;
    }

    // Clear all errors
    clearErrors() {
        this.errors = [];
        localStorage.removeItem('app_errors');
    }

    // Export errors for debugging
    exportErrors() {
        const errorData = {
            errors: this.errors,
            stats: this.getErrorStats(),
            exportTime: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        const blob = new Blob([JSON.stringify(errorData, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `errors_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// Security utilities
class SecurityManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupCSRFProtection();
        this.setupXSSProtection();
        this.setupInputSanitization();
    }

    // Basic CSRF protection
    setupCSRFProtection() {
        // Generate CSRF token
        if (!sessionStorage.getItem('csrf_token')) {
            const token = this.generateToken();
            sessionStorage.setItem('csrf_token', token);
        }
    }

    // XSS protection
    setupXSSProtection() {
        // Basic XSS protection without overriding innerHTML
        // This prevents the "Illegal invocation" error
        console.log('XSS protection enabled');

        // Add CSP meta tag if not present
        if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
            const cspMeta = document.createElement('meta');
            cspMeta.setAttribute('http-equiv', 'Content-Security-Policy');
            cspMeta.setAttribute('content', "default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data:;");
            document.head.appendChild(cspMeta);
        }
    }

    // Setup input sanitization
    setupInputSanitization() {
        document.addEventListener('input', (e) => {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                e.target.value = this.sanitizeInput(e.target.value);
            }
        });
    }

    // Generate secure token
    generateToken() {
        const array = new Uint8Array(32);
        crypto.getRandomValues(array);
        return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }

    // Sanitize HTML content
    sanitizeHTML(html) {
        const div = document.createElement('div');
        div.textContent = html;
        return div.innerHTML;
    }

    // Sanitize user input
    sanitizeInput(input) {
        if (typeof input !== 'string') return input;
        
        return input
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '')
            .trim();
    }

    // Validate input
    validateInput(input, type) {
        const validators = {
            email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            phone: /^(\+20|0)?1[0125]\d{8}$/,
            name: /^[\u0600-\u06FFa-zA-Z\s]{2,50}$/,
            address: /^[\u0600-\u06FFa-zA-Z0-9\s,.-]{5,200}$/
        };

        return validators[type] ? validators[type].test(input) : true;
    }

    // Get CSRF token
    getCSRFToken() {
        return sessionStorage.getItem('csrf_token');
    }
}

// Initialize error handler and security manager
const errorHandler = new ErrorHandler();
const securityManager = new SecurityManager();

// Global error handling functions
window.handleError = (error, context = '') => {
    errorHandler.handleError({
        type: 'manual_error',
        message: error.message || error,
        context: context,
        stack: error.stack,
        timestamp: new Date().toISOString()
    });
};

window.logWarning = (message, details) => {
    errorHandler.logWarning(message, details);
};

window.logInfo = (message, details) => {
    errorHandler.logInfo(message, details);
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ErrorHandler, SecurityManager };
}

// Make available globally
window.errorHandler = errorHandler;
window.securityManager = securityManager;
