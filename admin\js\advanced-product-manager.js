// Advanced Product Management System
// نظام إدارة المنتجات المتقدم

class AdvancedProductManager {
    constructor() {
        this.products = [];
        this.categories = [];
        this.currentProduct = null;
        this.supabase = null;
        this.storageManager = null;
        this.isInitialized = false;
        this.filters = {
            category: 'all',
            availability: 'all',
            featured: 'all',
            search: ''
        };
        this.sortBy = 'created_at';
        this.sortOrder = 'desc';
        this.currentPage = 1;
        this.itemsPerPage = 12;
        
        console.log('🚀 AdvancedProductManager initializing...');
        this.init();
    }

    async init() {
        try {
            // Initialize dependencies
            await this.initializeDependencies();
            
            // Load data
            await this.loadCategories();
            await this.loadProducts();
            
            // Setup UI
            this.setupEventListeners();
            this.setupDragAndDrop();
            this.setupKeyboardShortcuts();
            
            // Render interface
            this.renderInterface();
            
            // Setup real-time sync
            this.setupRealTimeSync();
            
            this.isInitialized = true;
            console.log('✅ AdvancedProductManager initialized successfully');
            
        } catch (error) {
            console.error('❌ Error initializing AdvancedProductManager:', error);
            this.showNotification('خطأ في تهيئة النظام', 'error');
        }
    }

    async initializeDependencies() {
        // Wait for Supabase
        await this.waitForDependency('supabaseManager', 'Supabase');
        this.supabase = window.supabaseManager;
        
        // Wait for Storage Manager
        await this.waitForDependency('supabaseStorageManager', 'Storage Manager');
        this.storageManager = window.supabaseStorageManager;
    }

    async waitForDependency(windowProperty, name) {
        return new Promise((resolve) => {
            const check = () => {
                if (window[windowProperty] && window[windowProperty].isInitialized) {
                    console.log(`✅ ${name} ready`);
                    resolve();
                } else {
                    setTimeout(check, 100);
                }
            };
            check();
        });
    }

    async loadCategories() {
        try {
            // Try loading from Supabase first
            if (this.supabase && this.supabase.isInitialized) {
                const result = await this.supabase.getCategories();
                if (result.success && result.data) {
                    this.categories = result.data;
                    console.log(`✅ Loaded ${this.categories.length} categories from Supabase`);
                    return;
                }
            }
            
            // Fallback categories
            this.categories = [
                { 
                    id: '1', 
                    name: 'المقبلات', 
                    name_en: 'Appetizers',
                    description: 'المقبلات والسلطات',
                    icon: 'fas fa-seedling',
                    color: '#27ae60',
                    is_active: true,
                    sort_order: 1
                },
                { 
                    id: '2', 
                    name: 'الأطباق الرئيسية', 
                    name_en: 'Main Dishes',
                    description: 'الأطباق الرئيسية واللحوم',
                    icon: 'fas fa-drumstick-bite',
                    color: '#e74c3c',
                    is_active: true,
                    sort_order: 2
                },
                { 
                    id: '3', 
                    name: 'المشروبات', 
                    name_en: 'Beverages',
                    description: 'المشروبات الساخنة والباردة',
                    icon: 'fas fa-coffee',
                    color: '#3498db',
                    is_active: true,
                    sort_order: 3
                },
                { 
                    id: '4', 
                    name: 'الحلويات', 
                    name_en: 'Desserts',
                    description: 'الحلويات والمعجنات',
                    icon: 'fas fa-birthday-cake',
                    color: '#f39c12',
                    is_active: true,
                    sort_order: 4
                }
            ];
            
            console.log('📦 Using default categories');
            
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    async loadProducts() {
        try {
            // Try Supabase first
            if (this.supabase && this.supabase.isInitialized) {
                const result = await this.supabase.getProducts();
                if (result.success && result.data) {
                    this.products = result.data.map(product => this.normalizeProduct(product));
                    this.saveToLocalStorage();
                    console.log(`✅ Loaded ${this.products.length} products from Supabase`);
                    return;
                }
            }
            
            // Fallback to localStorage
            const localProducts = localStorage.getItem('restaurant_products');
            if (localProducts && localProducts !== 'undefined') {
                this.products = JSON.parse(localProducts).map(product => this.normalizeProduct(product));
                console.log(`📦 Loaded ${this.products.length} products from localStorage`);
                return;
            }
            
            // Default products
            this.products = this.getDefaultProducts();
            this.saveToLocalStorage();
            console.log('📦 Using default products');
            
        } catch (error) {
            console.error('Error loading products:', error);
            this.products = this.getDefaultProducts();
        }
    }

    normalizeProduct(product) {
        return {
            id: product.id || this.generateId(),
            name: product.name || '',
            name_en: product.name_en || product.name || '',
            description: product.description || '',
            description_en: product.description_en || product.description || '',
            price: parseFloat(product.price) || 0,
            original_price: parseFloat(product.original_price) || parseFloat(product.price) || 0,
            discount_percentage: parseFloat(product.discount_percentage) || 0,
            category_id: product.category_id || '1',
            category: product.category || this.getCategoryName(product.category_id),
            images: Array.isArray(product.images) ? product.images : [],
            image_url: product.image_url || this.getPrimaryImageUrl(product.images),
            icon: product.icon || 'fas fa-utensils',
            is_available: product.is_available !== false,
            is_featured: product.is_featured || false,
            is_new: product.is_new || false,
            is_popular: product.is_popular || false,
            is_spicy: product.is_spicy || false,
            is_vegetarian: product.is_vegetarian || false,
            is_vegan: product.is_vegan || false,
            allergens: Array.isArray(product.allergens) ? product.allergens : [],
            nutrition_info: product.nutrition_info || {},
            preparation_time: parseInt(product.preparation_time) || 0,
            serving_size: product.serving_size || '',
            ingredients: Array.isArray(product.ingredients) ? product.ingredients : [],
            tags: Array.isArray(product.tags) ? product.tags : [],
            sort_order: parseInt(product.sort_order) || 0,
            view_count: parseInt(product.view_count) || 0,
            order_count: parseInt(product.order_count) || 0,
            rating: parseFloat(product.rating) || 0,
            rating_count: parseInt(product.rating_count) || 0,
            seo_title: product.seo_title || product.name,
            seo_description: product.seo_description || product.description,
            seo_keywords: product.seo_keywords || '',
            created_at: product.created_at || new Date().toISOString(),
            updated_at: product.updated_at || new Date().toISOString(),
            created_by: product.created_by || 'admin',
            updated_by: product.updated_by || 'admin'
        };
    }

    getPrimaryImageUrl(images) {
        if (!Array.isArray(images) || images.length === 0) return null;
        const primaryImage = images.find(img => img.isPrimary) || images[0];
        return primaryImage.url || primaryImage.path || null;
    }

    getCategoryName(categoryId) {
        const category = this.categories.find(cat => cat.id === categoryId);
        return category ? category.name : 'عام';
    }

    getCategoryColor(categoryId) {
        const category = this.categories.find(cat => cat.id === categoryId);
        return category ? category.color : '#95a5a6';
    }

    generateId() {
        return 'prod_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    getDefaultProducts() {
        return [
            {
                id: 'p1',
                name: 'حمص بالطحينة',
                description: 'حمص طازج مع الطحينة والزيت والبقدونس والبابريكا',
                price: 25,
                category_id: '1',
                icon: 'fas fa-seedling',
                is_available: true,
                is_featured: false,
                is_vegetarian: true,
                is_vegan: true,
                preparation_time: 5,
                serving_size: 'طبق متوسط',
                ingredients: ['حمص', 'طحينة', 'زيت زيتون', 'بقدونس', 'بابريكا'],
                tags: ['نباتي', 'صحي', 'مقبلات'],
                nutrition_info: {
                    calories: 180,
                    protein: 8,
                    carbs: 20,
                    fat: 9
                }
            },
            {
                id: 'p2',
                name: 'كباب مشوي',
                description: 'كباب لحم مشوي طازج مع الخضار والأرز البسمتي',
                price: 85,
                original_price: 95,
                discount_percentage: 10,
                category_id: '2',
                icon: 'fas fa-drumstick-bite',
                is_available: true,
                is_featured: true,
                is_popular: true,
                preparation_time: 20,
                serving_size: 'طبق كبير',
                ingredients: ['لحم مفروم', 'بصل', 'بقدونس', 'توابل', 'أرز بسمتي'],
                tags: ['لحوم', 'مشوي', 'شعبي'],
                nutrition_info: {
                    calories: 450,
                    protein: 35,
                    carbs: 25,
                    fat: 22
                }
            },
            {
                id: 'p3',
                name: 'شاي أحمر',
                description: 'شاي أحمر طازج مع النعناع والسكر',
                price: 15,
                category_id: '3',
                icon: 'fas fa-coffee',
                is_available: true,
                is_featured: false,
                is_vegetarian: true,
                is_vegan: true,
                preparation_time: 3,
                serving_size: 'كوب',
                ingredients: ['شاي أحمر', 'نعناع', 'سكر'],
                tags: ['مشروبات', 'ساخن', 'تقليدي'],
                nutrition_info: {
                    calories: 25,
                    protein: 0,
                    carbs: 6,
                    fat: 0
                }
            }
        ].map(product => this.normalizeProduct(product));
    }

    saveToLocalStorage() {
        try {
            localStorage.setItem('restaurant_products', JSON.stringify(this.products));
            localStorage.setItem('restaurant_categories', JSON.stringify(this.categories));
        } catch (error) {
            console.error('Error saving to localStorage:', error);
        }
    }

    setupEventListeners() {
        // Search and filters
        this.setupSearchAndFilters();
        
        // Product actions
        this.setupProductActions();
        
        // Modal events
        this.setupModalEvents();
        
        // Bulk actions
        this.setupBulkActions();
        
        // Import/Export
        this.setupImportExport();
    }

    setupSearchAndFilters() {
        // Search input
        const searchInput = document.getElementById('productSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value;
                this.applyFilters();
            });
        }

        // Category filter
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filters.category = e.target.value;
                this.applyFilters();
            });
        }

        // Availability filter
        const availabilityFilter = document.getElementById('availabilityFilter');
        if (availabilityFilter) {
            availabilityFilter.addEventListener('change', (e) => {
                this.filters.availability = e.target.value;
                this.applyFilters();
            });
        }

        // Featured filter
        const featuredFilter = document.getElementById('featuredFilter');
        if (featuredFilter) {
            featuredFilter.addEventListener('change', (e) => {
                this.filters.featured = e.target.value;
                this.applyFilters();
            });
        }

        // Sort options
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                const [sortBy, sortOrder] = e.target.value.split('_');
                this.sortBy = sortBy;
                this.sortOrder = sortOrder;
                this.applyFilters();
            });
        }
    }

    setupProductActions() {
        document.addEventListener('click', (e) => {
            const target = e.target.closest('[data-action]');
            if (!target) return;

            const action = target.dataset.action;
            const productId = target.dataset.productId;

            switch (action) {
                case 'add-product':
                    this.openProductModal();
                    break;
                case 'edit-product':
                    this.editProduct(productId);
                    break;
                case 'duplicate-product':
                    this.duplicateProduct(productId);
                    break;
                case 'delete-product':
                    this.deleteProduct(productId);
                    break;
                case 'toggle-availability':
                    this.toggleProductAvailability(productId);
                    break;
                case 'toggle-featured':
                    this.toggleProductFeatured(productId);
                    break;
                case 'view-analytics':
                    this.viewProductAnalytics(productId);
                    break;
                case 'manage-images':
                    this.manageProductImages(productId);
                    break;
            }
        });
    }

    setupModalEvents() {
        // Product form submission
        const productForm = document.getElementById('productForm');
        if (productForm) {
            productForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleProductSubmit(e.target);
            });
        }

        // Modal close events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-close') || 
                e.target.classList.contains('modal-overlay')) {
                this.closeAllModals();
            }
        });

        // Escape key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    setupBulkActions() {
        // Select all checkbox
        const selectAllCheckbox = document.getElementById('selectAllProducts');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.selectAllProducts(e.target.checked);
            });
        }

        // Bulk action buttons
        document.addEventListener('click', (e) => {
            const target = e.target.closest('[data-bulk-action]');
            if (!target) return;

            const action = target.dataset.bulkAction;
            const selectedProducts = this.getSelectedProducts();

            if (selectedProducts.length === 0) {
                this.showNotification('يرجى اختيار منتجات أولاً', 'warning');
                return;
            }

            switch (action) {
                case 'bulk-delete':
                    this.bulkDeleteProducts(selectedProducts);
                    break;
                case 'bulk-enable':
                    this.bulkToggleAvailability(selectedProducts, true);
                    break;
                case 'bulk-disable':
                    this.bulkToggleAvailability(selectedProducts, false);
                    break;
                case 'bulk-feature':
                    this.bulkToggleFeatured(selectedProducts, true);
                    break;
                case 'bulk-unfeature':
                    this.bulkToggleFeatured(selectedProducts, false);
                    break;
                case 'bulk-export':
                    this.bulkExportProducts(selectedProducts);
                    break;
            }
        });
    }

    setupImportExport() {
        // Import products
        const importInput = document.getElementById('importProducts');
        if (importInput) {
            importInput.addEventListener('change', (e) => {
                this.importProducts(e.target.files[0]);
            });
        }

        // Export all products
        const exportBtn = document.getElementById('exportAllProducts');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportAllProducts();
            });
        }
    }

    setupDragAndDrop() {
        // Setup sortable for product reordering
        const productsContainer = document.getElementById('productsGrid');
        if (productsContainer && typeof Sortable !== 'undefined') {
            new Sortable(productsContainer, {
                animation: 150,
                ghostClass: 'sortable-ghost',
                onEnd: (evt) => {
                    this.reorderProducts(evt.oldIndex, evt.newIndex);
                }
            });
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + N: New product
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                this.openProductModal();
            }
            
            // Ctrl/Cmd + S: Save (if modal is open)
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                const modal = document.querySelector('.modal[style*="block"]');
                if (modal) {
                    e.preventDefault();
                    const form = modal.querySelector('form');
                    if (form) {
                        form.dispatchEvent(new Event('submit'));
                    }
                }
            }
            
            // Delete key: Delete selected products
            if (e.key === 'Delete') {
                const selectedProducts = this.getSelectedProducts();
                if (selectedProducts.length > 0) {
                    this.bulkDeleteProducts(selectedProducts);
                }
            }
        });
    }

    setupRealTimeSync() {
        // Listen for storage changes (cross-tab sync)
        window.addEventListener('storage', (e) => {
            if (e.key === 'restaurant_products') {
                console.log('🔄 Products updated in another tab');
                this.loadProducts().then(() => {
                    this.renderProducts();
                    this.showNotification('تم تحديث المنتجات من علامة تبويب أخرى', 'info');
                });
            }
        });

        // Listen for custom events
        window.addEventListener('productAdded', (e) => {
            console.log('➕ Product added:', e.detail);
            this.handleRealTimeProductAdd(e.detail);
        });

        window.addEventListener('productUpdated', (e) => {
            console.log('✏️ Product updated:', e.detail);
            this.handleRealTimeProductUpdate(e.detail);
        });

        window.addEventListener('productDeleted', (e) => {
            console.log('🗑️ Product deleted:', e.detail);
            this.handleRealTimeProductDelete(e.detail);
        });
    }

    // Continue with more methods...
    renderInterface() {
        this.renderFiltersAndControls();
        this.renderProducts();
        this.renderStatistics();
        this.updatePagination();
    }

    renderFiltersAndControls() {
        const filtersContainer = document.getElementById('filtersContainer');
        if (!filtersContainer) return;

        filtersContainer.innerHTML = `
            <div class="filters-row">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="productSearch" placeholder="البحث في المنتجات..." value="${this.filters.search}">
                </div>

                <select id="categoryFilter" class="filter-select">
                    <option value="all">جميع الفئات</option>
                    ${this.categories.map(cat =>
                        `<option value="${cat.id}" ${this.filters.category === cat.id ? 'selected' : ''}>${cat.name}</option>`
                    ).join('')}
                </select>

                <select id="availabilityFilter" class="filter-select">
                    <option value="all">جميع الحالات</option>
                    <option value="available" ${this.filters.availability === 'available' ? 'selected' : ''}>متوفر</option>
                    <option value="unavailable" ${this.filters.availability === 'unavailable' ? 'selected' : ''}>غير متوفر</option>
                </select>

                <select id="featuredFilter" class="filter-select">
                    <option value="all">الكل</option>
                    <option value="featured" ${this.filters.featured === 'featured' ? 'selected' : ''}>مميز</option>
                    <option value="not-featured" ${this.filters.featured === 'not-featured' ? 'selected' : ''}>عادي</option>
                </select>

                <select id="sortSelect" class="filter-select">
                    <option value="created_at_desc">الأحدث أولاً</option>
                    <option value="created_at_asc">الأقدم أولاً</option>
                    <option value="name_asc">الاسم (أ-ي)</option>
                    <option value="name_desc">الاسم (ي-أ)</option>
                    <option value="price_asc">السعر (منخفض-عالي)</option>
                    <option value="price_desc">السعر (عالي-منخفض)</option>
                    <option value="order_count_desc">الأكثر طلباً</option>
                </select>
            </div>

            <div class="actions-row">
                <div class="bulk-actions">
                    <input type="checkbox" id="selectAllProducts">
                    <label for="selectAllProducts">تحديد الكل</label>

                    <button data-bulk-action="bulk-delete" class="btn btn-danger btn-sm">
                        <i class="fas fa-trash"></i> حذف المحدد
                    </button>
                    <button data-bulk-action="bulk-enable" class="btn btn-success btn-sm">
                        <i class="fas fa-eye"></i> تفعيل
                    </button>
                    <button data-bulk-action="bulk-disable" class="btn btn-warning btn-sm">
                        <i class="fas fa-eye-slash"></i> إلغاء تفعيل
                    </button>
                    <button data-bulk-action="bulk-export" class="btn btn-info btn-sm">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>

                <div class="main-actions">
                    <button data-action="add-product" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة منتج جديد
                    </button>
                    <button id="exportAllProducts" class="btn btn-secondary">
                        <i class="fas fa-file-export"></i> تصدير الكل
                    </button>
                    <label for="importProducts" class="btn btn-secondary">
                        <i class="fas fa-file-import"></i> استيراد
                        <input type="file" id="importProducts" accept=".json,.csv" style="display: none;">
                    </label>
                </div>
            </div>
        `;
    }

    renderProducts() {
        const container = document.getElementById('productsGrid');
        if (!container) return;

        const filteredProducts = this.getFilteredProducts();
        const paginatedProducts = this.getPaginatedProducts(filteredProducts);

        if (paginatedProducts.length === 0) {
            container.innerHTML = this.renderEmptyState();
            return;
        }

        const productsHtml = paginatedProducts.map(product => this.renderProductCard(product)).join('');
        container.innerHTML = productsHtml;
    }

    renderProductCard(product) {
        const categoryColor = this.getCategoryColor(product.category_id);
        const imageHtml = this.renderProductImage(product);
        const badgesHtml = this.renderProductBadges(product);
        const priceHtml = this.renderProductPrice(product);

        return `
            <div class="product-card ${product.is_available ? 'available' : 'unavailable'}"
                 data-product-id="${product.id}">
                <div class="product-card-header">
                    <input type="checkbox" class="product-select" data-product-id="${product.id}">
                    <div class="product-actions-menu">
                        <button class="actions-toggle"><i class="fas fa-ellipsis-v"></i></button>
                        <div class="actions-dropdown">
                            <button data-action="edit-product" data-product-id="${product.id}">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button data-action="duplicate-product" data-product-id="${product.id}">
                                <i class="fas fa-copy"></i> نسخ
                            </button>
                            <button data-action="manage-images" data-product-id="${product.id}">
                                <i class="fas fa-images"></i> إدارة الصور
                            </button>
                            <button data-action="view-analytics" data-product-id="${product.id}">
                                <i class="fas fa-chart-line"></i> الإحصائيات
                            </button>
                            <hr>
                            <button data-action="toggle-availability" data-product-id="${product.id}"
                                    class="${product.is_available ? 'text-warning' : 'text-success'}">
                                <i class="fas ${product.is_available ? 'fa-eye-slash' : 'fa-eye'}"></i>
                                ${product.is_available ? 'إخفاء' : 'إظهار'}
                            </button>
                            <button data-action="toggle-featured" data-product-id="${product.id}"
                                    class="${product.is_featured ? 'text-warning' : 'text-primary'}">
                                <i class="fas fa-star"></i>
                                ${product.is_featured ? 'إلغاء التمييز' : 'تمييز'}
                            </button>
                            <hr>
                            <button data-action="delete-product" data-product-id="${product.id}" class="text-danger">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>

                <div class="product-image-container">
                    ${imageHtml}
                    ${badgesHtml}
                </div>

                <div class="product-info">
                    <div class="product-category" style="background-color: ${categoryColor}20; color: ${categoryColor};">
                        ${product.category}
                    </div>

                    <h3 class="product-name">${product.name}</h3>
                    <p class="product-description">${product.description}</p>

                    ${priceHtml}

                    <div class="product-meta">
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span>${product.preparation_time || 0} دقيقة</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-eye"></i>
                            <span>${product.view_count || 0} مشاهدة</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-shopping-cart"></i>
                            <span>${product.order_count || 0} طلب</span>
                        </div>
                    </div>

                    <div class="product-tags">
                        ${(product.tags || []).map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                </div>

                <div class="product-card-footer">
                    <div class="quick-actions">
                        <button data-action="edit-product" data-product-id="${product.id}"
                                class="btn btn-sm btn-primary" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button data-action="toggle-availability" data-product-id="${product.id}"
                                class="btn btn-sm ${product.is_available ? 'btn-warning' : 'btn-success'}"
                                title="${product.is_available ? 'إخفاء' : 'إظهار'}">
                            <i class="fas ${product.is_available ? 'fa-eye-slash' : 'fa-eye'}"></i>
                        </button>
                        <button data-action="delete-product" data-product-id="${product.id}"
                                class="btn btn-sm btn-danger" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    renderProductImage(product) {
        if (product.image_url) {
            return `
                <img src="${product.image_url}" alt="${product.name}" class="product-image"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="product-icon-fallback" style="display: none;">
                    <i class="${product.icon}"></i>
                </div>
            `;
        } else {
            return `
                <div class="product-icon-fallback">
                    <i class="${product.icon}"></i>
                </div>
            `;
        }
    }

    renderProductBadges(product) {
        const badges = [];

        if (product.is_featured) badges.push('<span class="badge badge-featured">مميز</span>');
        if (product.is_new) badges.push('<span class="badge badge-new">جديد</span>');
        if (product.is_popular) badges.push('<span class="badge badge-popular">شائع</span>');
        if (product.discount_percentage > 0) badges.push(`<span class="badge badge-discount">خصم ${product.discount_percentage}%</span>`);
        if (product.is_vegetarian) badges.push('<span class="badge badge-vegetarian">نباتي</span>');
        if (product.is_spicy) badges.push('<span class="badge badge-spicy">حار</span>');

        return badges.length > 0 ? `<div class="product-badges">${badges.join('')}</div>` : '';
    }

    renderProductPrice(product) {
        if (product.discount_percentage > 0) {
            return `
                <div class="product-price">
                    <span class="current-price">${product.price} جنيه</span>
                    <span class="original-price">${product.original_price} جنيه</span>
                    <span class="discount-badge">خصم ${product.discount_percentage}%</span>
                </div>
            `;
        } else {
            return `
                <div class="product-price">
                    <span class="current-price">${product.price} جنيه</span>
                </div>
            `;
        }
    }

    renderEmptyState() {
        return `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-utensils"></i>
                </div>
                <h3>لا توجد منتجات</h3>
                <p>لم يتم العثور على منتجات تطابق المعايير المحددة</p>
                <button data-action="add-product" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة منتج جديد
                </button>
            </div>
        `;
    }

    renderStatistics() {
        const stats = this.calculateStatistics();

        document.getElementById('totalProducts').textContent = stats.totalProducts;
        document.getElementById('totalCategories').textContent = stats.totalCategories;
        document.getElementById('featuredProducts').textContent = stats.featuredProducts;
        document.getElementById('averagePrice').textContent = stats.averagePrice + ' جنيه';
    }

    calculateStatistics() {
        const totalProducts = this.products.length;
        const totalCategories = this.categories.length;
        const featuredProducts = this.products.filter(p => p.is_featured).length;
        const averagePrice = totalProducts > 0 ?
            Math.round(this.products.reduce((sum, p) => sum + p.price, 0) / totalProducts) : 0;

        return {
            totalProducts,
            totalCategories,
            featuredProducts,
            averagePrice
        };
    }

    getFilteredProducts() {
        let filtered = [...this.products];

        // Apply search filter
        if (this.filters.search) {
            const searchTerm = this.filters.search.toLowerCase();
            filtered = filtered.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm) ||
                (product.tags || []).some(tag => tag.toLowerCase().includes(searchTerm))
            );
        }

        // Apply category filter
        if (this.filters.category !== 'all') {
            filtered = filtered.filter(product => product.category_id === this.filters.category);
        }

        // Apply availability filter
        if (this.filters.availability !== 'all') {
            const isAvailable = this.filters.availability === 'available';
            filtered = filtered.filter(product => product.is_available === isAvailable);
        }

        // Apply featured filter
        if (this.filters.featured !== 'all') {
            const isFeatured = this.filters.featured === 'featured';
            filtered = filtered.filter(product => product.is_featured === isFeatured);
        }

        // Apply sorting
        filtered.sort((a, b) => {
            let aValue = a[this.sortBy];
            let bValue = b[this.sortBy];

            if (this.sortBy === 'name') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (this.sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        return filtered;
    }

    getPaginatedProducts(products) {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        return products.slice(startIndex, endIndex);
    }

    updatePagination() {
        const filteredProducts = this.getFilteredProducts();
        const totalPages = Math.ceil(filteredProducts.length / this.itemsPerPage);
        const container = document.getElementById('paginationContainer');

        if (!container || totalPages <= 1) {
            if (container) container.innerHTML = '';
            return;
        }

        let paginationHtml = '';

        // Previous button
        paginationHtml += `
            <button ${this.currentPage === 1 ? 'disabled' : ''}
                    onclick="advancedProductManager.goToPage(${this.currentPage - 1})">
                <i class="fas fa-chevron-right"></i>
            </button>
        `;

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHtml += `
                    <button class="${i === this.currentPage ? 'active' : ''}"
                            onclick="advancedProductManager.goToPage(${i})">
                        ${i}
                    </button>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHtml += '<span>...</span>';
            }
        }

        // Next button
        paginationHtml += `
            <button ${this.currentPage === totalPages ? 'disabled' : ''}
                    onclick="advancedProductManager.goToPage(${this.currentPage + 1})">
                <i class="fas fa-chevron-left"></i>
            </button>
        `;

        container.innerHTML = paginationHtml;
    }

    goToPage(page) {
        const filteredProducts = this.getFilteredProducts();
        const totalPages = Math.ceil(filteredProducts.length / this.itemsPerPage);

        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderProducts();
            this.updatePagination();
        }
    }

    applyFilters() {
        this.currentPage = 1; // Reset to first page
        this.renderProducts();
        this.updatePagination();
    }

    // Product CRUD operations
    async openProductModal(productId = null) {
        this.currentProduct = productId;

        // Create modal if it doesn't exist
        if (!document.getElementById('productModal')) {
            this.createProductModal();
        }

        const modal = document.getElementById('productModal');
        const form = document.getElementById('productForm');

        // Reset form
        form.reset();

        if (productId) {
            // Edit mode
            const product = this.products.find(p => p.id === productId);
            if (product) {
                this.populateProductForm(product);
            }
            document.getElementById('modalTitle').textContent = 'تعديل المنتج';
        } else {
            // Add mode
            document.getElementById('modalTitle').textContent = 'إضافة منتج جديد';
        }

        modal.style.display = 'flex';
    }

    createProductModal() {
        const modalHtml = `
            <div class="modal" id="productModal" style="display: none;">
                <div class="modal-overlay"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 id="modalTitle">إضافة منتج جديد</h2>
                        <button class="modal-close">&times;</button>
                    </div>
                    <form id="productForm" class="product-form">
                        <!-- Form content will be added here -->
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.setupProductForm();
    }

    setupProductForm() {
        const form = document.getElementById('productForm');
        if (!form) return;

        form.innerHTML = `
            <div class="form-grid">
                <div class="form-group">
                    <label for="productName">اسم المنتج *</label>
                    <input type="text" id="productName" name="name" required>
                </div>

                <div class="form-group">
                    <label for="productNameEn">الاسم بالإنجليزية</label>
                    <input type="text" id="productNameEn" name="name_en">
                </div>

                <div class="form-group full-width">
                    <label for="productDescription">الوصف</label>
                    <textarea id="productDescription" name="description" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label for="productPrice">السعر (جنيه) *</label>
                    <input type="number" id="productPrice" name="price" min="0" step="0.01" required>
                </div>

                <div class="form-group">
                    <label for="productOriginalPrice">السعر الأصلي</label>
                    <input type="number" id="productOriginalPrice" name="original_price" min="0" step="0.01">
                </div>

                <div class="form-group">
                    <label for="productCategory">الفئة *</label>
                    <select id="productCategory" name="category_id" required>
                        <option value="">اختر الفئة</option>
                        ${this.categories.map(cat =>
                            `<option value="${cat.id}">${cat.name}</option>`
                        ).join('')}
                    </select>
                </div>

                <div class="form-group">
                    <label for="productPreparationTime">وقت التحضير (دقيقة)</label>
                    <input type="number" id="productPreparationTime" name="preparation_time" min="0">
                </div>

                <div class="form-group">
                    <label for="productIcon">الأيقونة</label>
                    <input type="text" id="productIcon" name="icon" placeholder="fas fa-utensils">
                </div>

                <div class="form-group">
                    <label for="productServingSize">حجم الحصة</label>
                    <input type="text" id="productServingSize" name="serving_size" placeholder="طبق متوسط">
                </div>

                <div class="form-group full-width">
                    <label for="productIngredients">المكونات (مفصولة بفاصلة)</label>
                    <input type="text" id="productIngredients" name="ingredients" placeholder="مكون 1, مكون 2, مكون 3">
                </div>

                <div class="form-group full-width">
                    <label for="productTags">العلامات (مفصولة بفاصلة)</label>
                    <input type="text" id="productTags" name="tags" placeholder="علامة 1, علامة 2, علامة 3">
                </div>

                <div class="form-group full-width">
                    <label>صور المنتج</label>
                    <div class="image-upload-area" id="imageUploadArea">
                        <input type="file" id="productImages" name="images" multiple accept="image/*" style="display: none;">
                        <div class="upload-placeholder" onclick="document.getElementById('productImages').click()">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>انقر لرفع الصور أو اسحبها هنا</p>
                        </div>
                        <div class="image-preview" id="imagePreview"></div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="productAvailable" name="is_available" checked>
                        <label for="productAvailable">متوفر للطلب</label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="productFeatured" name="is_featured">
                        <label for="productFeatured">منتج مميز</label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="productVegetarian" name="is_vegetarian">
                        <label for="productVegetarian">نباتي</label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="productSpicy" name="is_spicy">
                        <label for="productSpicy">حار</label>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ المنتج
                </button>
                <button type="button" class="btn btn-secondary modal-close">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        `;
    }

    // Continue with more methods...
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas ${type === 'success' ? 'fa-check-circle' :
                           type === 'error' ? 'fa-exclamation-circle' :
                           type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
            <button class="notification-close">&times;</button>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#d4edda' :
                        type === 'error' ? '#f8d7da' :
                        type === 'warning' ? '#fff3cd' : '#d1ecf1'};
            color: ${type === 'success' ? '#155724' :
                    type === 'error' ? '#721c24' :
                    type === 'warning' ? '#856404' : '#0c5460'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' :
                               type === 'error' ? '#f5c6cb' :
                               type === 'warning' ? '#ffeaa7' : '#bee5eb'};
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 300px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 5000);

        // Manual close
        notification.querySelector('.notification-close').addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }

    closeAllModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
        this.currentProduct = null;
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .modal-content {
        background: white;
        border-radius: 12px;
        max-width: 800px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid #ecf0f1;
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #7f8c8d;
    }

    .product-form {
        padding: 20px;
    }

    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
    }

    .form-group.full-width {
        grid-column: 1 / -1;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        color: #2c3e50;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        width: 100%;
        padding: 12px;
        border: 2px solid #ecf0f1;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s;
    }

    .form-group input:focus,
    .form-group textarea:focus,
    .form-group select:focus {
        outline: none;
        border-color: #3498db;
    }

    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .checkbox-group input[type="checkbox"] {
        width: auto;
    }

    .image-upload-area {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: border-color 0.3s;
    }

    .image-upload-area:hover {
        border-color: #3498db;
    }

    .upload-placeholder i {
        font-size: 2rem;
        color: #bdc3c7;
        margin-bottom: 10px;
    }

    .image-preview {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }

    .image-preview img {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
        border: 2px solid #ecf0f1;
    }

    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        padding-top: 20px;
        border-top: 1px solid #ecf0f1;
    }
`;
document.head.appendChild(style);

// Initialize
const advancedProductManager = new AdvancedProductManager();
window.advancedProductManager = advancedProductManager;

console.log('✅ AdvancedProductManager loaded');
