// Supabase Image Manager for Admin Panel
// مدير الصور باستخدام Supabase للوحة الإدارة

class SupabaseImageManager {
    constructor() {
        this.storageManager = null;
        this.currentProductId = null;
        this.uploadedImages = [];
        this.isInitialized = false;
        
        this.init();
    }

    async init() {
        try {
            // Wait for storage manager to be ready
            await this.waitForStorageManager();
            
            this.storageManager = window.supabaseStorageManager;
            this.isInitialized = true;
            
            console.log('✅ Supabase Image Manager initialized');
            this.setupEventListeners();
            
        } catch (error) {
            console.error('❌ Error initializing Supabase Image Manager:', error);
        }
    }

    async waitForStorageManager() {
        return new Promise((resolve) => {
            const checkManager = () => {
                if (window.supabaseStorageManager && window.supabaseStorageManager.isInitialized) {
                    resolve();
                } else {
                    setTimeout(checkManager, 100);
                }
            };
            checkManager();
        });
    }

    setupEventListeners() {
        // File input change
        const fileInput = document.getElementById('productImages');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelection(e.target.files);
            });
        }

        // Drag and drop
        const dropZone = document.getElementById('imageDropZone');
        if (dropZone) {
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('drag-over');
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('drag-over');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('drag-over');
                this.handleFileSelection(e.dataTransfer.files);
            });
        }
    }

    async handleFileSelection(files) {
        if (!this.isInitialized) {
            this.showMessage('نظام إدارة الصور غير جاهز بعد', 'error');
            return;
        }

        const fileArray = Array.from(files);
        if (fileArray.length === 0) return;

        this.showMessage(`جاري رفع ${fileArray.length} صورة...`, 'info');
        this.showUploadProgress(true);

        try {
            // Compress images before upload
            const compressedFiles = await Promise.all(
                fileArray.map(file => this.storageManager.compressImage(file, 0.8))
            );

            // Upload images
            const uploadResult = await this.storageManager.uploadMultipleImages(compressedFiles);

            if (uploadResult.success) {
                this.showMessage(`تم رفع ${uploadResult.successful} صورة بنجاح!`, 'success');
                
                // Add to uploaded images list
                uploadResult.data.forEach(imageData => {
                    this.uploadedImages.push({
                        id: Date.now() + Math.random(),
                        url: imageData.url,
                        path: imageData.path,
                        name: imageData.name,
                        size: imageData.size,
                        isPrimary: this.uploadedImages.length === 0 // First image is primary
                    });
                });

                this.renderImageGallery();
                
            } else {
                this.showMessage(`فشل في رفع ${uploadResult.failed} صورة`, 'error');
            }

        } catch (error) {
            console.error('Upload error:', error);
            this.showMessage('خطأ في رفع الصور: ' + error.message, 'error');
        } finally {
            this.showUploadProgress(false);
        }
    }

    renderImageGallery() {
        const gallery = document.getElementById('imageGallery');
        if (!gallery) return;

        if (this.uploadedImages.length === 0) {
            gallery.innerHTML = '<p class="text-center text-muted">لا توجد صور مرفوعة</p>';
            return;
        }

        const galleryHTML = this.uploadedImages.map((image, index) => `
            <div class="image-item ${image.isPrimary ? 'primary' : ''}" data-image-id="${image.id}">
                <div class="image-container">
                    <img src="${image.url}" alt="${image.name}" loading="lazy">
                    <div class="image-overlay">
                        <button class="btn btn-sm btn-primary" onclick="supabaseImageManager.setPrimaryImage('${image.id}')" 
                                ${image.isPrimary ? 'disabled' : ''}>
                            ${image.isPrimary ? 'صورة رئيسية' : 'جعل رئيسية'}
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="supabaseImageManager.deleteImage('${image.id}')">
                            حذف
                        </button>
                    </div>
                </div>
                <div class="image-info">
                    <small class="text-muted">${image.name}</small>
                    <small class="text-muted">${this.formatFileSize(image.size)}</small>
                </div>
            </div>
        `).join('');

        gallery.innerHTML = galleryHTML;
    }

    async deleteImage(imageId) {
        if (!confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
            return;
        }

        const imageIndex = this.uploadedImages.findIndex(img => img.id == imageId);
        if (imageIndex === -1) return;

        const image = this.uploadedImages[imageIndex];

        try {
            // Delete from Supabase Storage
            const deleteResult = await this.storageManager.deleteImage(image.path);
            
            if (deleteResult.success) {
                // Remove from local array
                this.uploadedImages.splice(imageIndex, 1);
                
                // If deleted image was primary, make first image primary
                if (image.isPrimary && this.uploadedImages.length > 0) {
                    this.uploadedImages[0].isPrimary = true;
                }
                
                this.renderImageGallery();
                this.showMessage('تم حذف الصورة بنجاح', 'success');
                
            } else {
                throw new Error(deleteResult.error);
            }

        } catch (error) {
            console.error('Delete error:', error);
            this.showMessage('خطأ في حذف الصورة: ' + error.message, 'error');
        }
    }

    setPrimaryImage(imageId) {
        // Remove primary from all images
        this.uploadedImages.forEach(img => img.isPrimary = false);
        
        // Set new primary
        const image = this.uploadedImages.find(img => img.id == imageId);
        if (image) {
            image.isPrimary = true;
            this.renderImageGallery();
            this.showMessage('تم تحديد الصورة الرئيسية', 'success');
        }
    }

    getImagesForProduct() {
        return this.uploadedImages.map(img => ({
            url: img.url,
            path: img.path,
            name: img.name,
            isPrimary: img.isPrimary
        }));
    }

    loadProductImages(productId, images = []) {
        this.currentProductId = productId;
        this.uploadedImages = images.map((img, index) => ({
            id: Date.now() + index,
            url: img.url,
            path: img.path,
            name: img.name || `image-${index + 1}`,
            size: img.size || 0,
            isPrimary: img.isPrimary || index === 0
        }));
        
        this.renderImageGallery();
    }

    clearImages() {
        this.uploadedImages = [];
        this.currentProductId = null;
        this.renderImageGallery();
    }

    showUploadProgress(show) {
        const progressElement = document.getElementById('uploadProgress');
        if (progressElement) {
            progressElement.style.display = show ? 'block' : 'none';
        }
    }

    showMessage(message, type) {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.image-message');
        existingMessages.forEach(msg => msg.remove());

        // Create new message
        const messageDiv = document.createElement('div');
        messageDiv.className = `image-message alert alert-${type}`;
        messageDiv.style.cssText = `
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
            background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
            color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        `;
        messageDiv.textContent = message;

        // Insert message
        const container = document.getElementById('imageManagementContainer') || document.body;
        container.insertBefore(messageDiv, container.firstChild);

        // Auto remove after 3 seconds
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async getStorageStats() {
        if (!this.isInitialized) return null;
        
        try {
            const stats = await this.storageManager.getStorageStats();
            return stats;
        } catch (error) {
            console.error('Error getting storage stats:', error);
            return null;
        }
    }
}

// Initialize image manager
const supabaseImageManager = new SupabaseImageManager();

// Make available globally
window.supabaseImageManager = supabaseImageManager;

console.log('✅ Supabase Image Manager loaded');
