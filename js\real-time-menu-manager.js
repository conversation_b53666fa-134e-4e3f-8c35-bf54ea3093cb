// Real-Time Menu Manager with Supabase Integration
// مدير القائمة في الوقت الفعلي مع تكامل Supabase

class RealTimeMenuManager {
    constructor() {
        this.categories = [];
        this.products = [];
        this.isLoaded = false;
        this.supabase = null;
        this.storageManager = null;
        this.listeners = [];
        
        console.log('🚀 RealTimeMenuManager initializing...');
        this.init();
    }

    async init() {
        try {
            // Wait for dependencies
            await this.waitForDependencies();
            
            // Load initial data
            await this.loadData();
            
            // Setup real-time listeners
            this.setupRealTimeListeners();
            
            // Mark as loaded
            this.isLoaded = true;
            
            // Dispatch loaded event
            this.dispatchEvent('menuDataLoaded');
            
            console.log('✅ RealTimeMenuManager initialized successfully');
            
        } catch (error) {
            console.error('❌ Error initializing RealTimeMenuManager:', error);
            this.loadFallbackData();
        }
    }

    async waitForDependencies() {
        return new Promise((resolve) => {
            const checkDependencies = () => {
                if (window.supabaseManager && window.supabaseStorageManager) {
                    this.supabase = window.supabaseManager;
                    this.storageManager = window.supabaseStorageManager;
                    console.log('✅ Dependencies loaded');
                    resolve();
                } else {
                    setTimeout(checkDependencies, 100);
                }
            };
            checkDependencies();
        });
    }

    async loadData() {
        try {
            console.log('📥 Loading menu data...');

            // Load categories
            await this.loadCategories();
            
            // Load products
            await this.loadProducts();
            
            console.log(`✅ Data loaded: ${this.categories.length} categories, ${this.products.length} products`);
            
        } catch (error) {
            console.error('❌ Error loading data:', error);
            this.loadFallbackData();
        }
    }

    async loadCategories() {
        try {
            if (this.supabase && this.supabase.isInitialized) {
                const result = await this.supabase.getCategories();
                if (result.success && result.data) {
                    this.categories = result.data;
                    console.log(`✅ Loaded ${this.categories.length} categories from Supabase`);
                    return;
                }
            }
            
            // Fallback categories
            this.categories = [
                { id: '1', name: 'المقبلات', name_en: 'Appetizers', is_active: true, sort_order: 1 },
                { id: '2', name: 'الأطباق الرئيسية', name_en: 'Main Dishes', is_active: true, sort_order: 2 },
                { id: '3', name: 'المشروبات', name_en: 'Beverages', is_active: true, sort_order: 3 },
                { id: '4', name: 'الحلويات', name_en: 'Desserts', is_active: true, sort_order: 4 }
            ];
            console.log('📦 Using fallback categories');
            
        } catch (error) {
            console.error('❌ Error loading categories:', error);
        }
    }

    async loadProducts() {
        try {
            if (this.supabase && this.supabase.isInitialized) {
                const result = await this.supabase.getProducts();
                if (result.success && result.data) {
                    this.products = result.data.map(product => this.normalizeProduct(product));
                    console.log(`✅ Loaded ${this.products.length} products from Supabase`);
                    return;
                }
            }
            
            // Load from localStorage as fallback
            const localProducts = localStorage.getItem('restaurant_products');
            if (localProducts) {
                this.products = JSON.parse(localProducts).map(product => this.normalizeProduct(product));
                console.log(`📦 Loaded ${this.products.length} products from localStorage`);
                return;
            }
            
            // Ultimate fallback - sample data
            this.loadFallbackProducts();
            
        } catch (error) {
            console.error('❌ Error loading products:', error);
            this.loadFallbackProducts();
        }
    }

    normalizeProduct(product) {
        return {
            id: product.id,
            name: product.name,
            name_en: product.name_en || product.name,
            description: product.description || '',
            description_en: product.description_en || product.description || '',
            price: parseFloat(product.price) || 0,
            category_id: product.category_id,
            category: this.getCategoryName(product.category_id),
            images: product.images || [],
            image_url: this.getProductImageUrl(product),
            icon: product.icon || 'fas fa-utensils',
            is_available: product.is_available !== false,
            is_featured: product.is_featured || false,
            sort_order: product.sort_order || 0,
            created_at: product.created_at,
            updated_at: product.updated_at
        };
    }

    getProductImageUrl(product) {
        // Priority: images array > image_url > default
        if (product.images && product.images.length > 0) {
            const primaryImage = product.images.find(img => img.isPrimary) || product.images[0];
            return primaryImage.url || primaryImage.path;
        }
        
        if (product.image_url) {
            return product.image_url;
        }
        
        return null;
    }

    getCategoryName(categoryId) {
        const category = this.categories.find(cat => cat.id === categoryId);
        return category ? category.name : 'عام';
    }

    loadFallbackProducts() {
        this.products = [
            {
                id: 'p1',
                name: 'حمص بالطحينة',
                description: 'حمص طازج مع الطحينة والزيت والبقدونس',
                price: 25,
                category_id: '1',
                category: 'المقبلات',
                icon: 'fas fa-seedling',
                is_available: true,
                images: [],
                image_url: null
            },
            {
                id: 'p2',
                name: 'كباب مشوي',
                description: 'كباب لحم مشوي مع الخضار والأرز',
                price: 85,
                category_id: '2',
                category: 'الأطباق الرئيسية',
                icon: 'fas fa-drumstick-bite',
                is_available: true,
                images: [],
                image_url: null
            },
            {
                id: 'p3',
                name: 'شاي أحمر',
                description: 'شاي أحمر طازج مع النعناع',
                price: 15,
                category_id: '3',
                category: 'المشروبات',
                icon: 'fas fa-coffee',
                is_available: true,
                images: [],
                image_url: null
            }
        ];
        console.log('📦 Using fallback products');
    }

    loadFallbackData() {
        this.loadCategories();
        this.loadFallbackProducts();
        this.isLoaded = true;
        this.dispatchEvent('menuDataLoaded');
    }

    setupRealTimeListeners() {
        // Listen for storage changes (for cross-tab sync)
        window.addEventListener('storage', (e) => {
            if (e.key === 'restaurant_products') {
                console.log('🔄 Products updated in another tab, reloading...');
                this.loadProducts().then(() => {
                    this.dispatchEvent('menuDataRefreshed');
                });
            }
        });

        // Listen for custom events from admin panel
        window.addEventListener('productAdded', (e) => {
            console.log('➕ Product added:', e.detail);
            this.addProduct(e.detail);
        });

        window.addEventListener('productUpdated', (e) => {
            console.log('✏️ Product updated:', e.detail);
            this.updateProduct(e.detail);
        });

        window.addEventListener('productDeleted', (e) => {
            console.log('🗑️ Product deleted:', e.detail);
            this.removeProduct(e.detail.id);
        });
    }

    // Real-time update methods
    addProduct(productData) {
        const normalizedProduct = this.normalizeProduct(productData);
        this.products.unshift(normalizedProduct);
        this.saveToLocalStorage();
        this.dispatchEvent('menuDataRefreshed');
    }

    updateProduct(productData) {
        const index = this.products.findIndex(p => p.id === productData.id);
        if (index !== -1) {
            this.products[index] = this.normalizeProduct(productData);
            this.saveToLocalStorage();
            this.dispatchEvent('menuDataRefreshed');
        }
    }

    removeProduct(productId) {
        this.products = this.products.filter(p => p.id !== productId);
        this.saveToLocalStorage();
        this.dispatchEvent('menuDataRefreshed');
    }

    saveToLocalStorage() {
        try {
            localStorage.setItem('restaurant_products', JSON.stringify(this.products));
        } catch (error) {
            console.error('Error saving to localStorage:', error);
        }
    }

    // Public API methods
    getCategories() {
        return this.categories.filter(cat => cat.is_active !== false);
    }

    getProducts(categoryId = null) {
        let products = this.products.filter(product => product.is_available !== false);
        
        if (categoryId && categoryId !== 'all') {
            products = products.filter(product => product.category_id === categoryId);
        }
        
        return products.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
    }

    getProductById(id) {
        return this.products.find(product => product.id === id);
    }

    searchProducts(query) {
        if (!query) return this.getProducts();
        
        const searchTerm = query.toLowerCase();
        return this.products.filter(product => 
            product.is_available !== false && (
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm) ||
                (product.name_en && product.name_en.toLowerCase().includes(searchTerm))
            )
        );
    }

    isDataLoaded() {
        return this.isLoaded;
    }

    async waitForData() {
        if (this.isLoaded) return;
        
        return new Promise((resolve) => {
            const checkLoaded = () => {
                if (this.isLoaded) {
                    resolve();
                } else {
                    setTimeout(checkLoaded, 100);
                }
            };
            checkLoaded();
        });
    }

    dispatchEvent(eventName) {
        const event = new CustomEvent(eventName, {
            detail: {
                categories: this.categories,
                products: this.products,
                timestamp: new Date().toISOString()
            }
        });
        window.dispatchEvent(event);
    }

    // Force refresh from database
    async refresh() {
        console.log('🔄 Force refreshing data...');
        await this.loadData();
        this.dispatchEvent('menuDataRefreshed');
    }
}

// Initialize and make available globally
const realTimeMenuManager = new RealTimeMenuManager();
window.realTimeMenuManager = realTimeMenuManager;

// Backward compatibility
window.menuDataManager = realTimeMenuManager;

console.log('✅ RealTimeMenuManager loaded');
