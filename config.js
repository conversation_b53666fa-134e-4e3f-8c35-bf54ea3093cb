// Configuration file for <PERSON> Restaurant Ordering System
// ملف التكوين لنظام طلبات مطعم محمد الاشرافي

const CONFIG = {
    // Restaurant Information - معلومات المطعم
    restaurant: {
        name: 'محم<PERSON> الاشرافي',
        nameEn: '<PERSON>',
        description: 'مطعم وكافيه يقدم أشهى الأطباق والمشروبات',
        phone: '+201014840269',
        whatsapp: '201014840269', // Without + sign for WhatsApp API
        email: '<EMAIL>',
        address: 'القاهرة، مصر',
        workingHours: 'يومياً من 9 صباحاً حتى 12 منتصف الليل',
        logo: 'fas fa-utensils', // Font Awesome icon class
        currency: 'جنيه',
        currencySymbol: 'ج.م'
    },

    // System Settings - إعدادات النظام
    system: {
        version: '1.0.0',
        language: 'ar',
        direction: 'rtl',
        theme: {
            primaryColor: '#2c5aa0',
            secondaryColor: '#1e3a8a',
            accentColor: '#ffd700',
            successColor: '#28a745',
            errorColor: '#dc3545',
            warningColor: '#ffc107'
        },
        features: {
            enableSearch: true,
            enableCategories: true,
            enableCart: true,
            enableWhatsApp: true,
            enableNotifications: true,
            enableAnimations: true,
            enableKeyboardShortcuts: true,
            enableAutoSave: true
        }
    },

    // Admin Panel Settings - إعدادات لوحة الإدارة
    admin: {
        sessionTimeout: 24, // hours
        autoRefreshInterval: 30, // seconds
        maxLoginAttempts: 5,
        lockoutDuration: 15, // minutes
        defaultCredentials: [
            {
                username: 'admin',
                password: 'admin123',
                role: 'super_admin',
                permissions: ['all']
            },
            {
                username: 'محمد',
                password: 'alashrafi2024',
                role: 'owner',
                permissions: ['all']
            },
            {
                username: 'manager',
                password: 'restaurant123',
                role: 'manager',
                permissions: ['menu', 'orders', 'analytics']
            },
            {
                username: 'staff',
                password: 'staff2024',
                role: 'staff',
                permissions: ['orders']
            }
        ]
    },

    // Order Settings - إعدادات الطلبات
    orders: {
        statuses: {
            pending: 'قيد الانتظار',
            processing: 'قيد التحضير',
            completed: 'مكتملة',
            cancelled: 'ملغية'
        },
        defaultStatus: 'pending',
        autoAccept: false,
        minOrderAmount: 0,
        maxOrderAmount: 10000,
        deliveryFee: 0,
        taxRate: 0 // 0.14 for 14% tax
    },

    // WhatsApp Integration - تكامل الواتساب
    whatsapp: {
        apiUrl: 'https://wa.me/',
        messageTemplate: {
            header: '🍽️ *طلب جديد من مطعم محمد الاشرافي* 🍽️',
            separator: '═══════════════════════════',
            footer: '🙏 *شكراً لاختياركم مطعم محمد الاشرافي*\nنتطلع لخدمتكم دائماً! ❤️'
        },
        enableFormatting: true,
        enableEmojis: true
    },

    // Storage Settings - إعدادات التخزين
    storage: {
        keys: {
            cart: 'restaurant_cart',
            menu: 'restaurant_menu_data',
            dashboard: 'restaurant_dashboard_data',
            settings: 'restaurant_settings',
            adminSession: 'admin_session',
            activities: 'admin_activities'
        },
        autoSaveInterval: 30000, // 30 seconds
        maxActivities: 100
    },

    // UI Settings - إعدادات واجهة المستخدم
    ui: {
        animations: {
            duration: 300, // milliseconds
            easing: 'ease',
            enableRipple: true,
            enableHover: true
        },
        notifications: {
            duration: 3000, // milliseconds
            position: 'top-right',
            enableSound: false
        },
        responsive: {
            mobileBreakpoint: 768,
            tabletBreakpoint: 1024
        }
    },

    // Performance Settings - إعدادات الأداء
    performance: {
        enableLazyLoading: true,
        enableCaching: true,
        enableCompression: false,
        maxCacheSize: 50, // MB
        cacheExpiry: 7 // days
    },

    // Security Settings - إعدادات الأمان
    security: {
        enableCSRF: false,
        enableXSS: true,
        enableHTTPS: false, // Set to true in production
        enableSecureHeaders: false,
        sessionSecure: false // Set to true in production
    },

    // Development Settings - إعدادات التطوير
    development: {
        enableDebug: true,
        enableConsoleLog: true,
        enableTestMode: false,
        mockData: false
    },

    // API Settings (for future use) - إعدادات API
    api: {
        baseUrl: '',
        timeout: 30000, // milliseconds
        retryAttempts: 3,
        enableCORS: true
    }
};

// Utility functions - وظائف مساعدة
const ConfigUtils = {
    // Get configuration value
    get: function(path) {
        return path.split('.').reduce((obj, key) => obj && obj[key], CONFIG);
    },

    // Set configuration value
    set: function(path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((obj, key) => obj[key] = obj[key] || {}, CONFIG);
        target[lastKey] = value;
    },

    // Merge configuration
    merge: function(newConfig) {
        Object.assign(CONFIG, newConfig);
    },

    // Validate configuration
    validate: function() {
        const required = [
            'restaurant.name',
            'restaurant.whatsapp',
            'system.version'
        ];

        for (const path of required) {
            if (!this.get(path)) {
                console.error(`Missing required configuration: ${path}`);
                return false;
            }
        }
        return true;
    },

    // Export configuration as JSON
    export: function() {
        return JSON.stringify(CONFIG, null, 2);
    },

    // Import configuration from JSON
    import: function(jsonString) {
        try {
            const importedConfig = JSON.parse(jsonString);
            this.merge(importedConfig);
            return true;
        } catch (error) {
            console.error('Failed to import configuration:', error);
            return false;
        }
    }
};

// Initialize configuration
if (typeof window !== 'undefined') {
    window.CONFIG = CONFIG;
    window.ConfigUtils = ConfigUtils;
    
    // Validate configuration on load
    if (!ConfigUtils.validate()) {
        console.warn('Configuration validation failed');
    }
}

// Export for Node.js environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, ConfigUtils };
}
