// Firebase Storage Security Rules for Mohamed <PERSON>
// قواعد الأمان لتخزين Firebase

rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             request.auth.token.admin == true;
    }
    
    function isManager() {
      return isAuthenticated() && 
             (request.auth.token.admin == true || 
              request.auth.token.manager == true);
    }
    
    function isValidImageType() {
      return resource.contentType.matches('image/.*');
    }
    
    function isValidImageSize() {
      return resource.size < 5 * 1024 * 1024; // 5MB limit
    }
    
    function isValidFileName() {
      return resource.name.matches('.*\\.(jpg|jpeg|png|gif|webp)$');
    }
    
    // Product images - public read, admin write
    match /products/{productId}/{imageId} {
      // Anyone can read product images
      allow read: if true;
      
      // Only admins can upload/update/delete product images
      allow write: if isAdmin() && 
                      isValidImageType() && 
                      isValidImageSize() && 
                      isValidFileName();
    }
    
    // Product thumbnails - public read, admin write
    match /products/{productId}/thumbnails/{thumbnailId} {
      // Anyone can read thumbnails
      allow read: if true;
      
      // Only admins can upload/update/delete thumbnails
      allow write: if isAdmin() && 
                      isValidImageType() && 
                      isValidImageSize() && 
                      isValidFileName();
    }
    
    // Category images - public read, admin write
    match /categories/{categoryId}/{imageId} {
      // Anyone can read category images
      allow read: if true;
      
      // Only admins can upload/update/delete category images
      allow write: if isAdmin() && 
                      isValidImageType() && 
                      isValidImageSize() && 
                      isValidFileName();
    }
    
    // User profile images - user can manage their own
    match /users/{userId}/profile/{imageId} {
      // Users can read/write their own profile images
      allow read, write: if isAuthenticated() && 
                            request.auth.uid == userId && 
                            isValidImageType() && 
                            isValidImageSize() && 
                            isValidFileName();
      
      // Admins can read all profile images
      allow read: if isAdmin();
    }
    
    // Temporary uploads - authenticated users only
    match /temp/{userId}/{imageId} {
      // Users can upload to their temp folder
      allow read, write: if isAuthenticated() && 
                            request.auth.uid == userId && 
                            isValidImageType() && 
                            isValidImageSize() && 
                            isValidFileName();
      
      // Auto-delete temp files after 24 hours (handled by Cloud Function)
    }
    
    // Admin uploads - admin only
    match /admin/{path=**} {
      allow read, write: if isAdmin();
    }
    
    // System backups - admin only
    match /backups/{path=**} {
      allow read, write: if isAdmin();
    }
    
    // Error logs and reports - admin only
    match /reports/{path=**} {
      allow read, write: if isAdmin();
    }
    
    // Public assets - public read, admin write
    match /public/{path=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Promotion images - public read, admin write
    match /promotions/{promotionId}/{imageId} {
      // Anyone can read promotion images
      allow read: if true;
      
      // Only admins can upload/update/delete promotion images
      allow write: if isAdmin() && 
                      isValidImageType() && 
                      isValidImageSize() && 
                      isValidFileName();
    }
    
    // Menu PDFs and documents - public read, admin write
    match /documents/{documentId} {
      // Anyone can read documents (like menu PDFs)
      allow read: if true;
      
      // Only admins can upload/update/delete documents
      allow write: if isAdmin();
    }
    
    // Analytics exports - admin only
    match /analytics/{path=**} {
      allow read, write: if isAdmin();
    }
    
    // Default deny rule
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
