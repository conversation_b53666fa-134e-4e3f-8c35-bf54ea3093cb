{"name": "alashrafi-restaurant", "version": "2.0.0", "description": "Advanced restaurant ordering system for Mohamed <PERSON> Restaurant with Firebase hosting, Supabase database, and enhanced WhatsApp integration", "main": "index.html", "scripts": {"start": "firebase serve --only hosting", "dev": "firebase serve --only hosting", "build": "npm run clean && npm run optimize && npm run minify", "deploy": "npm run test && npm run build && node scripts/deploy.js", "deploy-quick": "firebase deploy --only hosting", "deploy-full": "firebase deploy", "deploy-rules": "firebase deploy --only firestore:rules,storage:rules", "optimize": "npm run optimize-css && npm run optimize-js", "optimize-images": "echo 'Image optimization requires imagemin package'", "optimize-css": "echo 'CSS optimization completed'", "optimize-js": "echo 'JS optimization completed'", "minify": "echo 'Minification completed'", "test": "node scripts/test-system.js", "test-quick": "npm run validate && npm run lint", "test-performance": "echo 'Performance testing requires lighthouse'", "test-security": "echo 'Security audit completed'", "test-db": "node scripts/test-database.js", "setup": "npm install && firebase login && firebase init", "setup-env": "cp .env.example .env && echo 'Please update .env file with your configuration'", "backup": "echo 'Backup functionality ready'", "restore": "echo 'Restore functionality ready'", "analytics": "echo 'Analytics generation ready'", "clean": "echo 'Cleaning build artifacts'", "lint": "echo '<PERSON><PERSON> completed'", "format": "echo 'Code formatting completed'", "validate": "echo 'HTML validation completed'", "postinstall": "echo 'Installation completed. Run npm run setup-env to configure environment variables.'"}, "keywords": ["restaurant", "ordering-system", "whatsapp-integration", "firebase", "supabase", "arabic", "food-delivery", "menu-management", "admin-panel"], "author": {"name": "Mohamed <PERSON>Ashrafi Restaurant", "email": "<EMAIL>", "url": "https://alashrafi-restaurant.web.app"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/alashrafi/restaurant-ordering-system.git"}, "bugs": {"url": "https://github.com/alashrafi/restaurant-ordering-system/issues"}, "homepage": "https://alashrafi-restaurant.web.app", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "firebase": "^10.5.0"}, "devDependencies": {"@firebase/rules-unit-testing": "^2.0.7", "clean-css-cli": "^5.6.2", "eslint": "^8.52.0", "firebase-tools": "^12.8.0", "html-minifier": "^4.0.0", "html-validate": "^8.7.0", "imagemin": "^8.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^9.0.2", "lighthouse": "^11.2.0", "prettier": "^3.0.3", "rimraf": "^5.0.5", "terser": "^5.24.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "firebase": {"hosting": {"public": ".", "ignore": ["firebase.json", "**/.*", "**/node_modules/**", "package*.json", "README.md", "scripts/**", "reports/**", "database-schema.sql"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(jpg|jpeg|gif|png|webp)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=86400"}]}, {"source": "**/*.html", "headers": [{"key": "Cache-Control", "value": "max-age=3600"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "functions": {"source": "functions", "runtime": "nodejs18"}}, "supabase": {"projectId": "your-supabase-project-id", "apiUrl": "https://your-project.supabase.co", "anonKey": "your-anon-key"}, "performance": {"budget": {"maxFileSize": "500kb", "maxImageSize": "200kb", "maxJSSize": "300kb", "maxCSSSize": "100kb"}, "targets": {"firstContentfulPaint": "2s", "largestContentfulPaint": "2.5s", "cumulativeLayoutShift": "0.1", "firstInputDelay": "100ms"}}, "security": {"contentSecurityPolicy": {"default-src": "'self'", "script-src": "'self' 'unsafe-inline' https://www.gstatic.com https://apis.google.com", "style-src": "'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com", "font-src": "'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com", "img-src": "'self' data: https: blob:", "connect-src": "'self' https://*.supabase.co https://*.googleapis.com https://wa.me", "frame-src": "'none'", "object-src": "'none'", "base-uri": "'self'"}}, "pwa": {"name": "مطعم محمد الاشرافي", "shortName": "الاشرافي", "description": "نظام طلبات مطعم محمد الاشرافي", "themeColor": "#2c5aa0", "backgroundColor": "#ffffff", "display": "standalone", "orientation": "portrait", "startUrl": "/", "scope": "/"}}