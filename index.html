<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محمد الاشرافي - نظام الطلبات الإلكتروني</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-utensils"></i>
                    <h1>محمد الاشرافي</h1>
                    <p>مطعم وكافيه</p>
                </div>
                <div class="header-actions">
                    <button class="cart-btn" id="cartBtn">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count" id="cartCount">0</span>
                    </button>
                    <a href="admin/login.html" class="admin-link">
                        <i class="fas fa-user-shield"></i>
                        إدارة
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h2>أهلاً وسهلاً بكم في مطعم محمد الاشرافي</h2>
                <p>استمتع بأشهى الأطباق والمشروبات في أجواء مميزة</p>
                <button class="cta-btn" onclick="scrollToMenu()">
                    <i class="fas fa-arrow-down"></i>
                    تصفح القائمة
                </button>
            </div>
        </div>
    </section>

    <!-- Search Section -->
    <section class="search-section">
        <div class="container">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="ابحث عن طبق أو مشروب...">
            </div>
        </div>
    </section>

    <!-- Categories -->
    <section class="categories">
        <div class="container">
            <h3>الأقسام</h3>
            <div class="category-tabs" id="categoryTabs">
                <button class="category-tab active" data-category="all">
                    <i class="fas fa-th-large"></i>
                    الكل
                </button>
                <button class="category-tab" data-category="appetizers">
                    <i class="fas fa-seedling"></i>
                    المقبلات
                </button>
                <button class="category-tab" data-category="main-dishes">
                    <i class="fas fa-drumstick-bite"></i>
                    الأطباق الرئيسية
                </button>
                <button class="category-tab" data-category="beverages">
                    <i class="fas fa-coffee"></i>
                    المشروبات
                </button>
                <button class="category-tab" data-category="desserts">
                    <i class="fas fa-ice-cream"></i>
                    الحلويات
                </button>
            </div>
        </div>
    </section>

    <!-- Menu Items -->
    <section class="menu" id="menuSection">
        <div class="container">
            <div class="menu-grid" id="menuGrid">
                <!-- Menu items will be dynamically loaded here -->
            </div>
        </div>
    </section>

    <!-- Cart Sidebar -->
    <div class="cart-sidebar" id="cartSidebar">
        <div class="cart-header">
            <h3>سلة الطلبات</h3>
            <button class="close-cart" id="closeCart">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="cart-items" id="cartItems">
            <!-- Cart items will be dynamically loaded here -->
        </div>
        <div class="cart-footer">
            <div class="cart-total">
                <span>المجموع: <span id="cartTotal">0</span> جنيه</span>
            </div>
            <button class="checkout-btn" id="checkoutBtn">
                <i class="fas fa-credit-card"></i>
                إتمام الطلب
            </button>
        </div>
    </div>

    <!-- Order Modal -->
    <div class="modal" id="orderModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إتمام الطلب</h3>
                <button class="close-modal" id="closeModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="orderForm">
                    <div class="form-group">
                        <label for="customerName">الاسم</label>
                        <input type="text" id="customerName" required>
                    </div>
                    <div class="form-group">
                        <label for="customerPhone">رقم الهاتف *</label>
                        <input type="tel" id="customerPhone" required placeholder="01xxxxxxxxx"
                               oninput="cart.handlePhoneInput(this.value)">
                        <small class="form-hint">سيتم استخدام الواتساب لتأكيد الطلب</small>
                    </div>
                    <div class="form-group">
                        <label for="customerAddress">العنوان</label>
                        <textarea id="customerAddress" rows="3" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="orderNotes">ملاحظات إضافية</label>
                        <textarea id="orderNotes" rows="2" placeholder="أي ملاحظات خاصة بالطلب..."></textarea>
                    </div>
                    <div class="order-summary">
                        <h4>ملخص الطلب</h4>
                        <div id="orderSummary"></div>
                        <div class="total-amount">
                            <strong>المجموع الكلي: <span id="finalTotal">0</span> جنيه</strong>
                        </div>
                    </div>
                    <button type="submit" class="submit-order-btn">
                        <i class="fab fa-whatsapp"></i>
                        إرسال الطلب عبر واتساب
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Scroll to Top Button -->
    <button class="scroll-to-top" id="scrollToTop">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Notification Container -->
    <div id="notificationContainer"></div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>محمد الاشرافي</h4>
                    <p>مطعم وكافيه يقدم أشهى الأطباق والمشروبات</p>
                </div>
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <p><i class="fab fa-whatsapp"></i> +201014840269</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
                <div class="footer-section">
                    <h4>ساعات العمل</h4>
                    <p>يومياً من 9 صباحاً حتى 12 منتصف الليل</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 محمد الاشرافي. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-storage-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-analytics-compat.js"></script>

    <!-- Supabase SDK -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script>
        // Make sure Supabase is available globally
        if (typeof window !== 'undefined' && window.supabase) {
            window.supabase = window.supabase;
        }
    </script>

    <!-- Application Scripts -->
    <script src="js/error-fixes.js"></script>
    <script src="js/error-handler.js"></script>
    <script src="js/performance-optimizer.js"></script>
    <script src="js/database-setup.js"></script>
    <script src="js/firebase-config.js"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/supabase-storage.js"></script>
    <script src="js/database-sync.js"></script>
    <script src="js/menu-data.js"></script>
    <script src="js/menu-data-manager.js"></script>
    <script src="js/whatsapp-enhanced.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
