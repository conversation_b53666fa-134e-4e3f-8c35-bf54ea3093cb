/* Products Management Dashboard Styles */

.products-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: white;
}

.stat-icon.products { background: linear-gradient(45deg, #3498db, #2980b9); }
.stat-icon.featured { background: linear-gradient(45deg, #f39c12, #e67e22); }
.stat-icon.available { background: linear-gradient(45deg, #27ae60, #229954); }
.stat-icon.revenue { background: linear-gradient(45deg, #e74c3c, #c0392b); }

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: bold;
    margin: 0;
    color: #2c3e50;
}

.stat-content p {
    margin: 0;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.products-filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 2fr;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
}

.search-box {
    position: relative;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.search-box input:focus {
    outline: none;
    border-color: #3498db;
}

.filter-select {
    padding: 12px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s;
}

.filter-select:focus {
    outline: none;
    border-color: #3498db;
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.bulk-actions input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    min-height: 200px;
}

.product-card {
    background: white;
    border: 1px solid #ecf0f1;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-card.unavailable {
    opacity: 0.7;
    filter: grayscale(50%);
}

.product-card-header {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 2;
}

.product-select {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.product-actions {
    display: flex;
    gap: 5px;
}

.product-actions .btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    padding: 0;
    background: rgba(255,255,255,0.9);
    color: #2c3e50;
    border: 1px solid rgba(255,255,255,0.5);
}

.product-image-container {
    height: 180px;
    background: linear-gradient(45deg, #3498db, #2980b9);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-icon-fallback {
    color: white;
    font-size: 3rem;
}

.product-badges {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    text-align: center;
}

.badge-featured { background: #ffd700; color: #333; }
.badge-new { background: #27ae60; color: white; }
.badge-discount { background: #e74c3c; color: white; }
.badge-popular { background: #9b59b6; color: white; }

.product-info {
    padding: 20px;
}

.product-category {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-bottom: 10px;
    background: #ecf0f1;
    color: #2c3e50;
}

.product-name {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 8px;
    color: #2c3e50;
    line-height: 1.3;
}

.product-description {
    color: #7f8c8d;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    font-size: 1.3rem;
    font-weight: bold;
    color: #27ae60;
    margin-bottom: 12px;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-bottom: 15px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.product-card-footer {
    padding: 0 20px 20px;
}

.quick-actions {
    display: flex;
    gap: 8px;
}

.quick-actions .btn {
    flex: 1;
    padding: 10px 12px;
    font-size: 0.9rem;
    text-align: center;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 80px 20px;
    color: #7f8c8d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #bdc3c7;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #2c3e50;
}

.empty-state p {
    margin-bottom: 20px;
}

/* Modal Styles */
.product-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.product-modal.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ecf0f1;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #7f8c8d;
}

.product-form {
    padding: 20px;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #2c3e50;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #ecf0f1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .products-filters {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .products-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .products-stats {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 15px;
    }
    
    .product-info {
        padding: 15px;
    }
    
    .quick-actions {
        flex-direction: column;
    }
}
