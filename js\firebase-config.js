// Firebase Configuration and Initialization
// تكوين وتهيئة Firebase

// Firebase configuration object
const firebaseConfig = {
    apiKey: "demo-api-key",
    authDomain: "alashrafi-restaurant-demo.firebaseapp.com",
    projectId: "alashrafi-restaurant-demo",
    storageBucket: "alashrafi-restaurant-demo.appspot.com",
    messagingSenderId: "123456789",
    appId: "1:123456789:web:demo123456789",
    measurementId: "G-DEMO123456"
};

class FirebaseManager {
    constructor() {
        this.app = null;
        this.auth = null;
        this.firestore = null;
        this.storage = null;
        this.analytics = null;
        this.isInitialized = false;
        
        this.init();
    }

    async init() {
        try {
            // Wait for Firebase SDK to load
            await this.waitForFirebase();
            
            // Initialize Firebase
            this.app = firebase.initializeApp(firebaseConfig);
            
            // Initialize services
            this.auth = firebase.auth();
            this.firestore = firebase.firestore();
            this.storage = firebase.storage();
            
            // Initialize Analytics if available
            if (typeof gtag !== 'undefined') {
                this.analytics = firebase.analytics();
            }
            
            // Configure Firestore settings
            this.firestore.settings({
                cacheSizeBytes: firebase.firestore.CACHE_SIZE_UNLIMITED
            });
            
            // Enable offline persistence
            await this.firestore.enablePersistence({
                synchronizeTabs: true
            }).catch((err) => {
                if (err.code === 'failed-precondition') {
                    console.warn('Multiple tabs open, persistence can only be enabled in one tab at a time.');
                } else if (err.code === 'unimplemented') {
                    console.warn('The current browser does not support all of the features required to enable persistence');
                }
            });
            
            this.isInitialized = true;
            this.setupAuthStateListener();
            
            console.log('Firebase initialized successfully');
            
        } catch (error) {
            console.error('Error initializing Firebase:', error);
            throw error;
        }
    }

    async waitForFirebase() {
        return new Promise((resolve) => {
            const checkFirebase = () => {
                if (typeof firebase !== 'undefined') {
                    resolve();
                } else {
                    setTimeout(checkFirebase, 100);
                }
            };
            checkFirebase();
        });
    }

    setupAuthStateListener() {
        this.auth.onAuthStateChanged((user) => {
            if (user) {
                console.log('User signed in:', user.uid);
                this.handleUserSignIn(user);
            } else {
                console.log('User signed out');
                this.handleUserSignOut();
            }
        });
    }

    handleUserSignIn(user) {
        // Store user info
        localStorage.setItem('firebase_user', JSON.stringify({
            uid: user.uid,
            email: user.email,
            displayName: user.displayName,
            photoURL: user.photoURL
        }));

        // Trigger custom event
        window.dispatchEvent(new CustomEvent('firebaseUserSignIn', { detail: user }));
    }

    handleUserSignOut() {
        // Clear user info
        localStorage.removeItem('firebase_user');
        
        // Trigger custom event
        window.dispatchEvent(new CustomEvent('firebaseUserSignOut'));
    }

    // Authentication methods
    async signInWithEmailAndPassword(email, password) {
        try {
            const result = await this.auth.signInWithEmailAndPassword(email, password);
            return { success: true, user: result.user };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async createUserWithEmailAndPassword(email, password) {
        try {
            const result = await this.auth.createUserWithEmailAndPassword(email, password);
            return { success: true, user: result.user };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async signOut() {
        try {
            await this.auth.signOut();
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    getCurrentUser() {
        return this.auth.currentUser;
    }

    // Firestore methods
    async addDocument(collection, data) {
        try {
            const docRef = await this.firestore.collection(collection).add({
                ...data,
                created_at: firebase.firestore.FieldValue.serverTimestamp(),
                updated_at: firebase.firestore.FieldValue.serverTimestamp()
            });
            return { success: true, id: docRef.id };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async updateDocument(collection, docId, data) {
        try {
            await this.firestore.collection(collection).doc(docId).update({
                ...data,
                updated_at: firebase.firestore.FieldValue.serverTimestamp()
            });
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async deleteDocument(collection, docId) {
        try {
            await this.firestore.collection(collection).doc(docId).delete();
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async getDocument(collection, docId) {
        try {
            const doc = await this.firestore.collection(collection).doc(docId).get();
            if (doc.exists) {
                return { success: true, data: { id: doc.id, ...doc.data() } };
            } else {
                return { success: false, error: 'Document not found' };
            }
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async getCollection(collection, options = {}) {
        try {
            let query = this.firestore.collection(collection);
            
            // Apply filters
            if (options.where) {
                options.where.forEach(condition => {
                    query = query.where(condition.field, condition.operator, condition.value);
                });
            }
            
            // Apply ordering
            if (options.orderBy) {
                query = query.orderBy(options.orderBy.field, options.orderBy.direction || 'asc');
            }
            
            // Apply limit
            if (options.limit) {
                query = query.limit(options.limit);
            }
            
            const snapshot = await query.get();
            const documents = [];
            
            snapshot.forEach(doc => {
                documents.push({ id: doc.id, ...doc.data() });
            });
            
            return { success: true, data: documents };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // Storage methods
    async uploadFile(path, file, metadata = {}) {
        try {
            const storageRef = this.storage.ref().child(path);
            const uploadTask = storageRef.put(file, metadata);
            
            return new Promise((resolve, reject) => {
                uploadTask.on('state_changed',
                    (snapshot) => {
                        const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
                        // Trigger progress event
                        window.dispatchEvent(new CustomEvent('firebaseUploadProgress', {
                            detail: { progress, snapshot }
                        }));
                    },
                    (error) => {
                        reject({ success: false, error: error.message });
                    },
                    async () => {
                        try {
                            const downloadURL = await uploadTask.snapshot.ref.getDownloadURL();
                            resolve({
                                success: true,
                                downloadURL: downloadURL,
                                path: path,
                                metadata: uploadTask.snapshot.metadata
                            });
                        } catch (error) {
                            reject({ success: false, error: error.message });
                        }
                    }
                );
            });
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async deleteFile(path) {
        try {
            const storageRef = this.storage.ref().child(path);
            await storageRef.delete();
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async getDownloadURL(path) {
        try {
            const storageRef = this.storage.ref().child(path);
            const url = await storageRef.getDownloadURL();
            return { success: true, url: url };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // Analytics methods
    logEvent(eventName, parameters = {}) {
        if (this.analytics) {
            this.analytics.logEvent(eventName, parameters);
        }
    }

    setUserProperties(properties) {
        if (this.analytics) {
            Object.keys(properties).forEach(key => {
                this.analytics.setUserProperties({ [key]: properties[key] });
            });
        }
    }

    // Utility methods
    getServerTimestamp() {
        return firebase.firestore.FieldValue.serverTimestamp();
    }

    getFieldValue() {
        return firebase.firestore.FieldValue;
    }

    // Real-time listeners
    onDocumentChange(collection, docId, callback) {
        return this.firestore.collection(collection).doc(docId).onSnapshot(callback);
    }

    onCollectionChange(collection, callback, options = {}) {
        let query = this.firestore.collection(collection);
        
        // Apply filters
        if (options.where) {
            options.where.forEach(condition => {
                query = query.where(condition.field, condition.operator, condition.value);
            });
        }
        
        // Apply ordering
        if (options.orderBy) {
            query = query.orderBy(options.orderBy.field, options.orderBy.direction || 'asc');
        }
        
        return query.onSnapshot(callback);
    }

    // Batch operations
    createBatch() {
        return this.firestore.batch();
    }

    async commitBatch(batch) {
        try {
            await batch.commit();
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
}

// Initialize Firebase Manager
let firebaseManager;

// Wait for DOM to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        firebaseManager = new FirebaseManager();
    });
} else {
    firebaseManager = new FirebaseManager();
}

// Make Firebase services available globally
window.firebaseManager = firebaseManager;
window.firebaseAuth = () => firebaseManager?.auth;
window.firebaseFirestore = () => firebaseManager?.firestore;
window.firebaseStorage = () => firebaseManager?.storage;
window.firebaseAnalytics = () => firebaseManager?.analytics;

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FirebaseManager;
}
