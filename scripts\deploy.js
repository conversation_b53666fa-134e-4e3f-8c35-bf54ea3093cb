#!/usr/bin/env node

// Deployment Script for <PERSON> System
// سكريبت النشر لنظام مطعم محمد الاشرافي

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class DeploymentManager {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.buildDir = path.join(this.projectRoot, 'dist');
        this.config = this.loadConfig();
        
        console.log('🚀 Starting deployment process...');
    }

    loadConfig() {
        try {
            const packageJson = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'package.json'), 'utf8'));
            return packageJson;
        } catch (error) {
            console.error('❌ Error loading package.json:', error.message);
            process.exit(1);
        }
    }

    async deploy() {
        try {
            console.log('📋 Pre-deployment checks...');
            await this.preDeploymentChecks();
            
            console.log('🔧 Building project...');
            await this.buildProject();
            
            console.log('🧪 Running tests...');
            await this.runTests();
            
            console.log('📦 Optimizing assets...');
            await this.optimizeAssets();
            
            console.log('🔒 Security checks...');
            await this.securityChecks();
            
            console.log('☁️ Deploying to Firebase...');
            await this.deployToFirebase();
            
            console.log('🔄 Post-deployment tasks...');
            await this.postDeploymentTasks();
            
            console.log('✅ Deployment completed successfully!');
            this.showDeploymentSummary();
            
        } catch (error) {
            console.error('❌ Deployment failed:', error.message);
            process.exit(1);
        }
    }

    async preDeploymentChecks() {
        // Check if Firebase CLI is installed
        try {
            execSync('firebase --version', { stdio: 'ignore' });
        } catch (error) {
            throw new Error('Firebase CLI is not installed. Run: npm install -g firebase-tools');
        }

        // Check if user is logged in to Firebase
        try {
            execSync('firebase projects:list', { stdio: 'ignore' });
        } catch (error) {
            throw new Error('Not logged in to Firebase. Run: firebase login');
        }

        // Check if .env file exists
        const envPath = path.join(this.projectRoot, '.env');
        if (!fs.existsSync(envPath)) {
            console.warn('⚠️ .env file not found. Using default configuration.');
        }

        // Check required files
        const requiredFiles = [
            'index.html',
            'firebase.json',
            'package.json'
        ];

        for (const file of requiredFiles) {
            const filePath = path.join(this.projectRoot, file);
            if (!fs.existsSync(filePath)) {
                throw new Error(`Required file missing: ${file}`);
            }
        }

        console.log('✅ Pre-deployment checks passed');
    }

    async buildProject() {
        try {
            // Clean previous build
            if (fs.existsSync(this.buildDir)) {
                fs.rmSync(this.buildDir, { recursive: true, force: true });
            }
            fs.mkdirSync(this.buildDir, { recursive: true });

            // Copy source files
            this.copyDirectory(this.projectRoot, this.buildDir, [
                'node_modules',
                'dist',
                '.git',
                '.env',
                'scripts',
                'reports',
                '*.md'
            ]);

            // Process HTML files
            await this.processHtmlFiles();

            // Process CSS files
            await this.processCssFiles();

            // Process JavaScript files
            await this.processJsFiles();

            console.log('✅ Project built successfully');
        } catch (error) {
            throw new Error(`Build failed: ${error.message}`);
        }
    }

    copyDirectory(src, dest, exclude = []) {
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }

        const items = fs.readdirSync(src);

        for (const item of items) {
            const srcPath = path.join(src, item);
            const destPath = path.join(dest, item);

            // Check if item should be excluded
            const shouldExclude = exclude.some(pattern => {
                if (pattern.includes('*')) {
                    return item.match(new RegExp(pattern.replace('*', '.*')));
                }
                return item === pattern;
            });

            if (shouldExclude) continue;

            const stat = fs.statSync(srcPath);

            if (stat.isDirectory()) {
                this.copyDirectory(srcPath, destPath, exclude);
            } else {
                fs.copyFileSync(srcPath, destPath);
            }
        }
    }

    async processHtmlFiles() {
        const htmlFiles = this.findFiles(this.buildDir, '.html');
        
        for (const file of htmlFiles) {
            let content = fs.readFileSync(file, 'utf8');
            
            // Minify HTML
            content = content
                .replace(/\s+/g, ' ')
                .replace(/>\s+</g, '><')
                .trim();
            
            // Add cache busting
            content = content.replace(
                /(href|src)="([^"]+\.(css|js))"/g,
                `$1="$2?v=${Date.now()}"`
            );
            
            fs.writeFileSync(file, content);
        }
    }

    async processCssFiles() {
        const cssFiles = this.findFiles(this.buildDir, '.css');
        
        for (const file of cssFiles) {
            let content = fs.readFileSync(file, 'utf8');
            
            // Minify CSS
            content = content
                .replace(/\/\*[\s\S]*?\*\//g, '')
                .replace(/\s+/g, ' ')
                .replace(/;\s*}/g, '}')
                .replace(/\s*{\s*/g, '{')
                .replace(/;\s*/g, ';')
                .trim();
            
            fs.writeFileSync(file, content);
        }
    }

    async processJsFiles() {
        const jsFiles = this.findFiles(this.buildDir, '.js');
        
        for (const file of jsFiles) {
            let content = fs.readFileSync(file, 'utf8');
            
            // Remove console.log statements in production
            content = content.replace(/console\.log\([^)]*\);?/g, '');
            
            // Basic minification
            content = content
                .replace(/\/\/.*$/gm, '')
                .replace(/\/\*[\s\S]*?\*\//g, '')
                .replace(/\s+/g, ' ')
                .trim();
            
            fs.writeFileSync(file, content);
        }
    }

    findFiles(dir, extension) {
        const files = [];
        
        function traverse(currentDir) {
            const items = fs.readdirSync(currentDir);
            
            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    traverse(fullPath);
                } else if (item.endsWith(extension)) {
                    files.push(fullPath);
                }
            }
        }
        
        traverse(dir);
        return files;
    }

    async runTests() {
        try {
            // Run basic validation tests
            console.log('Running HTML validation...');
            const htmlFiles = this.findFiles(this.buildDir, '.html');
            
            for (const file of htmlFiles) {
                const content = fs.readFileSync(file, 'utf8');
                
                // Basic HTML validation
                if (!content.includes('<!DOCTYPE html>')) {
                    console.warn(`⚠️ Missing DOCTYPE in ${file}`);
                }
                
                if (!content.includes('<html')) {
                    throw new Error(`Invalid HTML structure in ${file}`);
                }
            }
            
            console.log('✅ Tests passed');
        } catch (error) {
            throw new Error(`Tests failed: ${error.message}`);
        }
    }

    async optimizeAssets() {
        try {
            // Optimize images if imagemin is available
            try {
                execSync('npm list imagemin', { stdio: 'ignore' });
                console.log('Optimizing images...');
                execSync('npm run optimize-images', { stdio: 'inherit' });
            } catch (error) {
                console.log('⚠️ Image optimization skipped (imagemin not available)');
            }

            // Create gzipped versions of large files
            const largeFiles = this.findFiles(this.buildDir, '.js')
                .concat(this.findFiles(this.buildDir, '.css'))
                .filter(file => {
                    const stat = fs.statSync(file);
                    return stat.size > 1024; // Files larger than 1KB
                });

            for (const file of largeFiles) {
                try {
                    execSync(`gzip -k "${file}"`, { stdio: 'ignore' });
                } catch (error) {
                    // Gzip not available, skip
                }
            }

            console.log('✅ Assets optimized');
        } catch (error) {
            console.warn('⚠️ Asset optimization failed:', error.message);
        }
    }

    async securityChecks() {
        try {
            // Check for sensitive data in files
            const sensitivePatterns = [
                /password\s*[:=]\s*['"][^'"]+['"]/gi,
                /api[_-]?key\s*[:=]\s*['"][^'"]+['"]/gi,
                /secret\s*[:=]\s*['"][^'"]+['"]/gi,
                /token\s*[:=]\s*['"][^'"]+['"]/gi
            ];

            const allFiles = this.findFiles(this.buildDir, '.js')
                .concat(this.findFiles(this.buildDir, '.html'))
                .concat(this.findFiles(this.buildDir, '.css'));

            for (const file of allFiles) {
                const content = fs.readFileSync(file, 'utf8');
                
                for (const pattern of sensitivePatterns) {
                    if (pattern.test(content)) {
                        console.warn(`⚠️ Potential sensitive data found in ${file}`);
                    }
                }
            }

            console.log('✅ Security checks completed');
        } catch (error) {
            console.warn('⚠️ Security checks failed:', error.message);
        }
    }

    async deployToFirebase() {
        try {
            // Update firebase.json to point to build directory
            const firebaseConfig = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'firebase.json'), 'utf8'));
            firebaseConfig.hosting.public = 'dist';
            fs.writeFileSync(path.join(this.projectRoot, 'firebase.json'), JSON.stringify(firebaseConfig, null, 2));

            // Deploy to Firebase
            execSync('firebase deploy --only hosting', { 
                stdio: 'inherit',
                cwd: this.projectRoot
            });

            // Restore original firebase.json
            firebaseConfig.hosting.public = '.';
            fs.writeFileSync(path.join(this.projectRoot, 'firebase.json'), JSON.stringify(firebaseConfig, null, 2));

            console.log('✅ Deployed to Firebase successfully');
        } catch (error) {
            throw new Error(`Firebase deployment failed: ${error.message}`);
        }
    }

    async postDeploymentTasks() {
        try {
            // Generate deployment report
            const report = {
                timestamp: new Date().toISOString(),
                version: this.config.version,
                buildSize: this.calculateBuildSize(),
                deploymentUrl: 'https://alashrafi-restaurant.web.app',
                status: 'success'
            };

            const reportsDir = path.join(this.projectRoot, 'reports');
            if (!fs.existsSync(reportsDir)) {
                fs.mkdirSync(reportsDir, { recursive: true });
            }

            fs.writeFileSync(
                path.join(reportsDir, `deployment-${Date.now()}.json`),
                JSON.stringify(report, null, 2)
            );

            // Clean up build directory
            fs.rmSync(this.buildDir, { recursive: true, force: true });

            console.log('✅ Post-deployment tasks completed');
        } catch (error) {
            console.warn('⚠️ Post-deployment tasks failed:', error.message);
        }
    }

    calculateBuildSize() {
        let totalSize = 0;
        
        function calculateSize(dir) {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    calculateSize(fullPath);
                } else {
                    totalSize += stat.size;
                }
            }
        }
        
        if (fs.existsSync(this.buildDir)) {
            calculateSize(this.buildDir);
        }
        
        return `${(totalSize / 1024 / 1024).toFixed(2)} MB`;
    }

    showDeploymentSummary() {
        console.log('\n🎉 Deployment Summary:');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        console.log(`📦 Project: ${this.config.name}`);
        console.log(`🏷️  Version: ${this.config.version}`);
        console.log(`🌐 URL: https://alashrafi-restaurant.web.app`);
        console.log(`📊 Build Size: ${this.calculateBuildSize()}`);
        console.log(`⏰ Deployed at: ${new Date().toLocaleString('ar-EG')}`);
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        console.log('\n🚀 Your restaurant ordering system is now live!');
        console.log('📱 Test the system on different devices');
        console.log('🔍 Monitor performance and user feedback');
        console.log('📈 Check analytics for usage insights');
    }
}

// Run deployment if this script is executed directly
if (require.main === module) {
    const deployment = new DeploymentManager();
    deployment.deploy().catch(error => {
        console.error('💥 Deployment failed:', error.message);
        process.exit(1);
    });
}

module.exports = DeploymentManager;
