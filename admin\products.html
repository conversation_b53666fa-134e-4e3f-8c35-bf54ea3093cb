<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - مطعم محمد الاشرافي</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .products-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-card.unavailable {
            opacity: 0.7;
            filter: grayscale(50%);
        }

        .product-image-container {
            position: relative;
            height: 200px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-icon {
            color: white;
            font-size: 3rem;
        }

        .featured-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ffd700;
            color: #333;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .product-info {
            padding: 20px;
        }

        .product-info h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }

        .product-description {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .product-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .product-price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #27ae60;
        }

        .product-category {
            background: #ecf0f1;
            color: #2c3e50;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 0.8rem;
        }

        .product-actions {
            display: flex;
            gap: 8px;
            padding: 0 20px 20px;
        }

        .btn {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s;
            flex: 1;
            text-align: center;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        .image-upload {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .image-upload:hover {
            border-color: #3498db;
        }

        .image-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #bdc3c7;
        }

        .header-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .add-product-btn {
            background: #27ae60;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s;
        }

        .add-product-btn:hover {
            background: #229954;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <div class="header-actions">
            <div>
                <h1><i class="fas fa-utensils"></i> إدارة المنتجات</h1>
                <p>إضافة وتعديل وحذف منتجات القائمة</p>
            </div>
            <button class="add-product-btn" id="addProductBtn">
                <i class="fas fa-plus"></i> إضافة منتج جديد
            </button>
        </div>

        <!-- Products Container -->
        <div class="products-container" id="productsContainer">
            <!-- Products will be loaded here -->
        </div>
    </div>

    <!-- Product Modal -->
    <div class="modal" id="productModal">
        <div class="modal-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2 id="modalTitle">إضافة منتج جديد</h2>
                <button class="close-modal" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">&times;</button>
            </div>

            <form id="productForm">
                <div class="form-group">
                    <label for="productName">اسم المنتج *</label>
                    <input type="text" id="productName" name="name" required>
                </div>

                <div class="form-group">
                    <label for="productDescription">الوصف</label>
                    <textarea id="productDescription" name="description" placeholder="وصف المنتج..."></textarea>
                </div>

                <div class="form-group">
                    <label for="productPrice">السعر (جنيه) *</label>
                    <input type="number" id="productPrice" name="price" min="0" step="0.01" required>
                </div>

                <div class="form-group">
                    <label for="productCategory">الفئة *</label>
                    <select id="productCategory" name="category_id" required>
                        <option value="">اختر الفئة</option>
                        <option value="1">المقبلات</option>
                        <option value="2">الأطباق الرئيسية</option>
                        <option value="3">المشروبات</option>
                        <option value="4">الحلويات</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="productIcon">أيقونة المنتج</label>
                    <input type="text" id="productIcon" name="icon" placeholder="fas fa-utensils" value="fas fa-utensils">
                    <small style="color: #7f8c8d;">استخدم أيقونات Font Awesome</small>
                </div>

                <div class="form-group">
                    <label>صور المنتج</label>
                    <div class="image-upload" onclick="document.getElementById('productImages').click()">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>انقر لرفع الصور أو اسحبها هنا</p>
                        <small>يمكن رفع عدة صور (JPG, PNG, WebP)</small>
                    </div>
                    <input type="file" id="productImages" name="images" multiple accept="image/*" style="display: none;">
                    <div class="image-preview" id="imagePreview"></div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="productAvailable" name="is_available" checked>
                        <label for="productAvailable">متوفر للطلب</label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="productFeatured" name="is_featured">
                        <label for="productFeatured">منتج مميز</label>
                    </div>
                </div>

                <div style="display: flex; gap: 10px; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary" style="flex: 1;">
                        <i class="fas fa-save"></i> حفظ المنتج
                    </button>
                    <button type="button" class="btn btn-secondary close-modal" style="flex: 1; background: #95a5a6;">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Simple Auth Check
        function checkAuth() {
            const session = localStorage.getItem('admin_logged_in');
            if (!session) {
                window.location.replace('login.html');
                return false;
            }
            return true;
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
        });
    </script>

    <!-- Supabase SDK -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- Application Scripts -->
    <script src="../js/supabase-config.js"></script>
    <script src="../js/supabase-storage.js"></script>
    <script src="js/supabase-image-manager.js"></script>
    <script src="js/complete-product-manager.js"></script>
</body>
</html>
