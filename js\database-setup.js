// Database Setup and Initialization
// إعداد وتهيئة قاعدة البيانات

class DatabaseSetup {
    constructor() {
        this.isSetup = false;
        this.init();
    }

    async init() {
        try {
            // Check if data already exists
            if (this.hasExistingData()) {
                console.log('Database already initialized');
                this.isSetup = true;
                return;
            }

            // Setup initial data
            await this.setupInitialData();
            this.isSetup = true;
            console.log('Database setup completed');

        } catch (error) {
            console.error('Database setup failed:', error);
        }
    }

    hasExistingData() {
        const categories = localStorage.getItem('restaurant_categories');
        const products = localStorage.getItem('restaurant_products');
        return categories && products;
    }

    async setupInitialData() {
        // Setup categories
        const categories = [
            {
                id: '1',
                name: 'المقبلات',
                name_en: 'Appetizers',
                description: 'مقبلات شهية ومتنوعة',
                sort_order: 1,
                is_active: true,
                created_at: new Date().toISOString()
            },
            {
                id: '2',
                name: 'الأطباق الرئيسية',
                name_en: 'Main Dishes',
                description: 'أطباق رئيسية مميزة',
                sort_order: 2,
                is_active: true,
                created_at: new Date().toISOString()
            },
            {
                id: '3',
                name: 'المشروبات',
                name_en: 'Beverages',
                description: 'مشروبات ساخنة وباردة',
                sort_order: 3,
                is_active: true,
                created_at: new Date().toISOString()
            },
            {
                id: '4',
                name: 'الحلويات',
                name_en: 'Desserts',
                description: 'حلويات لذيذة ومتنوعة',
                sort_order: 4,
                is_active: true,
                created_at: new Date().toISOString()
            }
        ];

        // Setup products
        const products = [
            {
                id: '1',
                name: 'حمص بالطحينة',
                name_en: 'Hummus with Tahini',
                description: 'حمص طازج مع الطحينة والزيت والبقدونس، يُقدم مع الخبز العربي الطازج',
                description_en: 'Fresh hummus with tahini, oil and parsley, served with fresh Arabic bread',
                price: 25.00,
                category_id: '1',
                is_available: true,
                is_featured: false,
                preparation_time: 5,
                calories: 180,
                ingredients: ['حمص', 'طحينة', 'زيت زيتون', 'ليمون', 'ثوم', 'بقدونس'],
                allergens: ['سمسم'],
                tags: ['نباتي', 'صحي'],
                images: [
                    {
                        id: '1',
                        url: 'https://images.unsplash.com/photo-1571197119282-7c4e2b8b8d4a?w=400',
                        alt: 'حمص بالطحينة',
                        is_primary: true
                    }
                ],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '2',
                name: 'متبل باذنجان',
                name_en: 'Baba Ganoush',
                description: 'باذنجان مشوي مع الطحينة والثوم والليمون، طعم مدخن رائع',
                description_en: 'Grilled eggplant with tahini, garlic and lemon, amazing smoky flavor',
                price: 30.00,
                category_id: '1',
                is_available: true,
                is_featured: false,
                preparation_time: 10,
                calories: 150,
                ingredients: ['باذنجان', 'طحينة', 'ثوم', 'ليمون', 'زيت زيتون'],
                allergens: ['سمسم'],
                tags: ['نباتي', 'مشوي'],
                images: [
                    {
                        id: '2',
                        url: 'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=400',
                        alt: 'متبل باذنجان',
                        is_primary: true
                    }
                ],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '3',
                name: 'كباب لحم مشوي',
                name_en: 'Grilled Meat Kebab',
                description: 'كباب لحم بقري مشوي على الفحم مع الأرز الأبيض والسلطة الخضراء',
                description_en: 'Charcoal grilled beef kebab with white rice and green salad',
                price: 85.00,
                category_id: '2',
                is_available: true,
                is_featured: true,
                preparation_time: 20,
                calories: 450,
                ingredients: ['لحم بقري', 'أرز أبيض', 'خضروات مشكلة', 'بصل', 'بهارات مشكلة'],
                allergens: [],
                tags: ['مشوي', 'بروتين', 'مميز'],
                images: [
                    {
                        id: '3',
                        url: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=400',
                        alt: 'كباب لحم مشوي',
                        is_primary: true
                    }
                ],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '4',
                name: 'فراخ مشوية',
                name_en: 'Grilled Chicken',
                description: 'فراخ مشوية بالأعشاب والتوابل مع البطاطس المحمرة والخضروات المشوية',
                description_en: 'Herb and spice grilled chicken with roasted potatoes and grilled vegetables',
                price: 75.00,
                category_id: '2',
                is_available: true,
                is_featured: true,
                preparation_time: 25,
                calories: 380,
                ingredients: ['دجاج طازج', 'بطاطس', 'جزر', 'فلفل ألوان', 'أعشاب طبيعية'],
                allergens: [],
                tags: ['مشوي', 'صحي', 'مميز'],
                images: [
                    {
                        id: '4',
                        url: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?w=400',
                        alt: 'فراخ مشوية',
                        is_primary: true
                    }
                ],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '5',
                name: 'عصير برتقال طازج',
                name_en: 'Fresh Orange Juice',
                description: 'عصير برتقال طبيعي 100% بدون إضافات، غني بفيتامين سي',
                description_en: '100% natural orange juice with no additives, rich in vitamin C',
                price: 20.00,
                category_id: '3',
                is_available: true,
                is_featured: false,
                preparation_time: 3,
                calories: 110,
                ingredients: ['برتقال طازج'],
                allergens: [],
                tags: ['طبيعي', 'فيتامين سي', 'منعش'],
                images: [
                    {
                        id: '5',
                        url: 'https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=400',
                        alt: 'عصير برتقال طازج',
                        is_primary: true
                    }
                ],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '6',
                name: 'شاي بالنعناع',
                name_en: 'Mint Tea',
                description: 'شاي أحمر مع النعناع الطازج والسكر، مشروب تقليدي منعش',
                description_en: 'Black tea with fresh mint and sugar, traditional refreshing drink',
                price: 15.00,
                category_id: '3',
                is_available: true,
                is_featured: false,
                preparation_time: 5,
                calories: 25,
                ingredients: ['شاي أحمر', 'نعناع طازج', 'سكر أبيض'],
                allergens: [],
                tags: ['ساخن', 'منعش', 'تقليدي'],
                images: [
                    {
                        id: '6',
                        url: 'https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=400',
                        alt: 'شاي بالنعناع',
                        is_primary: true
                    }
                ],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '7',
                name: 'كنافة بالجبن',
                name_en: 'Cheese Kunafa',
                description: 'كنافة طازجة محشوة بالجبن الأبيض ومحلاة بالقطر، تُقدم ساخنة',
                description_en: 'Fresh kunafa stuffed with white cheese and sweetened with syrup, served hot',
                price: 40.00,
                category_id: '4',
                is_available: true,
                is_featured: true,
                preparation_time: 15,
                calories: 320,
                ingredients: ['عجينة كنافة', 'جبن أبيض', 'قطر', 'فستق حلبي', 'سمن بلدي'],
                allergens: ['جلوتين', 'ألبان', 'مكسرات'],
                tags: ['حلو', 'تقليدي', 'ساخن'],
                images: [
                    {
                        id: '7',
                        url: 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400',
                        alt: 'كنافة بالجبن',
                        is_primary: true
                    }
                ],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: '8',
                name: 'مهلبية',
                name_en: 'Muhallabia',
                description: 'مهلبية كريمية بالحليب الطازج مزينة بالفستق وماء الورد',
                description_en: 'Creamy milk pudding with fresh milk, garnished with pistachios and rose water',
                price: 25.00,
                category_id: '4',
                is_available: true,
                is_featured: false,
                preparation_time: 10,
                calories: 180,
                ingredients: ['حليب طازج', 'نشا', 'سكر', 'فستق', 'ماء ورد'],
                allergens: ['ألبان', 'مكسرات'],
                tags: ['كريمي', 'بارد', 'خفيف'],
                images: [
                    {
                        id: '8',
                        url: 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=400',
                        alt: 'مهلبية',
                        is_primary: true
                    }
                ],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            }
        ];

        // Save to localStorage
        localStorage.setItem('restaurant_categories', JSON.stringify(categories));
        localStorage.setItem('restaurant_products', JSON.stringify(products));

        // Setup admin users
        const adminUsers = [
            {
                id: '1',
                email: '<EMAIL>',
                username: 'admin',
                password: 'admin123', // In real app, this would be hashed
                role: 'admin',
                permissions: ['all'],
                created_at: new Date().toISOString()
            },
            {
                id: '2',
                email: '<EMAIL>',
                username: 'محمد',
                password: 'alashrafi2024', // In real app, this would be hashed
                role: 'owner',
                permissions: ['all'],
                created_at: new Date().toISOString()
            }
        ];

        localStorage.setItem('admin_users', JSON.stringify(adminUsers));

        // Setup restaurant settings
        const settings = {
            restaurant_name: 'مطعم محمد الاشرافي',
            restaurant_name_en: 'Mohamed Al-Ashrafi Restaurant',
            phone: '+201014840269',
            whatsapp: '+201014840269',
            address: 'القاهرة، مصر',
            address_en: 'Cairo, Egypt',
            working_hours: {
                open: '10:00',
                close: '24:00',
                days: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة']
            },
            delivery_fee: 15,
            min_order: 50,
            currency: 'جنيه',
            currency_en: 'EGP',
            updated_at: new Date().toISOString()
        };

        localStorage.setItem('restaurant_settings', JSON.stringify(settings));

        console.log('Initial data setup completed');
    }

    // Get data methods
    getCategories() {
        const data = localStorage.getItem('restaurant_categories');
        return data ? JSON.parse(data) : [];
    }

    getProducts() {
        const data = localStorage.getItem('restaurant_products');
        return data ? JSON.parse(data) : [];
    }

    getSettings() {
        const data = localStorage.getItem('restaurant_settings');
        return data ? JSON.parse(data) : {};
    }

    // Save data methods
    saveCategories(categories) {
        localStorage.setItem('restaurant_categories', JSON.stringify(categories));
    }

    saveProducts(products) {
        localStorage.setItem('restaurant_products', JSON.stringify(products));
    }

    saveSettings(settings) {
        localStorage.setItem('restaurant_settings', JSON.stringify(settings));
    }

    // Clear all data
    clearAllData() {
        localStorage.removeItem('restaurant_categories');
        localStorage.removeItem('restaurant_products');
        localStorage.removeItem('restaurant_settings');
        localStorage.removeItem('admin_users');
        console.log('All data cleared');
    }
}

// Initialize database setup
const databaseSetup = new DatabaseSetup();

// Make available globally
window.databaseSetup = databaseSetup;

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DatabaseSetup;
}
