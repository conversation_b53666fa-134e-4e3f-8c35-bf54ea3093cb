// Real-Time Product Manager for Admin Panel
// مدير المنتجات في الوقت الفعلي للوحة الإدارة

class RealTimeProductManager {
    constructor() {
        this.products = [];
        this.categories = [];
        this.supabase = null;
        this.storageManager = null;
        this.isInitialized = false;
        
        console.log('🚀 RealTimeProductManager initializing...');
        this.init();
    }

    async init() {
        try {
            // Wait for dependencies
            await this.waitForDependencies();
            
            // Load initial data
            await this.loadData();
            
            // Setup event listeners
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ RealTimeProductManager initialized');
            
        } catch (error) {
            console.error('❌ Error initializing RealTimeProductManager:', error);
        }
    }

    async waitForDependencies() {
        return new Promise((resolve) => {
            const checkDependencies = () => {
                if (window.supabaseManager && window.supabaseStorageManager) {
                    this.supabase = window.supabaseManager;
                    this.storageManager = window.supabaseStorageManager;
                    resolve();
                } else {
                    setTimeout(checkDependencies, 100);
                }
            };
            checkDependencies();
        });
    }

    async loadData() {
        await this.loadCategories();
        await this.loadProducts();
    }

    async loadCategories() {
        try {
            if (this.supabase && this.supabase.isInitialized) {
                const result = await this.supabase.getCategories();
                if (result.success) {
                    this.categories = result.data;
                    return;
                }
            }
            
            // Fallback categories
            this.categories = [
                { id: '1', name: 'المقبلات', name_en: 'Appetizers', is_active: true },
                { id: '2', name: 'الأطباق الرئيسية', name_en: 'Main Dishes', is_active: true },
                { id: '3', name: 'المشروبات', name_en: 'Beverages', is_active: true },
                { id: '4', name: 'الحلويات', name_en: 'Desserts', is_active: true }
            ];
            
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    async loadProducts() {
        try {
            // Try Supabase first
            if (this.supabase && this.supabase.isInitialized) {
                const result = await this.supabase.getProducts();
                if (result.success) {
                    this.products = result.data;
                    this.saveToLocalStorage();
                    return;
                }
            }
            
            // Fallback to localStorage
            const localProducts = localStorage.getItem('restaurant_products');
            if (localProducts) {
                this.products = JSON.parse(localProducts);
            } else {
                this.products = [];
            }
            
        } catch (error) {
            console.error('Error loading products:', error);
            this.products = [];
        }
    }

    setupEventListeners() {
        // Listen for form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.id === 'productForm') {
                e.preventDefault();
                this.handleProductSubmit(e.target);
            }
        });

        // Listen for delete buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('delete-product-btn')) {
                const productId = e.target.dataset.productId;
                this.deleteProduct(productId);
            }
        });
    }

    async handleProductSubmit(form) {
        try {
            const formData = new FormData(form);
            const productData = this.extractProductData(formData);
            
            // Handle image uploads
            const imageFiles = form.querySelector('#productImages').files;
            if (imageFiles.length > 0) {
                productData.images = await this.uploadImages(imageFiles);
                productData.image_url = productData.images[0]?.url || null;
            }
            
            const productId = form.dataset.productId;
            
            if (productId) {
                // Update existing product
                await this.updateProduct(productId, productData);
            } else {
                // Create new product
                await this.createProduct(productData);
            }
            
            // Close modal and refresh
            this.closeProductModal();
            this.refreshProductList();
            
        } catch (error) {
            console.error('Error saving product:', error);
            this.showMessage('خطأ في حفظ المنتج', 'error');
        }
    }

    extractProductData(formData) {
        return {
            name: formData.get('name'),
            name_en: formData.get('name_en') || formData.get('name'),
            description: formData.get('description'),
            description_en: formData.get('description_en') || formData.get('description'),
            price: parseFloat(formData.get('price')) || 0,
            category_id: formData.get('category_id'),
            is_available: formData.get('is_available') === 'on',
            is_featured: formData.get('is_featured') === 'on',
            icon: formData.get('icon') || 'fas fa-utensils',
            sort_order: parseInt(formData.get('sort_order')) || 0
        };
    }

    async uploadImages(imageFiles) {
        const uploadedImages = [];
        
        for (let i = 0; i < imageFiles.length; i++) {
            const file = imageFiles[i];
            
            try {
                const result = await this.storageManager.uploadImage(file);
                if (result.success) {
                    uploadedImages.push({
                        url: result.data.url,
                        path: result.data.path,
                        name: result.data.name,
                        isPrimary: i === 0 // First image is primary
                    });
                }
            } catch (error) {
                console.error('Error uploading image:', error);
            }
        }
        
        return uploadedImages;
    }

    async createProduct(productData) {
        try {
            // Generate ID
            const newProduct = {
                id: Date.now().toString(),
                ...productData,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            // Save to Supabase if available
            if (this.supabase && this.supabase.isInitialized) {
                const result = await this.supabase.createProduct(newProduct);
                if (result.success) {
                    newProduct.id = result.data.id;
                }
            }

            // Add to local array
            this.products.unshift(newProduct);
            this.saveToLocalStorage();
            
            // Notify main site
            this.notifyMainSite('productAdded', newProduct);
            
            this.showMessage('تم إضافة المنتج بنجاح', 'success');
            
        } catch (error) {
            console.error('Error creating product:', error);
            throw error;
        }
    }

    async updateProduct(productId, productData) {
        try {
            const updatedProduct = {
                ...productData,
                id: productId,
                updated_at: new Date().toISOString()
            };

            // Update in Supabase if available
            if (this.supabase && this.supabase.isInitialized) {
                await this.supabase.updateProduct(productId, updatedProduct);
            }

            // Update in local array
            const index = this.products.findIndex(p => p.id === productId);
            if (index !== -1) {
                this.products[index] = { ...this.products[index], ...updatedProduct };
                this.saveToLocalStorage();
            }
            
            // Notify main site
            this.notifyMainSite('productUpdated', updatedProduct);
            
            this.showMessage('تم تحديث المنتج بنجاح', 'success');
            
        } catch (error) {
            console.error('Error updating product:', error);
            throw error;
        }
    }

    async deleteProduct(productId) {
        if (!confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            return;
        }

        try {
            // Delete from Supabase if available
            if (this.supabase && this.supabase.isInitialized) {
                await this.supabase.deleteProduct(productId);
            }

            // Remove from local array
            this.products = this.products.filter(p => p.id !== productId);
            this.saveToLocalStorage();
            
            // Notify main site
            this.notifyMainSite('productDeleted', { id: productId });
            
            this.refreshProductList();
            this.showMessage('تم حذف المنتج بنجاح', 'success');
            
        } catch (error) {
            console.error('Error deleting product:', error);
            this.showMessage('خطأ في حذف المنتج', 'error');
        }
    }

    notifyMainSite(eventType, data) {
        // Dispatch custom event for real-time updates
        const event = new CustomEvent(eventType, { detail: data });
        window.dispatchEvent(event);
        
        // Also trigger storage event for cross-tab sync
        localStorage.setItem('restaurant_products', JSON.stringify(this.products));
    }

    saveToLocalStorage() {
        try {
            localStorage.setItem('restaurant_products', JSON.stringify(this.products));
        } catch (error) {
            console.error('Error saving to localStorage:', error);
        }
    }

    refreshProductList() {
        // Trigger refresh of product list in admin interface
        const event = new CustomEvent('refreshProductList');
        window.dispatchEvent(event);
    }

    closeProductModal() {
        const modal = document.getElementById('productModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    showMessage(message, type) {
        // Create and show message
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type}`;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10000;
            background: ${type === 'success' ? '#d4edda' : '#f8d7da'};
            color: ${type === 'success' ? '#155724' : '#721c24'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : '#f5c6cb'};
        `;
        messageDiv.textContent = message;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }

    // Public API
    getProducts() {
        return this.products;
    }

    getCategories() {
        return this.categories;
    }

    getProductById(id) {
        return this.products.find(p => p.id === id);
    }
}

// Initialize and make available globally
const realTimeProductManager = new RealTimeProductManager();
window.realTimeProductManager = realTimeProductManager;

console.log('✅ RealTimeProductManager loaded');
